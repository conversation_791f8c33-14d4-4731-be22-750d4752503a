@extends('layouts.auth')

@section('title', 'Login')

@section('content')
    <div class="text-center mb-4">
        <h3 class="text-dark mb-2">Welcome Back!</h3>
        <p class="text-muted">Sign in to your account to continue</p>
    </div>

    <!-- Session Status -->
    @if (session('status'))
        <div class="alert alert-success mb-4">
            {{ session('status') }}
        </div>
    @endif

    <form method="POST" action="{{ route('login') }}">
        @csrf

        <!-- Email Address -->
        <div class="mb-3">
            <label for="email" class="form-label text-dark fw-medium">{{ __('Email Address') }}</label>
            <input id="email" type="email" class="form-control @error('email') is-invalid @enderror"
                   name="email" value="{{ old('email') }}" required autofocus autocomplete="username"
                   placeholder="Enter your email address">
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- Password -->
        <div class="mb-3">
            <label for="password" class="form-label text-dark fw-medium">{{ __('Password') }}</label>
            <input id="password" type="password" class="form-control @error('password') is-invalid @enderror"
                   name="password" required autocomplete="current-password"
                   placeholder="Enter your password">
            @error('password')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- Remember Me -->
        <div class="mb-4">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="remember_me" name="remember">
                <label class="form-check-label text-muted" for="remember_me">
                    {{ __('Remember me') }}
                </label>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="d-grid mb-4">
            <button type="submit" class="btn btn-auth">
                <i class="fas fa-sign-in-alt me-2"></i>{{ __('Sign In') }}
            </button>
        </div>

        <!-- Links -->
        <div class="text-center">
            @if (Route::has('password.request'))
                <p class="mb-2">
                    <a href="{{ route('password.request') }}" class="auth-link">
                        <i class="fas fa-key me-1"></i>{{ __('Forgot your password?') }}
                    </a>
                </p>
            @endif

            @if (Route::has('register'))
                <p class="mb-0">
                    {{ __("Don't have an account?") }}
                    <a href="{{ route('register') }}" class="auth-link fw-medium">
                        {{ __('Create Account') }}
                    </a>
                </p>
            @endif
        </div>
    </form>
@endsection
