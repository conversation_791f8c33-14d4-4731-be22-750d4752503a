<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('pages.home');
    })->name('home');

Route::get('/about', function () {
    return view('pages.about');
    })->name('about');


Route::get('/services', function () {
    return view('pages.services');
    })->name('services');


Route::get('/booking', function () {
    return view('pages.booking');
    })->name('booking');


Route::get('/contact', function () {
    return view('pages.contact');
    })->name('contact');


Route::get('/testimonials', function () {
    return view('pages.testimonials');
    })->name('testimonials');

Route::get('/technicals', function () {
    return view('pages.technicals');
    })->name('technicals');
// Dashboard routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

    // Live Sessions
    Route::prefix('dashboard/live-sessions')->name('dashboard.live-sessions.')->group(function () {
        Route::get('/', [App\Http\Controllers\LiveSessionController::class, 'index'])->name('index');
        Route::get('/{session}', [App\Http\Controllers\LiveSessionController::class, 'show'])->name('show');
        Route::post('/{session}/register', [App\Http\Controllers\LiveSessionController::class, 'register'])->name('register');
        Route::delete('/{session}/unregister', [App\Http\Controllers\LiveSessionController::class, 'unregister'])->name('unregister');
        Route::get('/{session}/join', [App\Http\Controllers\LiveSessionController::class, 'join'])->name('join');
        Route::post('/{session}/attendance', [App\Http\Controllers\LiveSessionController::class, 'markAttendance'])->name('attendance');
        Route::post('/{session}/feedback', [App\Http\Controllers\LiveSessionController::class, 'submitFeedback'])->name('feedback');
    });

    // Courses
    Route::prefix('dashboard/courses')->name('dashboard.courses.')->group(function () {
        Route::get('/', [App\Http\Controllers\CourseController::class, 'index'])->name('index');
        Route::get('/{course}', [App\Http\Controllers\CourseController::class, 'show'])->name('show');
        Route::post('/{course}/enroll', [App\Http\Controllers\CourseController::class, 'enroll'])->name('enroll');
        Route::get('/{course}/start', [App\Http\Controllers\CourseController::class, 'start'])->name('start');
        Route::get('/{course}/continue', [App\Http\Controllers\CourseController::class, 'continue'])->name('continue');
        Route::post('/{course}/progress', [App\Http\Controllers\CourseController::class, 'updateProgress'])->name('progress');
        Route::post('/{course}/module', [App\Http\Controllers\CourseController::class, 'completeModule'])->name('module');
        Route::delete('/{course}/unenroll', [App\Http\Controllers\CourseController::class, 'unenroll'])->name('unenroll');
    });

    // Certificates
    Route::prefix('dashboard/certificates')->name('dashboard.certificates.')->group(function () {
        Route::get('/', [App\Http\Controllers\CertificateController::class, 'index'])->name('index');
        Route::get('/cpd', [App\Http\Controllers\CertificateController::class, 'cpd'])->name('cpd');
        Route::get('/report', [App\Http\Controllers\CertificateController::class, 'generateBulkReport'])->name('report');
        Route::get('/{certificate}', [App\Http\Controllers\CertificateController::class, 'show'])->name('show');
        Route::get('/{certificate}/download', [App\Http\Controllers\CertificateController::class, 'download'])->name('download');
        Route::get('/{certificate}/share', [App\Http\Controllers\CertificateController::class, 'share'])->name('share');
    });

    // Notifications
    Route::prefix('dashboard/notifications')->name('dashboard.notifications.')->group(function () {
        Route::get('/', [App\Http\Controllers\NotificationController::class, 'index'])->name('index');
        Route::get('/preferences', [App\Http\Controllers\NotificationController::class, 'preferences'])->name('preferences');
        Route::post('/preferences', [App\Http\Controllers\NotificationController::class, 'updatePreferences'])->name('preferences.update');
        Route::post('/bulk-action', [App\Http\Controllers\NotificationController::class, 'bulkAction'])->name('bulk-action');
        Route::post('/{notification}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('read');
        Route::post('/read-all', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('read-all');
        Route::delete('/{notification}', [App\Http\Controllers\NotificationController::class, 'destroy'])->name('destroy');
    });

    // Support
    Route::prefix('dashboard/support')->name('dashboard.support.')->group(function () {
        Route::get('/', [App\Http\Controllers\SupportController::class, 'index'])->name('index');
        Route::get('/help', [App\Http\Controllers\SupportController::class, 'help'])->name('help');
        Route::get('/search', [App\Http\Controllers\SupportController::class, 'searchKnowledgeBase'])->name('search');
        Route::get('/contact', [App\Http\Controllers\SupportController::class, 'contact'])->name('contact');
        Route::post('/contact', [App\Http\Controllers\SupportController::class, 'submitTicket'])->name('contact.submit');
        Route::get('/tickets', [App\Http\Controllers\SupportController::class, 'tickets'])->name('tickets');
        Route::get('/tickets/{ticket}', [App\Http\Controllers\SupportController::class, 'showTicket'])->name('tickets.show');
        Route::post('/tickets/{ticket}/reply', [App\Http\Controllers\SupportController::class, 'addReply'])->name('tickets.reply');
        Route::post('/tickets/{ticket}/close', [App\Http\Controllers\SupportController::class, 'closeTicket'])->name('tickets.close');
        Route::post('/faq/{faq}/rate', [App\Http\Controllers\SupportController::class, 'rateFaq'])->name('faq.rate');
    });

    // Billing
    Route::prefix('dashboard/billing')->name('dashboard.billing.')->group(function () {
        Route::get('/', [App\Http\Controllers\BillingController::class, 'index'])->name('index');
        Route::get('/history', [App\Http\Controllers\BillingController::class, 'billingHistory'])->name('history');
        Route::get('/invoice/{invoice}/download', [App\Http\Controllers\BillingController::class, 'downloadInvoice'])->name('invoice.download');
        Route::get('/invoices/download-all', [App\Http\Controllers\BillingController::class, 'downloadAllInvoices'])->name('invoices.download-all');
        Route::post('/payment-method', [App\Http\Controllers\BillingController::class, 'updatePaymentMethod'])->name('payment-method.update');
        Route::post('/cancel', [App\Http\Controllers\BillingController::class, 'cancelSubscription'])->name('cancel');
        Route::post('/change-plan', [App\Http\Controllers\BillingController::class, 'changePlan'])->name('change-plan');
    });
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Public certificate verification
Route::get('/certificate/verify/{certificateNumber}', [App\Http\Controllers\CertificateController::class, 'verify'])->name('certificate.verify');

// Admin routes with proper middleware
Route::middleware(['auth', 'verified', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Content Management
    Route::prefix('content')->name('content.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ContentController::class, 'index'])->name('index');

        // Carousel Management
        Route::prefix('carousel')->name('carousel.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\ContentController::class, 'carouselIndex'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\ContentController::class, 'carouselCreate'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\ContentController::class, 'carouselStore'])->name('store');
            Route::get('/{slide}/edit', [App\Http\Controllers\Admin\ContentController::class, 'carouselEdit'])->name('edit');
            Route::put('/{slide}', [App\Http\Controllers\Admin\ContentController::class, 'carouselUpdate'])->name('update');
            Route::delete('/{slide}', [App\Http\Controllers\Admin\ContentController::class, 'carouselDestroy'])->name('destroy');
        });

        // Services Management
        Route::prefix('services')->name('services.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\ContentController::class, 'servicesIndex'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\ContentController::class, 'servicesCreate'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\ContentController::class, 'servicesStore'])->name('store');
            Route::get('/{service}/edit', [App\Http\Controllers\Admin\ContentController::class, 'servicesEdit'])->name('edit');
            Route::put('/{service}', [App\Http\Controllers\Admin\ContentController::class, 'servicesUpdate'])->name('update');
            Route::delete('/{service}', [App\Http\Controllers\Admin\ContentController::class, 'servicesDestroy'])->name('destroy');
        });

        // Team Management
        Route::prefix('team')->name('team.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\ContentController::class, 'teamIndex'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\ContentController::class, 'teamCreate'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\ContentController::class, 'teamStore'])->name('store');
            Route::get('/{member}/edit', [App\Http\Controllers\Admin\ContentController::class, 'teamEdit'])->name('edit');
            Route::put('/{member}', [App\Http\Controllers\Admin\ContentController::class, 'teamUpdate'])->name('update');
            Route::delete('/{member}', [App\Http\Controllers\Admin\ContentController::class, 'teamDestroy'])->name('destroy');
        });

        // Testimonials Management
        Route::prefix('testimonials')->name('testimonials.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\ContentController::class, 'testimonialsIndex'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\ContentController::class, 'testimonialsCreate'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\ContentController::class, 'testimonialsStore'])->name('store');
            Route::get('/{testimonial}/edit', [App\Http\Controllers\Admin\ContentController::class, 'testimonialsEdit'])->name('edit');
            Route::put('/{testimonial}', [App\Http\Controllers\Admin\ContentController::class, 'testimonialsUpdate'])->name('update');
            Route::delete('/{testimonial}', [App\Http\Controllers\Admin\ContentController::class, 'testimonialsDestroy'])->name('destroy');
        });

        // Bulk actions and utilities
        Route::post('/bulk-action', [App\Http\Controllers\Admin\ContentController::class, 'bulkAction'])->name('bulk-action');
        Route::post('/reorder', [App\Http\Controllers\Admin\ContentController::class, 'reorder'])->name('reorder');
    });

    // Media Management
    Route::prefix('media')->name('media.')->group(function () {
        Route::post('/upload', [App\Http\Controllers\Admin\MediaController::class, 'upload'])->name('upload');
        Route::get('/browse', [App\Http\Controllers\Admin\MediaController::class, 'browse'])->name('browse');
        Route::delete('/delete', [App\Http\Controllers\Admin\MediaController::class, 'delete'])->name('delete');
        Route::get('/info', [App\Http\Controllers\Admin\MediaController::class, 'info'])->name('info');
    });

    // User Management
    Route::resource('users', App\Http\Controllers\Admin\UserManagementController::class);
    Route::patch('/users/{user}/toggle-status', [App\Http\Controllers\Admin\UserManagementController::class, 'toggleStatus'])->name('users.toggle-status');

    // Live Session Management
    Route::resource('sessions', App\Http\Controllers\Admin\LiveSessionManagementController::class);
    Route::patch('/sessions/{session}/start', [App\Http\Controllers\Admin\LiveSessionManagementController::class, 'start'])->name('sessions.start');
    Route::patch('/sessions/{session}/end', [App\Http\Controllers\Admin\LiveSessionManagementController::class, 'end'])->name('sessions.end');
    Route::get('/sessions/{session}/analytics', [App\Http\Controllers\Admin\LiveSessionManagementController::class, 'analytics'])->name('sessions.analytics');

    // Analytics & Reports
    Route::get('/analytics', [App\Http\Controllers\Admin\AnalyticsController::class, 'index'])->name('analytics.index');
    Route::get('/analytics/export', [App\Http\Controllers\Admin\AnalyticsController::class, 'export'])->name('analytics.export');

    // System Settings
    Route::get('/settings', [App\Http\Controllers\Admin\SystemSettingsController::class, 'index'])->name('settings.index');
    Route::put('/settings', [App\Http\Controllers\Admin\SystemSettingsController::class, 'update'])->name('settings.update');
    Route::post('/settings/clear-cache', [App\Http\Controllers\Admin\SystemSettingsController::class, 'clearCache'])->name('settings.clear-cache');
    Route::post('/settings/optimize', [App\Http\Controllers\Admin\SystemSettingsController::class, 'optimize'])->name('settings.optimize');
    Route::post('/settings/toggle-maintenance', [App\Http\Controllers\Admin\SystemSettingsController::class, 'toggleMaintenance'])->name('settings.toggle-maintenance');

    // Zoom Test Route (remove in production)
    Route::get('/test-zoom', function () {
        $zoomService = app(\App\Services\ZoomService::class);

        try {
            $connected = $zoomService->testConnection();
            $userAccount = $zoomService->getUserAccount();

            return response()->json([
                'connected' => $connected,
                'account' => $userAccount,
                'message' => $connected ? 'Zoom connection successful!' : 'Zoom connection failed!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'connected' => false,
                'error' => $e->getMessage(),
                'message' => 'Zoom connection failed!'
            ], 500);
        }
    })->name('test-zoom');
});

require __DIR__.'/auth.php';
