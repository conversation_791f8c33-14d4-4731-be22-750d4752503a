<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Service>
 */
class ServiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $services = [
            [
                'title' => 'Professional Training',
                'description' => 'Comprehensive healthcare training programs designed by industry experts to advance your career.',
                'icon' => 'fas fa-graduation-cap',
            ],
            [
                'title' => 'Certification Programs',
                'description' => 'Earn recognized certifications that meet industry standards and boost your professional credentials.',
                'icon' => 'fas fa-certificate',
            ],
            [
                'title' => 'Live Interactive Sessions',
                'description' => 'Join live sessions with expert instructors for real-time learning and immediate feedback.',
                'icon' => 'fas fa-video',
            ],
            [
                'title' => 'CPD Credit Tracking',
                'description' => 'Automatically track your Continuing Professional Development credits and maintain compliance.',
                'icon' => 'fas fa-clock',
            ],
            [
                'title' => 'Mobile Learning',
                'description' => 'Access your courses anytime, anywhere with our mobile-optimized learning platform.',
                'icon' => 'fas fa-mobile-alt',
            ],
            [
                'title' => '24/7 Support',
                'description' => 'Get help when you need it with our comprehensive support system and knowledge base.',
                'icon' => 'fas fa-headset',
            ],
        ];

        $serviceData = $this->faker->randomElement($services);

        return [
            'title' => $serviceData['title'],
            'description' => $serviceData['description'],
            'icon' => $serviceData['icon'],
            'image' => $this->faker->optional(0.7)->passthrough('services/service-' . $this->faker->numberBetween(1, 6) . '.jpg'),
            'order' => $this->faker->numberBetween(1, 10),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'created_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the service is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the service is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the service has an image.
     */
    public function withImage(): static
    {
        return $this->state(fn (array $attributes) => [
            'image' => 'services/service-' . $this->faker->numberBetween(1, 6) . '.jpg',
        ]);
    }
}