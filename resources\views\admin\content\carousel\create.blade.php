@extends('layouts.dashboard')

@section('page-title', 'Create Carousel Slide')

@section('content')
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Create Carousel Slide</h2>
                    <p class="text-muted mb-0">Add a new slide to the homepage carousel</p>
                </div>
                <div>
                    <a href="{{ route('admin.content.carousel.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Carousel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <form action="{{ route('admin.content.carousel.store') }}" method="POST" enctype="multipart/form-data" id="carouselForm">
                @csrf
                
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-edit me-2 text-primary"></i>Slide Content</h5>
                    </div>
                    <div class="card-body">
                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Subtitle -->
                        <div class="mb-3">
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                                   id="subtitle" name="subtitle" value="{{ old('subtitle') }}">
                            @error('subtitle')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description with Rich Text Editor -->
                        <x-rich-text-editor
                            name="description"
                            label="Description"
                            :required="true"
                            :value="old('description')"
                            toolbar="default"
                            :height="300"
                            help="Use the rich text editor to format your content with bold, italic, links, images, and more."
                        />
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-image me-2 text-primary"></i>Slide Image</h5>
                    </div>
                    <div class="card-body">
                        <!-- Image Upload -->
                        <div class="mb-3">
                            <label for="image" class="form-label">Slide Image <span class="text-danger">*</span></label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*" required>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Upload an image (JPEG, PNG, GIF). Recommended size: 1920x800px</div>
                        </div>

                        <!-- Image Preview -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <img id="previewImg" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-mouse-pointer me-2 text-primary"></i>Call-to-Action Buttons</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Primary Button -->
                            <div class="col-md-6">
                                <h6 class="text-primary">Primary Button</h6>
                                <div class="mb-3">
                                    <label for="primary_button_text" class="form-label">Button Text</label>
                                    <input type="text" class="form-control @error('primary_button_text') is-invalid @enderror" 
                                           id="primary_button_text" name="primary_button_text" value="{{ old('primary_button_text') }}">
                                    @error('primary_button_text')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-3">
                                    <label for="primary_button_link" class="form-label">Button Link</label>
                                    <input type="url" class="form-control @error('primary_button_link') is-invalid @enderror" 
                                           id="primary_button_link" name="primary_button_link" value="{{ old('primary_button_link') }}"
                                           placeholder="https://example.com">
                                    @error('primary_button_link')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Secondary Button -->
                            <div class="col-md-6">
                                <h6 class="text-secondary">Secondary Button</h6>
                                <div class="mb-3">
                                    <label for="secondary_button_text" class="form-label">Button Text</label>
                                    <input type="text" class="form-control @error('secondary_button_text') is-invalid @enderror" 
                                           id="secondary_button_text" name="secondary_button_text" value="{{ old('secondary_button_text') }}">
                                    @error('secondary_button_text')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-3">
                                    <label for="secondary_button_link" class="form-label">Button Link</label>
                                    <input type="url" class="form-control @error('secondary_button_link') is-invalid @enderror" 
                                           id="secondary_button_link" name="secondary_button_link" value="{{ old('secondary_button_link') }}"
                                           placeholder="https://example.com">
                                    @error('secondary_button_link')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-cog me-2 text-primary"></i>Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Order -->
                                <div class="mb-3">
                                    <label for="order" class="form-label">Display Order <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                           id="order" name="order" value="{{ old('order', 0) }}" min="0" required>
                                    @error('order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Lower numbers appear first</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Status -->
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">Status</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active (slide will be visible on the website)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.content.carousel.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Cancel
                    </a>
                    <div>
                        <button type="submit" name="action" value="save" class="btn btn-outline-primary me-2">
                            <i class="fas fa-save me-1"></i>Save Draft
                        </button>
                        <button type="submit" name="action" value="publish" class="btn btn-primary">
                            <i class="fas fa-check me-1"></i>Save & Publish
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Preview Panel -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm sticky-top" style="top: 2rem;">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-eye me-2 text-primary"></i>Live Preview</h5>
                </div>
                <div class="card-body">
                    <div id="slidePreview" class="border rounded p-3 bg-light">
                        <div class="text-center text-muted">
                            <i class="fas fa-image fa-3x mb-3"></i>
                            <p>Fill out the form to see a preview of your slide</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {

    // Image preview
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('previewImg').src = e.target.result;
                document.getElementById('imagePreview').style.display = 'block';
                updatePreview();
            };
            reader.readAsDataURL(file);
        }
    });

    // Live preview update
    function updatePreview() {
        const title = document.getElementById('title').value;
        const subtitle = document.getElementById('subtitle').value;
        const description = window.getRichTextContent ? window.getRichTextContent('description') : document.querySelector('[name="description"]').value;
        const primaryBtn = document.getElementById('primary_button_text').value;
        const secondaryBtn = document.getElementById('secondary_button_text').value;
        const previewImg = document.getElementById('previewImg').src;

        let previewHtml = '';
        
        if (previewImg && previewImg !== window.location.href) {
            previewHtml += `<img src="${previewImg}" class="img-fluid rounded mb-3" style="max-height: 150px; width: 100%; object-fit: cover;">`;
        }
        
        if (title) {
            previewHtml += `<h5 class="text-primary">${title}</h5>`;
        }
        
        if (subtitle) {
            previewHtml += `<p class="text-muted small">${subtitle}</p>`;
        }
        
        if (description) {
            previewHtml += `<div class="small">${description}</div>`;
        }
        
        if (primaryBtn || secondaryBtn) {
            previewHtml += '<div class="mt-3">';
            if (primaryBtn) {
                previewHtml += `<span class="badge bg-primary me-1">${primaryBtn}</span>`;
            }
            if (secondaryBtn) {
                previewHtml += `<span class="badge bg-outline-secondary">${secondaryBtn}</span>`;
            }
            previewHtml += '</div>';
        }

        if (!previewHtml) {
            previewHtml = `
                <div class="text-center text-muted">
                    <i class="fas fa-image fa-3x mb-3"></i>
                    <p>Fill out the form to see a preview of your slide</p>
                </div>
            `;
        }

        document.getElementById('slidePreview').innerHTML = previewHtml;
    }

    // Form field listeners for live preview
    ['title', 'subtitle', 'primary_button_text', 'secondary_button_text'].forEach(id => {
        document.getElementById(id).addEventListener('input', updatePreview);
    });

    // Listen for rich text editor changes
    document.addEventListener('richTextAutoSave', function(e) {
        if (e.detail.editorId.includes('description')) {
            updatePreview();
        }
    });

    // Form submission handling
    document.getElementById('carouselForm').addEventListener('submit', function(e) {
        // Sync TinyMCE content
        tinymce.triggerSave();
        
        const action = e.submitter.value;
        if (action === 'save') {
            document.getElementById('is_active').checked = false;
        } else if (action === 'publish') {
            document.getElementById('is_active').checked = true;
        }
    });
});
</script>
@endpush
@endsection
