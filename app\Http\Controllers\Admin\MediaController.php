<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class MediaController extends Controller
{
    /**
     * Upload media files for rich text editor
     */
    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:jpeg,png,jpg,gif,svg,pdf,doc,docx,xls,xlsx,ppt,pptx|max:10240', // 10MB max
        ]);

        try {
            $file = $request->file('file');
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $mimeType = $file->getMimeType();
            
            // Generate unique filename
            $filename = time() . '_' . Str::random(10) . '.' . $extension;
            
            // Determine storage directory based on file type
            $directory = $this->getStorageDirectory($mimeType);
            
            // Store the file
            $path = $file->storeAs($directory, $filename, 'public');
            
            // Get file URL
            $url = Storage::url($path);
            
            // Return response based on file type
            if (Str::startsWith($mimeType, 'image/')) {
                return response()->json([
                    'success' => true,
                    'location' => $url,
                    'file' => [
                        'name' => $originalName,
                        'size' => $file->getSize(),
                        'type' => $mimeType,
                        'url' => $url,
                        'path' => $path,
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'file' => [
                        'name' => $originalName,
                        'size' => $file->getSize(),
                        'type' => $mimeType,
                        'url' => $url,
                        'path' => $path,
                    ]
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to upload file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get media library for file browser
     */
    public function browse(Request $request)
    {
        $type = $request->get('type', 'all'); // all, images, documents
        $page = $request->get('page', 1);
        $perPage = 20;
        
        try {
            $files = [];
            $directories = ['media/images', 'media/documents', 'media/videos'];
            
            foreach ($directories as $directory) {
                if (Storage::disk('public')->exists($directory)) {
                    $directoryFiles = Storage::disk('public')->files($directory);
                    
                    foreach ($directoryFiles as $file) {
                        $mimeType = Storage::disk('public')->mimeType($file);
                        $shouldInclude = false;
                        
                        switch ($type) {
                            case 'images':
                                $shouldInclude = Str::startsWith($mimeType, 'image/');
                                break;
                            case 'documents':
                                $shouldInclude = !Str::startsWith($mimeType, 'image/') && !Str::startsWith($mimeType, 'video/');
                                break;
                            case 'videos':
                                $shouldInclude = Str::startsWith($mimeType, 'video/');
                                break;
                            default:
                                $shouldInclude = true;
                        }
                        
                        if ($shouldInclude) {
                            $files[] = [
                                'name' => basename($file),
                                'path' => $file,
                                'url' => Storage::url($file),
                                'size' => Storage::disk('public')->size($file),
                                'type' => $mimeType,
                                'modified' => Storage::disk('public')->lastModified($file),
                                'is_image' => Str::startsWith($mimeType, 'image/'),
                            ];
                        }
                    }
                }
            }
            
            // Sort by modification date (newest first)
            usort($files, function($a, $b) {
                return $b['modified'] - $a['modified'];
            });
            
            // Paginate results
            $total = count($files);
            $offset = ($page - 1) * $perPage;
            $files = array_slice($files, $offset, $perPage);
            
            return response()->json([
                'success' => true,
                'files' => $files,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $perPage),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to browse files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete media file
     */
    public function delete(Request $request)
    {
        $request->validate([
            'path' => 'required|string',
        ]);

        try {
            $path = $request->path;
            
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
                
                return response()->json([
                    'success' => true,
                    'message' => 'File deleted successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'File not found'
                ], 404);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to delete file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get file information
     */
    public function info(Request $request)
    {
        $request->validate([
            'path' => 'required|string',
        ]);

        try {
            $path = $request->path;
            
            if (Storage::disk('public')->exists($path)) {
                $file = [
                    'name' => basename($path),
                    'path' => $path,
                    'url' => Storage::url($path),
                    'size' => Storage::disk('public')->size($path),
                    'type' => Storage::disk('public')->mimeType($path),
                    'modified' => Storage::disk('public')->lastModified($path),
                    'is_image' => Str::startsWith(Storage::disk('public')->mimeType($path), 'image/'),
                ];
                
                return response()->json([
                    'success' => true,
                    'file' => $file
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => 'File not found'
                ], 404);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get file info: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Determine storage directory based on MIME type
     */
    private function getStorageDirectory($mimeType)
    {
        if (Str::startsWith($mimeType, 'image/')) {
            return 'media/images';
        } elseif (Str::startsWith($mimeType, 'video/')) {
            return 'media/videos';
        } else {
            return 'media/documents';
        }
    }

    /**
     * Format file size for display
     */
    private function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
