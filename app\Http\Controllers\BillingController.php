<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BillingController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        // Mock billing data - in a real app, this would come from a payment provider
        $stats = [
            'current_plan' => 'Professional',
            'monthly_cost' => 29.99,
            'next_billing_date' => now()->addMonth(),
            'total_spent' => 359.88,
            'billing_cycle' => 'monthly',
            'subscription_status' => 'active',
        ];

        $invoices = [
            [
                'id' => 'VCH-202412-001',
                'date' => now()->subMonth(),
                'amount' => 29.99,
                'status' => 'paid',
                'description' => 'Professional Plan - Monthly Subscription',
                'download_url' => '#',
            ],
            [
                'id' => 'VCH-202411-001',
                'date' => now()->subMonths(2),
                'amount' => 29.99,
                'status' => 'paid',
                'description' => 'Professional Plan - Monthly Subscription',
                'download_url' => '#',
            ],
            [
                'id' => 'VCH-202410-001',
                'date' => now()->subMonths(3),
                'amount' => 29.99,
                'status' => 'paid',
                'description' => 'Professional Plan - Monthly Subscription',
                'download_url' => '#',
            ],
            [
                'id' => 'VCH-202409-001',
                'date' => now()->subMonths(4),
                'amount' => 29.99,
                'status' => 'paid',
                'description' => 'Professional Plan - Monthly Subscription',
                'download_url' => '#',
            ],
        ];

        $paymentMethods = [
            [
                'id' => 1,
                'type' => 'card',
                'last_four' => '4242',
                'brand' => 'Visa',
                'expires' => '12/26',
                'is_default' => true,
            ]
        ];

        $plans = [
            [
                'name' => 'Basic',
                'price' => 19.99,
                'billing_cycle' => 'monthly',
                'features' => [
                    '5 Courses per month',
                    'Basic support',
                    'Certificate downloads',
                    'Mobile access'
                ],
                'is_current' => false,
                'is_popular' => false,
            ],
            [
                'name' => 'Professional',
                'price' => 29.99,
                'billing_cycle' => 'monthly',
                'features' => [
                    'Unlimited courses',
                    'Priority support',
                    'All certificates',
                    'Live session access',
                    'CPD tracking',
                    'Advanced analytics'
                ],
                'is_current' => true,
                'is_popular' => true,
            ],
            [
                'name' => 'Enterprise',
                'price' => 49.99,
                'billing_cycle' => 'monthly',
                'features' => [
                    'Everything in Professional',
                    'Team management',
                    'Custom branding',
                    'API access',
                    'Dedicated support',
                    'Custom integrations'
                ],
                'is_current' => false,
                'is_popular' => false,
            ],
        ];

        return view('dashboard.billing.index', compact('stats', 'invoices', 'paymentMethods', 'plans'));
    }

    public function downloadInvoice($invoiceId)
    {
        // In a real implementation, this would generate and download the actual invoice PDF
        return redirect()->back()->with('info', 'Invoice download functionality would be implemented here.');
    }

    public function updatePaymentMethod(Request $request)
    {
        $request->validate([
            'card_number' => 'required|string',
            'expiry_month' => 'required|integer|min:1|max:12',
            'expiry_year' => 'required|integer|min:' . date('Y'),
            'cvv' => 'required|string|size:3',
            'cardholder_name' => 'required|string|max:255',
        ]);

        // In a real implementation, this would integrate with a payment processor
        return redirect()->back()->with('success', 'Payment method updated successfully.');
    }

    public function cancelSubscription(Request $request)
    {
        $request->validate([
            'cancellation_reason' => 'nullable|string|max:500',
        ]);

        // In a real implementation, this would cancel the subscription with the payment processor
        return redirect()->back()->with('success', 'Subscription cancellation request submitted. Your subscription will remain active until the end of the current billing period.');
    }

    public function changePlan(Request $request)
    {
        $request->validate([
            'plan' => 'required|string|in:basic,professional,enterprise',
        ]);

        // In a real implementation, this would change the subscription plan
        return redirect()->back()->with('success', 'Plan change request submitted. Changes will take effect on your next billing cycle.');
    }

    public function downloadAllInvoices()
    {
        // In a real implementation, this would generate a ZIP file with all invoices
        return redirect()->back()->with('info', 'Bulk invoice download functionality would be implemented here.');
    }

    public function billingHistory(Request $request)
    {
        $year = $request->get('year', now()->year);
        
        // Mock data for billing history
        $invoices = collect([
            [
                'id' => 'VCH-202412-001',
                'date' => now()->subMonth(),
                'amount' => 29.99,
                'status' => 'paid',
                'description' => 'Professional Plan - Monthly Subscription',
            ],
            [
                'id' => 'VCH-202411-001',
                'date' => now()->subMonths(2),
                'amount' => 29.99,
                'status' => 'paid',
                'description' => 'Professional Plan - Monthly Subscription',
            ],
            [
                'id' => 'VCH-202410-001',
                'date' => now()->subMonths(3),
                'amount' => 29.99,
                'status' => 'paid',
                'description' => 'Professional Plan - Monthly Subscription',
            ],
        ])->filter(function ($invoice) use ($year) {
            return $invoice['date']->year == $year;
        });

        $availableYears = [2024, 2023, 2022];

        return view('dashboard.billing.history', compact('invoices', 'year', 'availableYears'));
    }
}
