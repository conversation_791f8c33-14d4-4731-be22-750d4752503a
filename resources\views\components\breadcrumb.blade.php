@props([
    'items' => [],
    'separator' => '/',
    'showHome' => true,
    'homeText' => 'Dashboard',
    'homeRoute' => 'dashboard',
    'class' => '',
])

@php
    $breadcrumbItems = [];
    
    // Add home/dashboard item if enabled
    if ($showHome) {
        $breadcrumbItems[] = [
            'text' => $homeText,
            'url' => route($homeRoute),
            'active' => false
        ];
    }
    
    // Process provided items
    foreach ($items as $index => $item) {
        $isLast = $index === count($items) - 1;
        
        if (is_string($item)) {
            // Simple string item (active by default if last)
            $breadcrumbItems[] = [
                'text' => $item,
                'url' => null,
                'active' => $isLast
            ];
        } elseif (is_array($item)) {
            // Array item with text and optional url
            $breadcrumbItems[] = [
                'text' => $item['text'] ?? $item['title'] ?? $item[0] ?? '',
                'url' => $item['url'] ?? $item['route'] ?? $item[1] ?? null,
                'active' => $item['active'] ?? $isLast
            ];
        }
    }
@endphp

<nav aria-label="breadcrumb" class="{{ $class }}">
    <ol class="breadcrumb breadcrumb-custom mb-0">
        @foreach($breadcrumbItems as $index => $item)
            @if($item['active'])
                <li class="breadcrumb-item active" aria-current="page">
                    <span>{{ $item['text'] }}</span>
                </li>
            @else
                <li class="breadcrumb-item">
                    @if($item['url'])
                        <a href="{{ $item['url'] }}" class="text-decoration-none">
                            {{ $item['text'] }}
                        </a>
                    @else
                        <span>{{ $item['text'] }}</span>
                    @endif
                </li>
            @endif
        @endforeach
    </ol>
</nav>

@once
@push('styles')
<style>
.breadcrumb-custom {
    background: none;
    padding: 0;
    margin: 0;
    font-size: 0.9rem;
}

.breadcrumb-custom .breadcrumb-item {
    color: #6c757d;
}

.breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
    content: var(--bs-breadcrumb-divider, "/");
    color: #adb5bd;
    margin: 0 0.5rem;
}

.breadcrumb-custom .breadcrumb-item.active {
    color: var(--primary, #2E8B57);
    font-weight: 600;
}

.breadcrumb-custom .breadcrumb-item a {
    color: #6c757d;
    transition: color 0.2s ease;
}

.breadcrumb-custom .breadcrumb-item a:hover {
    color: var(--primary, #2E8B57);
}

@media (max-width: 576px) {
    .breadcrumb-custom {
        font-size: 0.8rem;
    }
    
    .breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
        margin: 0 0.3rem;
    }
}
</style>
@endpush
@endonce
