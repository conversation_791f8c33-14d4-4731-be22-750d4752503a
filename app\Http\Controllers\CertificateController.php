<?php

namespace App\Http\Controllers;

use App\Models\Certificate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class CertificateController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $filter = $request->get('filter', 'all');
        $type = $request->get('type');

        $query = Certificate::where('user_id', $user->id);

        // Apply filters
        switch ($filter) {
            case 'active':
                $query->active();
                break;
            case 'expired':
                $query->expired();
                break;
        }

        if ($type) {
            $query->byType($type);
        }

        $certificates = $query->with(['course', 'liveSession'])
            ->orderBy('issued_at', 'desc')
            ->paginate(12);

        // Get filter options
        $types = Certificate::where('user_id', $user->id)
            ->distinct()
            ->pluck('certificate_type');

        // Get user statistics
        $stats = [
            'total_certificates' => Certificate::where('user_id', $user->id)->count(),
            'active_certificates' => Certificate::where('user_id', $user->id)->active()->count(),
            'expired_certificates' => Certificate::where('user_id', $user->id)->expired()->count(),
            'total_cpd_credits' => Certificate::where('user_id', $user->id)
                ->active()
                ->sum('cpd_credits'),
        ];

        return view('dashboard.certificates.index', compact(
            'certificates',
            'stats',
            'filter',
            'type',
            'types'
        ));
    }

    public function show(Certificate $certificate)
    {
        // Check if user owns this certificate
        if ($certificate->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to certificate.');
        }

        return view('dashboard.certificates.show', compact('certificate'));
    }

    public function download(Certificate $certificate)
    {
        // Check if user owns this certificate
        if ($certificate->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to certificate.');
        }

        if (!$certificate->certificate_file) {
            // Generate certificate PDF if it doesn't exist
            $this->generateCertificatePDF($certificate);
        }

        if ($certificate->certificate_file && Storage::exists($certificate->certificate_file)) {
            return Storage::download(
                $certificate->certificate_file,
                $certificate->certificate_number . '.pdf'
            );
        }

        return redirect()->back()->with('error', 'Certificate file not found.');
    }

    public function verify($certificateNumber)
    {
        $certificate = Certificate::where('certificate_number', $certificateNumber)
            ->with(['user', 'course', 'liveSession'])
            ->first();

        if (!$certificate) {
            return view('dashboard.certificates.verify', [
                'certificate' => null,
                'message' => 'Certificate not found or invalid certificate number.'
            ]);
        }

        return view('dashboard.certificates.verify', compact('certificate'));
    }

    public function share(Certificate $certificate)
    {
        // Check if user owns this certificate
        if ($certificate->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to certificate.');
        }

        $shareUrl = route('certificate.verify', $certificate->certificate_number);

        return view('dashboard.certificates.share', compact('certificate', 'shareUrl'));
    }

    private function generateCertificatePDF(Certificate $certificate)
    {
        $fileName = 'certificates/' . $certificate->certificate_number . '.pdf';

        // Generate HTML content for the certificate
        $html = $this->generateCertificateHTML($certificate);

        // For now, we'll store the HTML content as a file
        // In a real implementation, you would use a library like DomPDF or TCPDF
        Storage::put($fileName, $html);

        $certificate->update(['certificate_file' => $fileName]);

        return $fileName;
    }

    private function generateCertificateHTML(Certificate $certificate)
    {
        $user = $certificate->user;
        $courseName = $certificate->course ? $certificate->course->title : $certificate->liveSession->title;
        $instructor = $certificate->course ? $certificate->course->instructor : $certificate->liveSession->instructor;

        return view('certificates.template', compact('certificate', 'user', 'courseName', 'instructor'))->render();
    }

    public function generateBulkReport(Request $request)
    {
        $user = Auth::user();
        $year = $request->get('year', now()->year);
        $format = $request->get('format', 'pdf');

        $certificates = Certificate::where('user_id', $user->id)
            ->active()
            ->whereYear('issued_at', $year)
            ->with(['course', 'liveSession'])
            ->orderBy('issued_at', 'desc')
            ->get();

        if ($format === 'csv') {
            return $this->generateCSVReport($certificates, $year);
        }

        return $this->generatePDFReport($certificates, $year);
    }

    private function generateCSVReport($certificates, $year)
    {
        $filename = "cpd-report-{$year}.csv";
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($certificates) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Certificate Number',
                'Title',
                'Type',
                'CPD Credits',
                'Issue Date',
                'Instructor',
                'Status'
            ]);

            foreach ($certificates as $certificate) {
                fputcsv($file, [
                    $certificate->certificate_number,
                    $certificate->title,
                    ucfirst($certificate->certificate_type),
                    $certificate->cpd_credits,
                    $certificate->issued_at->format('Y-m-d'),
                    $certificate->course ? $certificate->course->instructor : $certificate->liveSession->instructor,
                    ucfirst($certificate->status)
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function generatePDFReport($certificates, $year)
    {
        $user = Auth::user();
        $totalCredits = $certificates->sum('cpd_credits');

        $html = view('certificates.cpd-report', compact('certificates', 'user', 'year', 'totalCredits'))->render();

        $filename = "cpd-report-{$year}.pdf";

        // For now, return HTML. In real implementation, convert to PDF
        return response($html)
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }

    public function cpd(Request $request)
    {
        $user = Auth::user();
        $year = $request->get('year', now()->year);

        // Get certificates for the specified year
        $certificates = Certificate::where('user_id', $user->id)
            ->active()
            ->whereYear('issued_at', $year)
            ->with(['course', 'liveSession'])
            ->orderBy('issued_at', 'desc')
            ->get();

        // Calculate CPD credits by category/type
        $cpdByType = $certificates->groupBy('certificate_type')
            ->map(function ($group) {
                return $group->sum('cpd_credits');
            });

        $cpdByMonth = $certificates->groupBy(function ($certificate) {
            return $certificate->issued_at->format('Y-m');
        })->map(function ($group) {
            return $group->sum('cpd_credits');
        });

        // Get available years
        $availableYears = Certificate::where('user_id', $user->id)
            ->selectRaw('YEAR(issued_at) as year')
            ->distinct()
            ->orderBy('year', 'desc')
            ->pluck('year');

        $stats = [
            'total_credits_year' => $certificates->sum('cpd_credits'),
            'total_certificates_year' => $certificates->count(),
            'course_credits' => $cpdByType->get('course', 0),
            'session_credits' => $cpdByType->get('session', 0),
        ];

        return view('dashboard.certificates.cpd', compact(
            'certificates',
            'stats',
            'year',
            'availableYears',
            'cpdByType',
            'cpdByMonth'
        ));
    }
}
