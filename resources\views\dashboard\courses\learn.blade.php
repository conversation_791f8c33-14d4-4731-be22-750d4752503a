@extends('layouts.dashboard')

@section('page-title', 'Learning: ' . $course->title)

@section('content')
<div class="dashboard-content-wrapper">
    <!-- Course Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-gradient-primary text-white" style="background: linear-gradient(135deg, var(--medical-green), var(--medical-blue)) !important;">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <h2 class="mb-2">{{ $course->title }}</h2>
                            <p class="mb-2 opacity-75">{{ $course->description }}</p>
                            <div class="d-flex align-items-center">
                                <span class="me-3">
                                    <i class="fas fa-user me-1"></i>{{ $course->instructor }}
                                </span>
                                <span class="me-3">
                                    <i class="fas fa-clock me-1"></i>{{ $course->duration_hours }}h
                                </span>
                                <span>
                                    <i class="fas fa-certificate me-1"></i>{{ $course->cpd_credits }} CPD Credits
                                </span>
                            </div>
                        </div>
                        <div class="col-lg-4 text-end">
                            <div class="mb-2">
                                <small class="opacity-75">Progress</small>
                                <h4 class="mb-0">{{ $enrollment->progress_percentage }}%</h4>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-white" style="width: {{ $enrollment->progress_percentage }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Content -->
    <div class="row">
        <!-- Course Modules -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-book-open me-2 text-primary"></i>Course Modules</h5>
                </div>
                <div class="card-body">
                    @php
                        $modules = [
                            ['id' => 'module-1', 'title' => 'Introduction and Overview', 'duration' => '30 min', 'type' => 'video'],
                            ['id' => 'module-2', 'title' => 'Core Concepts', 'duration' => '45 min', 'type' => 'reading'],
                            ['id' => 'module-3', 'title' => 'Practical Applications', 'duration' => '60 min', 'type' => 'interactive'],
                            ['id' => 'module-4', 'title' => 'Case Studies', 'duration' => '40 min', 'type' => 'case_study'],
                            ['id' => 'module-5', 'title' => 'Assessment', 'duration' => '20 min', 'type' => 'quiz'],
                        ];
                        $moduleProgress = $enrollment->progress_data['modules'] ?? [];
                    @endphp

                    @foreach($modules as $index => $module)
                        @php
                            $isCompleted = isset($moduleProgress[$module['id']]) && $moduleProgress[$module['id']]['progress'] >= 100;
                            $isUnlocked = $index === 0 || (isset($moduleProgress[$modules[$index-1]['id']]) && $moduleProgress[$modules[$index-1]['id']]['progress'] >= 100);
                        @endphp
                        
                        <div class="module-item border rounded p-3 mb-3 {{ $isCompleted ? 'border-success' : ($isUnlocked ? 'border-primary' : 'border-secondary') }}">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center">
                                        <div class="module-icon me-3">
                                            @if($module['type'] === 'video')
                                                <i class="fas fa-play-circle fa-2x {{ $isCompleted ? 'text-success' : ($isUnlocked ? 'text-primary' : 'text-muted') }}"></i>
                                            @elseif($module['type'] === 'reading')
                                                <i class="fas fa-book fa-2x {{ $isCompleted ? 'text-success' : ($isUnlocked ? 'text-primary' : 'text-muted') }}"></i>
                                            @elseif($module['type'] === 'interactive')
                                                <i class="fas fa-laptop fa-2x {{ $isCompleted ? 'text-success' : ($isUnlocked ? 'text-primary' : 'text-muted') }}"></i>
                                            @elseif($module['type'] === 'case_study')
                                                <i class="fas fa-file-medical fa-2x {{ $isCompleted ? 'text-success' : ($isUnlocked ? 'text-primary' : 'text-muted') }}"></i>
                                            @else
                                                <i class="fas fa-question-circle fa-2x {{ $isCompleted ? 'text-success' : ($isUnlocked ? 'text-primary' : 'text-muted') }}"></i>
                                            @endif
                                        </div>
                                        <div>
                                            <h6 class="mb-1 {{ $isUnlocked ? '' : 'text-muted' }}">{{ $module['title'] }}</h6>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>{{ $module['duration'] }}
                                                @if($isCompleted)
                                                    <span class="badge bg-success ms-2">Completed</span>
                                                @elseif(!$isUnlocked)
                                                    <span class="badge bg-secondary ms-2">Locked</span>
                                                @endif
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    @if($isCompleted)
                                        <button class="btn btn-success btn-sm" disabled>
                                            <i class="fas fa-check me-1"></i>Completed
                                        </button>
                                    @elseif($isUnlocked)
                                        <button class="btn btn-primary btn-sm start-module" data-module="{{ $module['id'] }}">
                                            <i class="fas fa-play me-1"></i>Start
                                        </button>
                                    @else
                                        <button class="btn btn-secondary btn-sm" disabled>
                                            <i class="fas fa-lock me-1"></i>Locked
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Course Sidebar -->
        <div class="col-lg-4">
            <!-- Progress Summary -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h6 class="mb-0"><i class="fas fa-chart-pie me-2 text-primary"></i>Progress Summary</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-semibold">Overall Progress</span>
                            <span class="text-primary fw-bold">{{ $enrollment->progress_percentage }}%</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-primary" style="width: {{ $enrollment->progress_percentage }}%"></div>
                        </div>
                    </div>

                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h5 class="text-primary mb-0">{{ count($moduleProgress) }}</h5>
                                <small class="text-muted">Modules Started</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h5 class="text-success mb-0">{{ collect($moduleProgress)->where('progress', 100)->count() }}</h5>
                            <small class="text-muted">Modules Completed</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Course Info -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2 text-primary"></i>Course Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted d-block">Instructor</small>
                        <strong>{{ $course->instructor }}</strong>
                        @if($course->instructor_title)
                            <br><small class="text-muted">{{ $course->instructor_title }}</small>
                        @endif
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted d-block">Category</small>
                        <span class="badge bg-primary">{{ ucfirst($course->category) }}</span>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted d-block">Level</small>
                        <span class="badge bg-info">{{ ucfirst($course->level) }}</span>
                    </div>

                    @if($course->learning_objectives)
                        <div class="mb-0">
                            <small class="text-muted d-block mb-2">Learning Objectives</small>
                            <ul class="list-unstyled small">
                                @foreach($course->learning_objectives as $objective)
                                    <li class="mb-1">
                                        <i class="fas fa-check-circle text-success me-2"></i>{{ $objective }}
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h6 class="mb-0"><i class="fas fa-bolt me-2 text-primary"></i>Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('dashboard.courses.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Courses
                        </a>
                        
                        @if($enrollment->isCompleted())
                            <a href="{{ route('dashboard.certificates.index', ['course' => $course->id]) }}" class="btn btn-success">
                                <i class="fas fa-certificate me-2"></i>View Certificate
                            </a>
                        @endif

                        <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#helpModal">
                            <i class="fas fa-question-circle me-2"></i>Need Help?
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Module Content Modal -->
<div class="modal fade" id="moduleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="moduleModalTitle">Module Content</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="moduleModalBody">
                <!-- Module content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="completeModuleBtn">Mark as Complete</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const moduleModal = new bootstrap.Modal(document.getElementById('moduleModal'));
    let currentModuleId = null;

    // Handle module start
    document.querySelectorAll('.start-module').forEach(button => {
        button.addEventListener('click', function() {
            currentModuleId = this.dataset.module;
            const moduleTitle = this.closest('.module-item').querySelector('h6').textContent;
            
            document.getElementById('moduleModalTitle').textContent = moduleTitle;
            document.getElementById('moduleModalBody').innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-play-circle fa-3x text-primary mb-3"></i>
                    <h5>Starting Module: ${moduleTitle}</h5>
                    <p class="text-muted">This is a demo module. In a real implementation, this would contain the actual course content.</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Click "Mark as Complete" when you finish this module to unlock the next one.
                    </div>
                </div>
            `;
            
            moduleModal.show();
        });
    });

    // Handle module completion
    document.getElementById('completeModuleBtn').addEventListener('click', function() {
        if (!currentModuleId) return;

        fetch(`{{ route('dashboard.courses.module', $course) }}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                module_id: currentModuleId,
                module_progress: 100
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                moduleModal.hide();
                location.reload(); // Refresh to show updated progress
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to update progress. Please try again.');
        });
    });
});
</script>
@endpush
@endsection
