@extends('layouts.dashboard')

@section('page-title', 'Live Sessions')

@section('content')
<div class="dashboard-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Live Sessions</h2>
                    <p class="text-muted mb-0">Join interactive medical education sessions with experts</p>
                </div>
                <div>
                    <a href="{{ route('dashboard.support.contact') }}" class="btn btn-outline-primary">
                        <i class="fas fa-headset me-2"></i>Need Help?
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-calendar-alt text-primary fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-primary mb-1">{{ $stats['upcoming_count'] }}</h3>
                    <p class="text-muted mb-0">Upcoming Sessions</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-user-check text-success fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-success mb-1">{{ $stats['registered_count'] }}</h3>
                    <p class="text-muted mb-0">Registered Sessions</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-video text-info fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-info mb-1">{{ $stats['attended_count'] }}</h3>
                    <p class="text-muted mb-0">Sessions Attended</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-certificate text-warning fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-warning mb-1">{{ $stats['total_credits'] }}</h3>
                    <p class="text-muted mb-0">CPD Credits Earned</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <!-- Filter Tabs -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="btn-group flex-wrap" role="group">
                                <a href="{{ route('dashboard.live-sessions.index', ['filter' => 'upcoming']) }}"
                                   class="btn {{ $filter === 'upcoming' ? 'btn-primary' : 'btn-outline-primary' }}">
                                    <i class="fas fa-calendar-plus me-1"></i>Upcoming
                                </a>
                                <a href="{{ route('dashboard.live-sessions.index', ['filter' => 'live']) }}"
                                   class="btn {{ $filter === 'live' ? 'btn-success' : 'btn-outline-success' }}">
                                    <i class="fas fa-circle me-1"></i>Live Now
                                </a>
                                <a href="{{ route('dashboard.live-sessions.index', ['filter' => 'registered']) }}"
                                   class="btn {{ $filter === 'registered' ? 'btn-info' : 'btn-outline-info' }}">
                                    <i class="fas fa-user-check me-1"></i>My Registrations
                                </a>
                                <a href="{{ route('dashboard.live-sessions.index', ['filter' => 'attended']) }}"
                                   class="btn {{ $filter === 'attended' ? 'btn-warning' : 'btn-outline-warning' }}">
                                    <i class="fas fa-video me-1"></i>Attended
                                </a>
                                <a href="{{ route('dashboard.live-sessions.index', ['filter' => 'completed']) }}"
                                   class="btn {{ $filter === 'completed' ? 'btn-secondary' : 'btn-outline-secondary' }}">
                                    <i class="fas fa-check-circle me-1"></i>Completed
                                </a>
                                <a href="{{ route('dashboard.live-sessions.index', ['filter' => 'all']) }}"
                                   class="btn {{ $filter === 'all' ? 'btn-dark' : 'btn-outline-dark' }}">
                                    <i class="fas fa-list me-1"></i>All Sessions
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Search and Filters -->
                    <div class="row">
                        <div class="col-12">
                            <form method="GET" action="{{ route('dashboard.live-sessions.index') }}" class="row g-3">
                                <input type="hidden" name="filter" value="{{ $filter }}">

                                <div class="col-md-4">
                                    <label for="search" class="form-label">Search Sessions</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" id="search" name="search"
                                               value="{{ $search }}" placeholder="Search by title, instructor...">
                                    </div>
                                </div>

                                <div class="col-md-2">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">All Categories</option>
                                        @foreach($categories as $cat)
                                            <option value="{{ $cat }}" {{ $category === $cat ? 'selected' : '' }}>
                                                {{ ucfirst($cat) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label for="date_from" class="form-label">From Date</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from"
                                           value="{{ $date_from }}">
                                </div>

                                <div class="col-md-2">
                                    <label for="date_to" class="form-label">To Date</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to"
                                           value="{{ $date_to }}">
                                </div>

                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-1"></i>Filter
                                        </button>
                                        <a href="{{ route('dashboard.live-sessions.index', ['filter' => $filter]) }}"
                                           class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <span class="text-muted">
                                <i class="fas fa-filter me-1"></i>
                                Showing {{ $sessions->count() }} of {{ $sessions->total() }} sessions
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sessions List -->
    <div class="row">
        @forelse($sessions as $session)
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h5 class="card-title mb-2">{{ $session->title }}</h5>
                            <p class="text-muted mb-2">{{ Str::limit($session->description, 100) }}</p>
                        </div>
                        <span class="badge bg-{{ $session->status === 'scheduled' ? 'primary' : ($session->status === 'live' ? 'success' : 'secondary') }}">
                            {{ ucfirst($session->status) }}
                        </span>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted d-block">Instructor</small>
                            <strong>{{ $session->instructor }}</strong>
                            @if($session->instructor_title)
                                <br><small class="text-muted">{{ $session->instructor_title }}</small>
                            @endif
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">Date & Time</small>
                            <strong>{{ $session->session_date->format('M j, Y') }}</strong>
                            <br><small class="text-muted">{{ $session->session_date->format('g:i A T') }}</small>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted d-block">Duration</small>
                            <strong>{{ $session->duration_minutes }} minutes</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">CPD Credits</small>
                            <strong>{{ $session->cpd_credits }} credits</strong>
                        </div>
                    </div>

                    @if($session->max_participants)
                    <div class="mb-3">
                        <small class="text-muted d-block">Availability</small>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar" style="width: {{ ($session->registration_count / $session->max_participants) * 100 }}%"></div>
                        </div>
                        <small class="text-muted">{{ $session->registration_count }}/{{ $session->max_participants }} registered</small>
                    </div>
                    @endif

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            @php
                                $userRegistration = $session->registrations->first();
                            @endphp

                            @if($userRegistration)
                                @if($userRegistration->status === 'registered')
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Registered
                                    </span>
                                @elseif($userRegistration->status === 'attended')
                                    <span class="badge bg-info">
                                        <i class="fas fa-video me-1"></i>Attended
                                    </span>
                                @endif
                            @endif
                        </div>

                        <div class="btn-group">
                            <a href="{{ route('dashboard.live-sessions.show', $session) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>

                            @if(!$userRegistration && $session->status === 'scheduled' && !$session->isFullyBooked())
                                <form action="{{ route('dashboard.live-sessions.register', $session) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-user-plus me-1"></i>Register
                                    </button>
                                </form>
                            @elseif($userRegistration && $userRegistration->status === 'registered' && $session->status === 'scheduled')
                                @if($session->session_date->diffInMinutes(now()) <= 15 && $session->session_date->isFuture())
                                    <a href="{{ route('dashboard.live-sessions.join', $session) }}" class="btn btn-success btn-sm">
                                        <i class="fas fa-video me-1"></i>Join Session
                                    </a>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @empty
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-video fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No sessions found</h4>
                    <p class="text-muted mb-4">
                        @if($filter === 'upcoming')
                            There are no upcoming sessions at the moment.
                        @elseif($filter === 'registered')
                            You haven't registered for any sessions yet.
                        @elseif($filter === 'attended')
                            You haven't attended any sessions yet.
                        @else
                            No sessions match your current filter.
                        @endif
                    </p>
                    <a href="{{ route('dashboard.live-sessions.index', ['filter' => 'upcoming']) }}" class="btn btn-primary">
                        <i class="fas fa-calendar-plus me-2"></i>Browse Upcoming Sessions
                    </a>
                </div>
            </div>
        </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($sessions->hasPages())
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-center">
                {{ $sessions->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh page every 5 minutes for live session updates
    setTimeout(function() {
        window.location.reload();
    }, 300000); // 5 minutes
});
</script>
@endpush
