<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate of Completion</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .certificate {
            background: white;
            border: 8px solid #2E8B57;
            border-radius: 20px;
            padding: 60px;
            max-width: 800px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .certificate::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(46,139,87,0.05) 0%, transparent 70%);
            z-index: 0;
        }
        
        .certificate-content {
            position: relative;
            z-index: 1;
        }
        
        .header {
            margin-bottom: 40px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #2E8B57, #4A90E2);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        .title {
            font-size: 36px;
            color: #2E8B57;
            margin: 0;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 3px;
        }
        
        .subtitle {
            font-size: 18px;
            color: #666;
            margin: 10px 0 0 0;
            font-style: italic;
        }
        
        .recipient-section {
            margin: 50px 0;
        }
        
        .certifies-text {
            font-size: 20px;
            color: #333;
            margin-bottom: 20px;
        }
        
        .recipient-name {
            font-size: 42px;
            color: #2E8B57;
            font-weight: bold;
            margin: 20px 0;
            border-bottom: 3px solid #2E8B57;
            display: inline-block;
            padding-bottom: 10px;
        }
        
        .completion-text {
            font-size: 20px;
            color: #333;
            margin: 30px 0 20px 0;
        }
        
        .course-name {
            font-size: 28px;
            color: #4A90E2;
            font-weight: bold;
            margin: 20px 0;
            font-style: italic;
        }
        
        .details-section {
            margin: 40px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .detail-item {
            text-align: center;
            margin: 10px;
        }
        
        .detail-label {
            font-size: 14px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }
        
        .detail-value {
            font-size: 18px;
            color: #333;
            font-weight: bold;
        }
        
        .footer {
            margin-top: 50px;
            border-top: 2px solid #eee;
            padding-top: 30px;
        }
        
        .signature-section {
            display: flex;
            justify-content: space-between;
            align-items: end;
            margin-top: 40px;
        }
        
        .signature {
            text-align: center;
            width: 200px;
        }
        
        .signature-line {
            border-bottom: 2px solid #333;
            margin-bottom: 10px;
            height: 40px;
        }
        
        .signature-label {
            font-size: 14px;
            color: #666;
        }
        
        .certificate-number {
            font-size: 12px;
            color: #999;
            margin-top: 20px;
        }
        
        .verification-info {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .certificate {
                border: 4px solid #2E8B57;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="certificate">
        <div class="certificate-content">
            <div class="header">
                <div class="logo">VCH</div>
                <h1 class="title">Certificate of Completion</h1>
                <p class="subtitle">Virtual CME Hub - Continuing Medical Education</p>
            </div>
            
            <div class="recipient-section">
                <p class="certifies-text">This is to certify that</p>
                <h2 class="recipient-name">{{ $user->name }}</h2>
                <p class="completion-text">has successfully completed the {{ $certificate->certificate_type === 'course' ? 'course' : 'live session' }}</p>
                <h3 class="course-name">"{{ $courseName }}"</h3>
            </div>
            
            <div class="details-section">
                <div class="detail-item">
                    <div class="detail-label">CPD Credits Earned</div>
                    <div class="detail-value">{{ $certificate->cpd_credits }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">Date of Completion</div>
                    <div class="detail-value">{{ $certificate->issued_at->format('F j, Y') }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">Instructor</div>
                    <div class="detail-value">{{ $instructor }}</div>
                </div>
            </div>
            
            <div class="footer">
                <div class="signature-section">
                    <div class="signature">
                        <div class="signature-line"></div>
                        <div class="signature-label">Medical Director</div>
                    </div>
                    
                    <div class="signature">
                        <div class="signature-line"></div>
                        <div class="signature-label">Program Coordinator</div>
                    </div>
                </div>
                
                <div class="certificate-number">
                    Certificate Number: {{ $certificate->certificate_number }}
                </div>
                
                <div class="verification-info">
                    Verify this certificate at: {{ route('certificate.verify', $certificate->certificate_number) }}
                </div>
            </div>
        </div>
    </div>
</body>
</html>
