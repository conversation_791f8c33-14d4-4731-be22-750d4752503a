<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Course>
 */
class CourseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            'Medical Training',
            'Healthcare Management',
            'Clinical Skills',
            'Patient Care',
            'Emergency Medicine',
            'Nursing Education',
            'Medical Technology',
            'Healthcare Ethics',
            'Public Health',
            'Medical Research'
        ];

        $levels = ['Beginner', 'Intermediate', 'Advanced'];
        $statuses = ['draft', 'published', 'archived'];

        $title = $this->faker->randomElement([
            'Advanced Cardiac Life Support',
            'Pediatric Emergency Medicine',
            'Healthcare Quality Management',
            'Clinical Documentation Excellence',
            'Patient Safety Fundamentals',
            'Medical Ethics and Law',
            'Infection Control Protocols',
            'Emergency Response Training',
            'Healthcare Leadership',
            'Medical Device Training',
            'Pharmaceutical Management',
            'Healthcare Communication',
            'Clinical Research Methods',
            'Healthcare Technology',
            'Patient Care Excellence'
        ]);

        $description = $this->faker->paragraphs(3, true);
        $duration = $this->faker->numberBetween(30, 480); // 30 minutes to 8 hours
        $price = $this->faker->randomElement([0, 29.99, 49.99, 79.99, 99.99, 149.99, 199.99]);

        return [
            'title' => $title,
            'slug' => \Illuminate\Support\Str::slug($title),
            'description' => $description,
            'short_description' => $this->faker->sentence(20),
            'category' => $this->faker->randomElement($categories),
            'level' => $this->faker->randomElement($levels),
            'duration_minutes' => $duration,
            'price' => $price,
            'is_free' => $price == 0,
            'max_participants' => $this->faker->randomElement([null, 20, 50, 100, 200]),
            'prerequisites' => $this->faker->optional(0.3)->sentence(10),
            'learning_objectives' => json_encode([
                $this->faker->sentence(8),
                $this->faker->sentence(8),
                $this->faker->sentence(8),
                $this->faker->sentence(8),
            ]),
            'course_outline' => json_encode([
                'Module 1: ' . $this->faker->sentence(5),
                'Module 2: ' . $this->faker->sentence(5),
                'Module 3: ' . $this->faker->sentence(5),
                'Module 4: ' . $this->faker->sentence(5),
            ]),
            'instructor_name' => $this->faker->name(),
            'instructor_bio' => $this->faker->paragraph(2),
            'instructor_credentials' => $this->faker->optional(0.7)->randomElement([
                'MD, PhD',
                'RN, MSN',
                'MD, FACP',
                'PharmD, RPh',
                'RN, BSN, CEN',
                'MD, FACEP',
                'MSN, RN, CNE'
            ]),
            'featured_image' => 'courses/course-' . $this->faker->numberBetween(1, 10) . '.jpg',
            'video_preview_url' => $this->faker->optional(0.5)->url(),
            'certificate_template' => $this->faker->optional(0.8)->randomElement([
                'template_standard.pdf',
                'template_advanced.pdf',
                'template_professional.pdf'
            ]),
            'cpd_credits' => $this->faker->randomFloat(1, 0.5, 8.0),
            'status' => $this->faker->randomElement($statuses),
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'enrollment_start' => $this->faker->optional(0.3)->dateTimeBetween('-30 days', '+30 days'),
            'enrollment_end' => $this->faker->optional(0.3)->dateTimeBetween('+30 days', '+90 days'),
            'course_start' => $this->faker->optional(0.5)->dateTimeBetween('-15 days', '+45 days'),
            'course_end' => $this->faker->optional(0.5)->dateTimeBetween('+45 days', '+120 days'),
            'created_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the course is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'published',
        ]);
    }

    /**
     * Indicate that the course is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the course is free.
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => 0,
            'is_free' => true,
        ]);
    }

    /**
     * Indicate that the course is premium.
     */
    public function premium(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => $this->faker->randomElement([99.99, 149.99, 199.99, 299.99]),
            'is_free' => false,
        ]);
    }
}
