<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FAQ>
 */
class FAQFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = ['general', 'courses', 'certification', 'payment', 'technical'];
        
        $faqsByCategory = [
            'general' => [
                [
                    'question' => 'What is Virtual Care Hub?',
                    'answer' => 'Virtual Care Hub is a comprehensive online platform dedicated to healthcare education and training. We offer a wide range of courses, certifications, and resources designed specifically for healthcare professionals seeking to enhance their skills and advance their careers.',
                ],
                [
                    'question' => 'Who can benefit from Virtual Care Hub courses?',
                    'answer' => 'Our courses are designed for healthcare professionals at all levels, including doctors, nurses, paramedics, healthcare administrators, and allied health professionals. We offer specialized training for various medical specialties and healthcare roles.',
                ],
            ],
            'courses' => [
                [
                    'question' => 'How long do I have access to a course after purchase?',
                    'answer' => 'Once you purchase a course, you will have access to all course materials for 12 months. For certification courses, you will have access to the preparation materials until you complete your certification exam.',
                ],
                [
                    'question' => 'Are your courses accredited?',
                    'answer' => 'Yes, all our courses are accredited by relevant professional bodies and organizations. Each course page displays specific accreditation details and the number of CPD/CME credits available.',
                ],
            ],
            'certification' => [
                [
                    'question' => 'How do I receive my certificate after completing a course?',
                    'answer' => 'Upon successful completion of a course and passing any required assessments, your certificate will be automatically generated and available for download from your user dashboard. You will also receive a copy via email.',
                ],
                [
                    'question' => 'Are your certifications recognized internationally?',
                    'answer' => 'Many of our certifications are recognized internationally, particularly those aligned with global healthcare standards. Each certification page provides details about recognition and validity across different countries and regions.',
                ],
            ],
            'payment' => [
                [
                    'question' => 'What payment methods do you accept?',
                    'answer' => 'We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers. For institutional purchases, we also offer invoice payment options.',
                ],
                [
                    'question' => 'Do you offer refunds if I\'m not satisfied with a course?',
                    'answer' => 'Yes, we offer a 14-day money-back guarantee for most courses if you\'re not satisfied. Please note that this does not apply to courses where you have already downloaded certificates or completed more than 50% of the content.',
                ],
            ],
            'technical' => [
                [
                    'question' => 'What are the technical requirements for accessing your courses?',
                    'answer' => 'Our platform is accessible on any modern web browser (Chrome, Firefox, Safari, Edge). For optimal experience, we recommend a stable internet connection with at least 5 Mbps download speed. Some courses with video content may require higher speeds.',
                ],
                [
                    'question' => 'Can I access courses on mobile devices?',
                    'answer' => 'Yes, our platform is fully responsive and optimized for mobile devices. We also offer dedicated apps for iOS and Android for an enhanced mobile learning experience.',
                ],
            ],
        ];
        
        $category = $this->faker->randomElement($categories);
        $faqData = $this->faker->randomElement($faqsByCategory[$category]);
        
        return [
            'question' => $faqData['question'],
            'answer' => $faqData['answer'],
            'category' => $category,
            'order' => $this->faker->numberBetween(1, 10),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'views' => $this->faker->numberBetween(0, 1000),
            'helpful_votes' => $this->faker->numberBetween(0, 100),
            'created_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the FAQ is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the FAQ is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Set the FAQ category.
     */
    public function category(string $category): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => $category,
        ]);
    }

    /**
     * Indicate that the FAQ is popular.
     */
    public function popular(): static
    {
        return $this->state(fn (array $attributes) => [
            'views' => $this->faker->numberBetween(500, 2000),
            'helpful_votes' => $this->faker->numberBetween(50, 200),
        ]);
    }
}