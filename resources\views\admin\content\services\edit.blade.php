@extends('layouts.dashboard')

@section('page-title', 'Edit Service')

@section('content')
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Edit Service</h2>
                    <p class="text-muted mb-0">Update service information</p>
                </div>
                <div>
                    <a href="{{ route('admin.content.services.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Services
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <form action="{{ route('admin.content.services.update', $service->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2 text-primary"></i>Service Information</h5>
                    </div>
                    <div class="card-body">
                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Service Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title', $service->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <x-rich-text-editor 
                                name="description" 
                                label="" 
                                :required="true"
                                :value="old('description', $service->description)"
                                toolbar="full"
                                :height="300"
                                help="Describe the service in detail. Include benefits, features, and any other relevant information."
                            />
                        </div>

                        <!-- Icon -->
                        <div class="mb-3">
                            <label for="icon" class="form-label">Font Awesome Icon <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-icons"></i></span>
                                <input type="text" class="form-control @error('icon') is-invalid @enderror" 
                                       id="icon" name="icon" value="{{ old('icon', $service->icon) }}" 
                                       placeholder="fa-graduation-cap" required>
                            </div>
                            @error('icon')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Enter a Font Awesome icon name (e.g., fa-graduation-cap, fa-stethoscope). <a href="https://fontawesome.com/icons" target="_blank">Browse icons</a></div>
                        </div>

                        <!-- Image -->
                        <div class="mb-3">
                            <label for="image" class="form-label">Service Image</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Upload a new image to replace the current one. Recommended size: 800x600px</div>
                        </div>

                        <!-- Current Image Preview -->
                        <div id="currentImagePreview" class="mt-3">
                            @if($service->image)
                                <div class="d-flex align-items-center">
                                    <img src="{{ asset('storage/' . $service->image) }}" alt="{{ $service->title }}" 
                                         class="img-thumbnail" style="max-height: 150px; max-width: 200px;">
                                    <div class="ms-3">
                                        <p class="mb-1">Current image</p>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image" value="1">
                                            <label class="form-check-label" for="remove_image">Remove current image</label>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <p class="text-muted">No image currently set</p>
                            @endif
                        </div>

                        <!-- New Image Preview -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <p class="mb-1">New image preview:</p>
                            <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-height: 150px; max-width: 200px;">
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-cog me-2 text-primary"></i>Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Order -->
                                <div class="mb-3">
                                    <label for="order" class="form-label">Display Order <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                           id="order" name="order" value="{{ old('order', $service->order) }}" min="0" required>
                                    @error('order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Lower numbers appear first</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Status -->
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">Status</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               value="1" {{ old('is_active', $service->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active (service will be visible on the website)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.content.services.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Cancel
                    </a>
                    <div>
                        <button type="submit" name="action" value="save" class="btn btn-outline-primary me-2">
                            <i class="fas fa-save me-1"></i>Save Draft
                        </button>
                        <button type="submit" name="action" value="publish" class="btn btn-primary">
                            <i class="fas fa-check me-1"></i>Save & Publish
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Preview Panel -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm sticky-top" style="top: 2rem;">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-eye me-2 text-primary"></i>Live Preview</h5>
                </div>
                <div class="card-body">
                    <div id="servicePreview" class="text-center">
                        <div class="mb-4">
                            <div id="previewIconContainer" class="mb-3">
                                <div class="bg-primary rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                                     style="width: 80px; height: 80px;">
                                    <i id="previewIcon" class="{{ $service->icon }} fa-2x text-white"></i>
                                </div>
                            </div>
                            <h5 id="previewTitle" class="mb-3">{{ $service->title }}</h5>
                            <div id="previewImageContainer" class="mb-3">
                                @if($service->image)
                                    <img src="{{ asset('storage/' . $service->image) }}" class="img-fluid rounded" 
                                         style="max-height: 150px;" id="currentPreviewImg">
                                @endif
                            </div>
                            <div id="previewDescription" class="text-muted small text-start">
                                {!! $service->description !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image preview
        document.getElementById('image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                    updatePreview(true);
                };
                reader.readAsDataURL(file);
            }
        });

        // Remove image checkbox
        if (document.getElementById('remove_image')) {
            document.getElementById('remove_image').addEventListener('change', function(e) {
                if (this.checked) {
                    document.getElementById('previewImageContainer').innerHTML = '';
                } else {
                    // Restore original image if available
                    const currentImg = document.getElementById('currentPreviewImg');
                    if (currentImg) {
                        document.getElementById('previewImageContainer').innerHTML = 
                            `<img src="${currentImg.src}" class="img-fluid rounded" style="max-height: 150px;" id="currentPreviewImg">`;
                    }
                }
            });
        }

        // Live preview update
        function updatePreview(newImage = false) {
            const title = document.getElementById('title').value || 'Service Title';
            const description = window.getRichTextContent ? window.getRichTextContent('description') : 
                               document.querySelector('[name="description"]').value || 'Service description will appear here';
            const icon = document.getElementById('icon').value || 'fa-graduation-cap';
            
            // Update title
            document.getElementById('previewTitle').textContent = title;
            
            // Update description
            document.getElementById('previewDescription').innerHTML = description;
            
            // Update icon
            const previewIcon = document.getElementById('previewIcon');
            previewIcon.className = ''; // Clear existing classes
            previewIcon.classList.add(icon, 'fa-2x', 'text-white');
            
            // Update image if a new one is selected
            if (newImage) {
                const previewImg = document.getElementById('previewImg');
                if (previewImg && previewImg.src && previewImg.src !== window.location.href) {
                    const imgContainer = document.getElementById('previewImageContainer');
                    imgContainer.innerHTML = `<img src="${previewImg.src}" class="img-fluid rounded" style="max-height: 150px;">`;
                }
            }
        }

        // Form field listeners for live preview
        ['title', 'icon'].forEach(id => {
            document.getElementById(id).addEventListener('input', function() {
                updatePreview(false);
            });
        });

        // Listen for rich text editor changes
        document.addEventListener('richTextAutoSave', function(e) {
            if (e.detail.editorId.includes('description')) {
                updatePreview(false);
            }
        });

        // Form submission handling
        document.querySelector('form').addEventListener('submit', function(e) {
            const action = e.submitter.value;
            if (action === 'save') {
                document.getElementById('is_active').checked = false;
            } else if (action === 'publish') {
                document.getElementById('is_active').checked = true;
            }
        });
    });
</script>
@endpush
@endsection