<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('certificates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('certificate_type'); // course, session, cpd
            $table->foreignId('course_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('live_session_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('certificate_number')->unique();
            $table->string('title');
            $table->text('description')->nullable();
            $table->integer('cpd_credits');
            $table->timestamp('issued_at');
            $table->timestamp('expires_at')->nullable();
            $table->string('certificate_file')->nullable(); // PDF file path
            $table->json('verification_data')->nullable(); // verification hash, etc.
            $table->string('status')->default('active'); // active, expired, revoked
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('certificates');
    }
};
