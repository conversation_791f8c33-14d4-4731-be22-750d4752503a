<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Enrollment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'course_id',
        'enrolled_at',
        'started_at',
        'completed_at',
        'progress_percentage',
        'status',
        'progress_data',
    ];

    protected $casts = [
        'enrolled_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'progress_data' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeEnrolled($query)
    {
        return $query->where('status', 'enrolled');
    }

    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    public function isInProgress()
    {
        return $this->status === 'in_progress';
    }

    public function updateProgress($percentage, $data = null)
    {
        $this->progress_percentage = $percentage;

        if ($data) {
            $this->progress_data = array_merge($this->progress_data ?? [], $data);
        }

        if ($percentage >= 100) {
            $this->status = 'completed';
            $this->completed_at = now();
        } elseif ($percentage > 0 && $this->status === 'enrolled') {
            $this->status = 'in_progress';
            $this->started_at = now();
        }

        $this->save();
    }
}
