@extends('layouts.dashboard')

@section('page-title', 'Help Center')

@section('content')
<div class="dashboard-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Help Center</h2>
                    <p class="text-muted mb-0">Find answers to frequently asked questions</p>
                </div>
                <div>
                    <a href="{{ route('dashboard.support.contact') }}" class="btn btn-primary">
                        <i class="fas fa-headset me-2"></i>Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <form method="GET" class="row align-items-center">
                                <div class="col-md-7">
                                    <div class="position-relative">
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-search"></i>
                                            </span>
                                            <input type="text" name="search" id="searchInput" class="form-control"
                                                   placeholder="Search FAQs..." value="{{ $search }}"
                                                   autocomplete="off">
                                        </div>
                                        <!-- Live Search Results -->
                                        <div id="searchResults" class="position-absolute w-100 bg-white border rounded shadow-sm mt-1 d-none" style="z-index: 1000;">
                                            <!-- Results will be populated here -->
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <select name="category" class="form-select">
                                        <option value="all">All Categories</option>
                                        @foreach($categories as $cat)
                                            <option value="{{ $cat }}" {{ $category == $cat ? 'selected' : '' }}>
                                                {{ ucfirst(str_replace('_', ' ', $cat)) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Help Categories -->
    @if(!$search && $category === 'all')
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-3">Browse by Category</h4>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{{ route('dashboard.support.help', ['category' => 'general']) }}" class="text-decoration-none">
                <div class="card border-0 shadow-sm h-100 hover-card">
                    <div class="card-body text-center">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3 mb-3 d-inline-flex">
                            <i class="fas fa-question-circle text-primary fa-2x"></i>
                        </div>
                        <h6 class="card-title">General Questions</h6>
                        <p class="text-muted small">Basic platform information and getting started</p>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{{ route('dashboard.support.help', ['category' => 'courses']) }}" class="text-decoration-none">
                <div class="card border-0 shadow-sm h-100 hover-card">
                    <div class="card-body text-center">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3 mb-3 d-inline-flex">
                            <i class="fas fa-graduation-cap text-success fa-2x"></i>
                        </div>
                        <h6 class="card-title">Courses</h6>
                        <p class="text-muted small">Course enrollment, progress, and completion</p>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{{ route('dashboard.support.help', ['category' => 'sessions']) }}" class="text-decoration-none">
                <div class="card border-0 shadow-sm h-100 hover-card">
                    <div class="card-body text-center">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3 mb-3 d-inline-flex">
                            <i class="fas fa-video text-info fa-2x"></i>
                        </div>
                        <h6 class="card-title">Live Sessions</h6>
                        <p class="text-muted small">Session registration, joining, and attendance</p>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{{ route('dashboard.support.help', ['category' => 'certificates']) }}" class="text-decoration-none">
                <div class="card border-0 shadow-sm h-100 hover-card">
                    <div class="card-body text-center">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3 mb-3 d-inline-flex">
                            <i class="fas fa-certificate text-warning fa-2x"></i>
                        </div>
                        <h6 class="card-title">Certificates & CPD</h6>
                        <p class="text-muted small">Certificate downloads and CPD credit tracking</p>
                    </div>
                </div>
            </a>
        </div>
    </div>
    @endif

    <!-- FAQs List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2 text-primary"></i>
                        @if($search)
                            Search Results for "{{ $search }}"
                        @elseif($category !== 'all')
                            {{ ucfirst(str_replace('_', ' ', $category)) }} FAQs
                        @else
                            Frequently Asked Questions
                        @endif
                    </h5>
                </div>
                <div class="card-body p-0">
                    @forelse($faqs as $faq)
                    <div class="faq-item border-bottom">
                        <div class="faq-question p-4 cursor-pointer" data-bs-toggle="collapse" 
                             data-bs-target="#faq-{{ $faq->id }}" aria-expanded="false">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="bg-{{ $faq->category_color }} bg-opacity-10 rounded-circle p-2 me-3">
                                        <i class="{{ $faq->category_icon }} text-{{ $faq->category_color }}"></i>
                                    </div>
                                    <h6 class="mb-0">{{ $faq->question }}</h6>
                                </div>
                                <i class="fas fa-chevron-down text-muted"></i>
                            </div>
                        </div>
                        <div class="collapse" id="faq-{{ $faq->id }}">
                            <div class="faq-answer p-4 pt-0 bg-light">
                                <div class="ms-5">
                                    {!! nl2br(e($faq->answer)) !!}

                                    <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                                        <small class="text-muted">
                                            <i class="fas fa-tag me-1"></i>{{ ucfirst(str_replace('_', ' ', $faq->category)) }}
                                        </small>

                                        <div class="faq-rating" data-faq-id="{{ $faq->id }}">
                                            <small class="text-muted me-2">Was this helpful?</small>
                                            <button class="btn btn-sm btn-outline-success btn-helpful me-1">
                                                <i class="fas fa-thumbs-up me-1"></i>Yes
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger btn-not-helpful">
                                                <i class="fas fa-thumbs-down me-1"></i>No
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No FAQs found</h5>
                        <p class="text-muted mb-4">
                            @if($search)
                                No results found for "{{ $search }}". Try different keywords or browse by category.
                            @else
                                No FAQs are available for this category at the moment.
                            @endif
                        </p>
                        @if($search || $category !== 'all')
                            <a href="{{ route('dashboard.support.help') }}" class="btn btn-primary me-2">
                                <i class="fas fa-list me-1"></i>View All FAQs
                            </a>
                        @endif
                        <a href="{{ route('dashboard.support.contact') }}" class="btn btn-outline-primary">
                            <i class="fas fa-headset me-1"></i>Contact Support
                        </a>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    @if($faqs->hasPages())
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-center">
                {{ $faqs->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
    @endif

    <!-- Still Need Help Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-primary text-white">
                <div class="card-body text-center py-4">
                    <h4 class="mb-3">Still need help?</h4>
                    <p class="mb-4">Can't find what you're looking for? Our support team is here to help you.</p>
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-6">
                                    <a href="{{ route('dashboard.support.contact') }}" class="btn btn-light btn-lg w-100">
                                        <i class="fas fa-headset me-2"></i>Contact Support
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="mailto:<EMAIL>" class="btn btn-outline-light btn-lg w-100">
                                        <i class="fas fa-envelope me-2"></i>Email Us
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.hover-card {
    transition: transform 0.2s ease-in-out;
}

.hover-card:hover {
    transform: translateY(-5px);
}

.faq-question {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.faq-question:hover {
    background-color: #f8f9fa;
}

.faq-question[aria-expanded="true"] .fa-chevron-down {
    transform: rotate(180deg);
}

.fa-chevron-down {
    transition: transform 0.2s ease;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live search functionality
    const searchInput = document.getElementById('searchInput');
    const searchResults = document.getElementById('searchResults');
    let searchTimeout;

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length < 2) {
                searchResults.classList.add('d-none');
                return;
            }

            searchTimeout = setTimeout(() => {
                fetch(`{{ route('dashboard.support.search') }}?q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.results.length > 0) {
                            let html = '<div class="p-2"><small class="text-muted">Quick Results:</small></div>';
                            data.results.forEach(result => {
                                html += `
                                    <a href="{{ route('dashboard.support.help') }}?search=${encodeURIComponent(query)}#faq-${result.id}"
                                       class="d-block p-2 text-decoration-none border-bottom">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-question-circle text-primary me-2"></i>
                                            <div>
                                                <small class="fw-semibold">${result.question}</small>
                                                <br><small class="text-muted">${result.category}</small>
                                            </div>
                                        </div>
                                    </a>
                                `;
                            });
                            searchResults.innerHTML = html;
                            searchResults.classList.remove('d-none');
                        } else {
                            searchResults.innerHTML = '<div class="p-3 text-center text-muted">No results found</div>';
                            searchResults.classList.remove('d-none');
                        }
                    })
                    .catch(error => {
                        console.error('Search error:', error);
                        searchResults.classList.add('d-none');
                    });
            }, 300);
        });

        // Hide search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                searchResults.classList.add('d-none');
            }
        });
    }

    // FAQ Rating functionality
    document.querySelectorAll('.faq-rating').forEach(function(ratingDiv) {
        const faqId = ratingDiv.dataset.faqId;
        const helpfulBtn = ratingDiv.querySelector('.btn-helpful');
        const notHelpfulBtn = ratingDiv.querySelector('.btn-not-helpful');

        if (helpfulBtn) {
            helpfulBtn.addEventListener('click', function() {
                rateFaq(faqId, true, ratingDiv);
            });
        }

        if (notHelpfulBtn) {
            notHelpfulBtn.addEventListener('click', function() {
                rateFaq(faqId, false, ratingDiv);
            });
        }
    });

    function rateFaq(faqId, helpful, ratingDiv) {
        fetch(`/dashboard/support/faq/${faqId}/rate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ helpful: helpful })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                ratingDiv.innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-check-circle text-success me-1"></i>
                        <small class="text-success">${data.message}</small>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Rating error:', error);
        });
    }

    // Auto-expand FAQ if there's a hash in URL
    if (window.location.hash) {
        const target = document.querySelector(window.location.hash);
        if (target && target.classList.contains('collapse')) {
            const collapse = new bootstrap.Collapse(target, {
                show: true
            });
        }
    }

    // Update URL hash when FAQ is expanded
    document.querySelectorAll('[data-bs-toggle="collapse"]').forEach(function(element) {
        element.addEventListener('click', function() {
            const target = this.getAttribute('data-bs-target');
            if (target) {
                setTimeout(function() {
                    window.location.hash = target;
                }, 100);
            }
        });
    });
});
</script>
@endpush
