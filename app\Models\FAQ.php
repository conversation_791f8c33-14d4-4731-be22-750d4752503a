<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FAQ extends Model
{
    use HasFactory;

    protected $table = 'faqs';

    protected $fillable = [
        'question',
        'answer',
        'category',
        'order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function getCategoryIconAttribute()
    {
        $icons = [
            'general' => 'fas fa-question-circle',
            'technical' => 'fas fa-cog',
            'billing' => 'fas fa-credit-card',
            'courses' => 'fas fa-graduation-cap',
            'sessions' => 'fas fa-video',
            'certificates' => 'fas fa-certificate',
            'account' => 'fas fa-user',
        ];

        return $icons[$this->category] ?? 'fas fa-info-circle';
    }

    public function getCategoryColorAttribute()
    {
        $colors = [
            'general' => 'primary',
            'technical' => 'warning',
            'billing' => 'success',
            'courses' => 'info',
            'sessions' => 'danger',
            'certificates' => 'warning',
            'account' => 'secondary',
        ];

        return $colors[$this->category] ?? 'primary';
    }
}
