<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CarouselSlide>
 */
class CarouselSlideFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $slides = [
            [
                'title' => 'Welcome to VCH Learning',
                'subtitle' => 'Professional Healthcare Education',
                'description' => 'Advance your healthcare career with our comprehensive training programs designed by industry experts.',
                'primary_button_text' => 'Explore Courses',
                'primary_button_link' => '/courses',
                'secondary_button_text' => 'Learn More',
                'secondary_button_link' => '/about',
            ],
            [
                'title' => 'Live Interactive Sessions',
                'subtitle' => 'Learn from Industry Experts',
                'description' => 'Join live sessions with experienced healthcare professionals and get real-time answers to your questions.',
                'primary_button_text' => 'View Sessions',
                'primary_button_link' => '/live-sessions',
                'secondary_button_text' => 'Schedule',
                'secondary_button_link' => '/live-sessions/schedule',
            ],
            [
                'title' => 'Earn Professional Certifications',
                'subtitle' => 'Recognized Industry Standards',
                'description' => 'Complete courses and earn certifications that are recognized by major healthcare organizations worldwide.',
                'primary_button_text' => 'Get Certified',
                'primary_button_link' => '/certifications',
                'secondary_button_text' => 'View Certificates',
                'secondary_button_link' => '/dashboard/certificates',
            ],
            [
                'title' => 'Flexible Learning Platform',
                'subtitle' => 'Learn at Your Own Pace',
                'description' => 'Access courses anytime, anywhere with our mobile-friendly platform designed for busy healthcare professionals.',
                'primary_button_text' => 'Start Learning',
                'primary_button_link' => '/register',
                'secondary_button_text' => 'Free Trial',
                'secondary_button_link' => '/trial',
            ],
            [
                'title' => 'CPD Credits Available',
                'subtitle' => 'Maintain Your Professional Development',
                'description' => 'Earn Continuing Professional Development credits with our accredited courses and maintain your professional standing.',
                'primary_button_text' => 'View CPD Courses',
                'primary_button_link' => '/courses?cpd=true',
                'secondary_button_text' => 'Track Credits',
                'secondary_button_link' => '/dashboard/cpd',
            ],
        ];

        $slideData = $this->faker->randomElement($slides);

        return [
            'title' => $slideData['title'],
            'subtitle' => $slideData['subtitle'],
            'description' => $slideData['description'],
            'image' => 'carousel/slide-' . $this->faker->numberBetween(1, 5) . '.jpg',
            'primary_button_text' => $slideData['primary_button_text'],
            'primary_button_link' => $slideData['primary_button_link'],
            'secondary_button_text' => $this->faker->optional(0.7)->passthrough($slideData['secondary_button_text']),
            'secondary_button_link' => $this->faker->optional(0.7)->passthrough($slideData['secondary_button_link']),
            'order' => $this->faker->numberBetween(1, 10),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'created_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the slide is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the slide is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the slide has both buttons.
     */
    public function withBothButtons(): static
    {
        return $this->state(fn (array $attributes) => [
            'secondary_button_text' => 'Learn More',
            'secondary_button_link' => '/about',
        ]);
    }

    /**
     * Indicate that the slide has only primary button.
     */
    public function primaryButtonOnly(): static
    {
        return $this->state(fn (array $attributes) => [
            'secondary_button_text' => null,
            'secondary_button_link' => null,
        ]);
    }
}
