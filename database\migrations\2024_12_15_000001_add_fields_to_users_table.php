<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Only add fields that don't exist
            if (!Schema::hasColumn('users', 'role')) {
                $table->string('role')->default('user')->after('email_verified_at');
            }

            if (!Schema::hasColumn('users', 'job_title')) {
                $table->string('job_title')->nullable()->after('organization');
            }

            if (!Schema::hasColumn('users', 'preferences')) {
                $table->json('preferences')->nullable()->after('notification_preferences');
            }

            // Add indexes for performance (only if columns exist)
            if (Schema::hasColumn('users', 'role')) {
                $table->index('role');
            }
            if (Schema::hasColumn('users', 'is_active')) {
                $table->index('is_active');
            }
            if (Schema::hasColumn('users', 'last_login_at')) {
                $table->index('last_login_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['role']);
            $table->dropIndex(['is_active']);
            $table->dropIndex(['last_login_at']);
            
            $table->dropColumn([
                'role',
                'phone',
                'organization',
                'job_title',
                'bio',
                'avatar',
                'is_active',
                'last_login_at',
                'preferences'
            ]);
        });
    }
};
