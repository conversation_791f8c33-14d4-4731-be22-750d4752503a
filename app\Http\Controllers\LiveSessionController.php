<?php

namespace App\Http\Controllers;

use App\Models\LiveSession;
use App\Models\SessionRegistration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LiveSessionController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $filter = $request->get('filter', 'upcoming');
        $category = $request->get('category');
        $search = $request->get('search');
        $date_from = $request->get('date_from');
        $date_to = $request->get('date_to');

        $query = LiveSession::query();

        // Apply main filter
        switch ($filter) {
            case 'upcoming':
                $query->upcoming();
                break;
            case 'completed':
                $query->completed();
                break;
            case 'live':
                $query->live();
                break;
            case 'registered':
                $query->whereHas('registrations', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                });
                break;
            case 'attended':
                $query->whereHas('registrations', function ($q) use ($user) {
                    $q->where('user_id', $user->id)->where('status', 'attended');
                });
                break;
            case 'all':
                // No additional filter
                break;
        }

        // Apply category filter
        if ($category) {
            $query->byCategory($category);
        }

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('instructor', 'like', "%{$search}%");
            });
        }

        // Apply date range filter
        if ($date_from) {
            $query->where('session_date', '>=', $date_from);
        }
        if ($date_to) {
            $query->where('session_date', '<=', $date_to . ' 23:59:59');
        }

        $sessions = $query->with(['registrations' => function ($q) use ($user) {
            $q->where('user_id', $user->id);
        }])->orderBy('session_date', $filter === 'upcoming' ? 'asc' : 'desc')->paginate(12);

        // Get user statistics
        $stats = [
            'upcoming_count' => LiveSession::upcoming()->count(),
            'registered_count' => SessionRegistration::where('user_id', $user->id)->count(),
            'attended_count' => SessionRegistration::where('user_id', $user->id)
                ->where('status', 'attended')->count(),
            'total_credits' => SessionRegistration::where('user_id', $user->id)
                ->where('status', 'attended')
                ->with('liveSession')
                ->get()
                ->sum(function ($registration) {
                    return $registration->liveSession->cpd_credits;
                }),
        ];

        // Get available categories for filter
        $categories = LiveSession::distinct('category')->pluck('category')->filter();

        return view('dashboard.live-sessions.index', compact(
            'sessions',
            'stats',
            'filter',
            'category',
            'search',
            'date_from',
            'date_to',
            'categories'
        ));
    }

    public function show(LiveSession $session)
    {
        $user = Auth::user();
        $registration = $session->registrations()->where('user_id', $user->id)->first();

        return view('dashboard.live-sessions.show', compact('session', 'registration'));
    }

    public function register(Request $request, LiveSession $session)
    {
        $user = Auth::user();

        // Check if already registered
        if ($session->isRegisteredByUser($user->id)) {
            return redirect()->back()->with('error', 'You are already registered for this session.');
        }

        // Check if session is full
        if ($session->isFullyBooked()) {
            return redirect()->back()->with('error', 'This session is fully booked.');
        }

        // Check if session is still upcoming
        if ($session->session_date->isPast()) {
            return redirect()->back()->with('error', 'Registration is closed for this session.');
        }

        SessionRegistration::create([
            'user_id' => $user->id,
            'live_session_id' => $session->id,
            'registered_at' => now(),
            'status' => 'registered',
        ]);

        return redirect()->back()->with('success', 'Successfully registered for the session!');
    }

    public function unregister(Request $request, LiveSession $session)
    {
        $user = Auth::user();

        $registration = SessionRegistration::where('user_id', $user->id)
            ->where('live_session_id', $session->id)
            ->first();

        if (!$registration) {
            return redirect()->back()->with('error', 'You are not registered for this session.');
        }

        // Check if session hasn't started yet
        if ($session->session_date->isPast()) {
            return redirect()->back()->with('error', 'Cannot unregister from a session that has already started.');
        }

        $registration->delete();

        return redirect()->back()->with('success', 'Successfully unregistered from the session.');
    }

    public function join(LiveSession $session)
    {
        $user = Auth::user();

        // Check if user is registered
        if (!$session->isRegisteredByUser($user->id)) {
            return redirect()->back()->with('error', 'You must be registered to join this session.');
        }

        // Check if session is live or about to start (within 15 minutes)
        $sessionStart = $session->session_date;
        $now = now();

        if ($now->lt($sessionStart->subMinutes(15))) {
            return redirect()->back()->with('error', 'Session has not started yet. You can join 15 minutes before the start time.');
        }

        if ($now->gt($sessionStart->addMinutes($session->duration_minutes))) {
            return redirect()->back()->with('error', 'This session has already ended.');
        }

        // Mark as attended if not already
        $registration = SessionRegistration::where('user_id', $user->id)
            ->where('live_session_id', $session->id)
            ->first();

        if ($registration && $registration->status === 'registered') {
            $registration->markAsAttended();
        }

        // Redirect to meeting URL
        if ($session->meeting_url) {
            return redirect($session->meeting_url);
        }

        return redirect()->back()->with('error', 'Meeting URL is not available for this session.');
    }

    public function markAttendance(Request $request, LiveSession $session)
    {
        $user = Auth::user();

        $registration = SessionRegistration::where('user_id', $user->id)
            ->where('live_session_id', $session->id)
            ->first();

        if (!$registration) {
            return response()->json(['error' => 'Registration not found'], 404);
        }

        $duration = $request->input('duration', $session->duration_minutes);
        $registration->markAsAttended($duration);

        return response()->json(['success' => true, 'message' => 'Attendance marked successfully']);
    }

    public function submitFeedback(Request $request, LiveSession $session)
    {
        $user = Auth::user();

        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'feedback' => 'nullable|string|max:1000',
            'would_recommend' => 'boolean',
        ]);

        $registration = SessionRegistration::where('user_id', $user->id)
            ->where('live_session_id', $session->id)
            ->where('status', 'attended')
            ->first();

        if (!$registration) {
            return redirect()->back()->with('error', 'You must attend the session to provide feedback.');
        }

        $feedbackData = [
            'rating' => $request->rating,
            'feedback' => $request->feedback,
            'would_recommend' => $request->boolean('would_recommend'),
            'submitted_at' => now(),
        ];

        $registration->addFeedback($feedbackData);

        return redirect()->back()->with('success', 'Thank you for your feedback!');
    }

    public function feedback(Request $request, LiveSession $session)
    {
        $user = Auth::user();

        $registration = SessionRegistration::where('user_id', $user->id)
            ->where('live_session_id', $session->id)
            ->where('status', 'attended')
            ->first();

        if (!$registration) {
            return redirect()->back()->with('error', 'You must have attended this session to provide feedback.');
        }

        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'comments' => 'nullable|string|max:1000',
        ]);

        $registration->addFeedback([
            'rating' => $request->rating,
            'comments' => $request->comments,
            'submitted_at' => now(),
        ]);

        return redirect()->back()->with('success', 'Thank you for your feedback!');
    }
}
