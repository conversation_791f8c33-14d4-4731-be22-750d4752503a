<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LiveSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'instructor',
        'instructor_title',
        'instructor_image',
        'session_date',
        'duration_minutes',
        'cpd_credits',
        'category',
        'level',
        'is_free',
        'price',
        'meeting_url',
        'meeting_id',
        'meeting_password',
        'max_participants',
        'status',
        'materials',
        'prerequisites',
        'registration_required',
        'auto_record',
        'waiting_room',
        'join_before_host',
        'mute_participants',
        'zoom_meeting_id',
        'zoom_join_url',
        'zoom_start_url',
        'zoom_password',
        'zoom_webinar_id',
    ];

    protected $casts = [
        'session_date' => 'datetime',
        'materials' => 'array',
    ];

    public function registrations()
    {
        return $this->hasMany(SessionRegistration::class);
    }

    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'session_registrations')
                    ->withPivot(['registered_at', 'attended_at', 'attendance_duration', 'status', 'feedback'])
                    ->withTimestamps();
    }

    public function scopeUpcoming($query)
    {
        return $query->where('session_date', '>', now())
                    ->where('status', 'scheduled');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeLive($query)
    {
        return $query->where('status', 'live');
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function isRegisteredByUser($userId)
    {
        return $this->registrations()->where('user_id', $userId)->exists();
    }

    public function wasAttendedByUser($userId)
    {
        return $this->registrations()
                    ->where('user_id', $userId)
                    ->where('status', 'attended')
                    ->exists();
    }

    public function getRegistrationCountAttribute()
    {
        return $this->registrations()->count();
    }

    public function getAvailableSpotsAttribute()
    {
        if (!$this->max_participants) {
            return null;
        }
        return $this->max_participants - $this->registration_count;
    }

    public function isFullyBooked()
    {
        return $this->max_participants && $this->registration_count >= $this->max_participants;
    }
}
