<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('support_tickets')) {
            Schema::create('support_tickets', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('ticket_number')->unique();
                $table->string('subject');
                $table->text('message');
                $table->string('category')->default('general');
                $table->string('priority')->default('medium');
                $table->string('status')->default('open');
                $table->string('assigned_to')->nullable();
                $table->text('resolution')->nullable();
                $table->timestamp('resolved_at')->nullable();
                $table->timestamps();
                
                // Indexes
                $table->index('ticket_number');
                $table->index('category');
                $table->index('priority');
                $table->index('status');
                $table->index('user_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('support_tickets');
    }
};
