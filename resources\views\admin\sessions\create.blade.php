@extends('layouts.dashboard')

@section('title', 'Create Live Session')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Create New Live Session</h1>
            <p class="mb-0 text-muted">Create a live session with automatic Zoom integration</p>
        </div>
        <a href="{{ route('admin.sessions.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Sessions
        </a>
    </div>

    <form method="POST" action="{{ route('admin.sessions.store') }}">
        @csrf
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Session Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-group mb-3">
                            <label for="title">Session Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group mb-3">
                            <label for="description">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="instructor">Instructor Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('instructor') is-invalid @enderror" 
                                           id="instructor" name="instructor" value="{{ old('instructor') }}" required>
                                    @error('instructor')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="instructor_title">Instructor Title</label>
                                    <input type="text" class="form-control @error('instructor_title') is-invalid @enderror" 
                                           id="instructor_title" name="instructor_title" value="{{ old('instructor_title') }}" 
                                           placeholder="e.g., Chief Medical Officer">
                                    @error('instructor_title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="session_date">Session Date & Time <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control @error('session_date') is-invalid @enderror"
                                           id="session_date" name="session_date" value="{{ old('session_date') }}" required>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-clock me-1"></i>Time zone: East Africa Time (EAT) - Nairobi, Kenya
                                    </small>
                                    @error('session_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="duration_minutes">Duration (minutes) <span class="text-danger">*</span></label>
                                    <select class="form-control @error('duration_minutes') is-invalid @enderror" 
                                            id="duration_minutes" name="duration_minutes" required>
                                        <option value="">Select Duration</option>
                                        <option value="30" {{ old('duration_minutes') == '30' ? 'selected' : '' }}>30 minutes</option>
                                        <option value="45" {{ old('duration_minutes') == '45' ? 'selected' : '' }}>45 minutes</option>
                                        <option value="60" {{ old('duration_minutes') == '60' ? 'selected' : '' }}>1 hour</option>
                                        <option value="90" {{ old('duration_minutes') == '90' ? 'selected' : '' }}>1.5 hours</option>
                                        <option value="120" {{ old('duration_minutes') == '120' ? 'selected' : '' }}>2 hours</option>
                                        <option value="180" {{ old('duration_minutes') == '180' ? 'selected' : '' }}>3 hours</option>
                                        <option value="240" {{ old('duration_minutes') == '240' ? 'selected' : '' }}>4 hours</option>
                                    </select>
                                    @error('duration_minutes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="category">Category</label>
                                    <select class="form-control @error('category') is-invalid @enderror" id="category" name="category">
                                        <option value="">Select Category</option>
                                        <option value="Emergency Medicine" {{ old('category') == 'Emergency Medicine' ? 'selected' : '' }}>Emergency Medicine</option>
                                        <option value="Patient Care" {{ old('category') == 'Patient Care' ? 'selected' : '' }}>Patient Care</option>
                                        <option value="Healthcare Management" {{ old('category') == 'Healthcare Management' ? 'selected' : '' }}>Healthcare Management</option>
                                        <option value="Clinical Skills" {{ old('category') == 'Clinical Skills' ? 'selected' : '' }}>Clinical Skills</option>
                                        <option value="Medical Technology" {{ old('category') == 'Medical Technology' ? 'selected' : '' }}>Medical Technology</option>
                                        <option value="Healthcare Ethics" {{ old('category') == 'Healthcare Ethics' ? 'selected' : '' }}>Healthcare Ethics</option>
                                        <option value="Public Health" {{ old('category') == 'Public Health' ? 'selected' : '' }}>Public Health</option>
                                    </select>
                                    @error('category')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="level">Level</label>
                                    <select class="form-control @error('level') is-invalid @enderror" id="level" name="level">
                                        <option value="">Select Level</option>
                                        <option value="Beginner" {{ old('level') == 'Beginner' ? 'selected' : '' }}>Beginner</option>
                                        <option value="Intermediate" {{ old('level') == 'Intermediate' ? 'selected' : '' }}>Intermediate</option>
                                        <option value="Advanced" {{ old('level') == 'Advanced' ? 'selected' : '' }}>Advanced</option>
                                    </select>
                                    @error('level')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="cpd_credits">CPD Credits</label>
                                    <input type="number" class="form-control @error('cpd_credits') is-invalid @enderror" 
                                           id="cpd_credits" name="cpd_credits" value="{{ old('cpd_credits') }}" 
                                           min="0" max="10" step="0.1">
                                    @error('cpd_credits')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="max_participants">Maximum Participants</label>
                            <input type="number" class="form-control @error('max_participants') is-invalid @enderror" 
                                   id="max_participants" name="max_participants" value="{{ old('max_participants') }}" 
                                   min="1" max="1000" placeholder="Leave empty for unlimited">
                            @error('max_participants')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Leave empty for unlimited participants</small>
                        </div>
                    </div>
                </div>

                <!-- Pricing -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Pricing</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input type="hidden" name="is_free" value="0">
                            <input type="checkbox" class="form-check-input" id="is_free" name="is_free" value="1" 
                                   {{ old('is_free', true) ? 'checked' : '' }} onchange="togglePricing()">
                            <label class="form-check-label" for="is_free">
                                Free Session
                            </label>
                        </div>

                        <div id="pricing-section" style="{{ old('is_free', true) ? 'display: none;' : '' }}">
                            <div class="form-group mb-3">
                                <label for="price">Session Price ($)</label>
                                <input type="number" class="form-control @error('price') is-invalid @enderror" 
                                       id="price" name="price" value="{{ old('price') }}" 
                                       min="0" step="0.01">
                                @error('price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Zoom Settings -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fab fa-zoom text-primary me-2"></i>Zoom Meeting Settings
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input type="hidden" name="registration_required" value="0">
                                    <input type="checkbox" class="form-check-input" id="registration_required" 
                                           name="registration_required" value="1" {{ old('registration_required') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="registration_required">
                                        Require Registration
                                    </label>
                                    <small class="form-text text-muted">Participants must register before joining</small>
                                </div>

                                <div class="form-check mb-3">
                                    <input type="hidden" name="waiting_room" value="0">
                                    <input type="checkbox" class="form-check-input" id="waiting_room" 
                                           name="waiting_room" value="1" {{ old('waiting_room') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="waiting_room">
                                        Enable Waiting Room
                                    </label>
                                </div>

                                <div class="form-check mb-3">
                                    <input type="hidden" name="auto_record" value="0">
                                    <input type="checkbox" class="form-check-input" id="auto_record" 
                                           name="auto_record" value="1" {{ old('auto_record') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="auto_record">
                                        Auto Record to Cloud
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input type="hidden" name="join_before_host" value="0">
                                    <input type="checkbox" class="form-check-input" id="join_before_host" 
                                           name="join_before_host" value="1" {{ old('join_before_host') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="join_before_host">
                                        Allow Join Before Host
                                    </label>
                                </div>

                                <div class="form-check mb-3">
                                    <input type="hidden" name="mute_participants" value="0">
                                    <input type="checkbox" class="form-check-input" id="mute_participants" 
                                           name="mute_participants" value="1" {{ old('mute_participants', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="mute_participants">
                                        Mute Participants on Entry
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Zoom Integration</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <i class="fab fa-zoom fa-3x text-primary"></i>
                        </div>
                        <p class="text-muted small">
                            This session will automatically create a Zoom meeting with the following features:
                        </p>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success me-2"></i>Automatic meeting creation</li>
                            <li><i class="fas fa-check text-success me-2"></i>No registration required for participants</li>
                            <li><i class="fas fa-check text-success me-2"></i>Direct join links</li>
                            <li><i class="fas fa-check text-success me-2"></i>Host controls</li>
                            <li><i class="fas fa-check text-success me-2"></i>Optional cloud recording</li>
                            <li><i class="fas fa-clock text-info me-2"></i>Scheduled in East Africa Time (EAT)</li>
                        </ul>
                    </div>
                </div>

                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Session Guidelines</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled small text-muted">
                            <li class="mb-2">
                                <strong>Timing:</strong> Schedule sessions at least 1 hour in advance
                            </li>
                            <li class="mb-2">
                                <strong>Duration:</strong> Recommended 60-120 minutes for optimal engagement
                            </li>
                            <li class="mb-2">
                                <strong>Participants:</strong> Consider your Zoom plan limits
                            </li>
                            <li class="mb-2">
                                <strong>Recording:</strong> Enable for sessions that will be referenced later
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>Create Live Session
                    </button>
                    <a href="{{ route('admin.sessions.index') }}" class="btn btn-secondary btn-lg">
                        Cancel
                    </a>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
function togglePricing() {
    const isFree = document.getElementById('is_free').checked;
    const pricingSection = document.getElementById('pricing-section');
    const priceInput = document.getElementById('price');
    
    if (isFree) {
        pricingSection.style.display = 'none';
        priceInput.value = '';
        priceInput.removeAttribute('required');
    } else {
        pricingSection.style.display = 'block';
        priceInput.setAttribute('required', 'required');
    }
}

// Set minimum date to current date/time in East Africa Time
document.addEventListener('DOMContentLoaded', function() {
    const sessionDateInput = document.getElementById('session_date');

    // Get current time in East Africa Time (UTC+3)
    const now = new Date();
    const eatOffset = 3 * 60; // EAT is UTC+3
    const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
    const eatTime = new Date(utc + (eatOffset * 60000));

    // Format for datetime-local input
    const year = eatTime.getFullYear();
    const month = String(eatTime.getMonth() + 1).padStart(2, '0');
    const day = String(eatTime.getDate()).padStart(2, '0');
    const hours = String(eatTime.getHours()).padStart(2, '0');
    const minutes = String(eatTime.getMinutes()).padStart(2, '0');

    const minDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;
    sessionDateInput.min = minDateTime;

    // Show current EAT time for reference
    const currentTimeDisplay = document.createElement('small');
    currentTimeDisplay.className = 'form-text text-info';
    currentTimeDisplay.innerHTML = `<i class="fas fa-info-circle me-1"></i>Current EAT time: ${eatTime.toLocaleString('en-US', {
        timeZone: 'Africa/Nairobi',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })}`;
    sessionDateInput.parentNode.appendChild(currentTimeDisplay);
});
</script>
@endpush
