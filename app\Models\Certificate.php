<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Certificate extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'certificate_type',
        'course_id',
        'live_session_id',
        'certificate_number',
        'title',
        'description',
        'cpd_credits',
        'issued_at',
        'expires_at',
        'certificate_file',
        'verification_data',
        'status',
    ];

    protected $casts = [
        'issued_at' => 'datetime',
        'expires_at' => 'datetime',
        'verification_data' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function liveSession()
    {
        return $this->belongsTo(LiveSession::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now())
                    ->orWhere('status', 'expired');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('certificate_type', $type);
    }

    public function isExpired()
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function isActive()
    {
        return $this->status === 'active' && !$this->isExpired();
    }

    public function generateCertificateNumber()
    {
        $prefix = strtoupper(substr($this->certificate_type, 0, 3));
        $year = now()->year;
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

        return $prefix . '-' . $year . '-' . $random;
    }

    public function getVerificationUrl()
    {
        return route('certificate.verify', $this->certificate_number);
    }

    public function getDownloadUrl()
    {
        if ($this->certificate_file) {
            return asset('storage/' . $this->certificate_file);
        }
        return null;
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($certificate) {
            if (!$certificate->certificate_number) {
                $certificate->certificate_number = $certificate->generateCertificateNumber();
            }
            if (!$certificate->issued_at) {
                $certificate->issued_at = now();
            }
        });
    }
}
