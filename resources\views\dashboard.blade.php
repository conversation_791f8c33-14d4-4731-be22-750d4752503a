@extends('layouts.dashboard')

@section('page-title', 'Overview')

@section('content')
<div class="dashboard-content-wrapper">
<!-- Dashboard Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 bg-gradient-primary text-white" style="background: linear-gradient(135deg, var(--medical-green), var(--medical-blue)) !important;">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h2 class="mb-2">Welcome back, {{ Auth::user()->name }}!</h2>
                        <p class="mb-0 opacity-75">Continue your medical education journey with Virtual CME Hub</p>
                    </div>
                    <div class="col-lg-4 text-end">
                        <div class="d-flex align-items-center justify-content-end">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <span>{{ date('l, F j, Y') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Statistics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-video text-primary fa-2x"></i>
                    </div>
                </div>
                <h3 class="text-primary mb-1">{{ $stats['sessions_attended'] }}</h3>
                <p class="text-muted mb-0">Live Sessions Attended</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-success bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-graduation-cap text-success fa-2x"></i>
                    </div>
                </div>
                <h3 class="text-success mb-1">{{ $stats['courses_completed'] }}</h3>
                <p class="text-muted mb-0">Courses Completed</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-certificate text-warning fa-2x"></i>
                    </div>
                </div>
                <h3 class="text-warning mb-1">{{ $stats['cpd_credits'] }}</h3>
                <p class="text-muted mb-0">CPD Credits Earned</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-info bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-clock text-info fa-2x"></i>
                    </div>
                </div>
                <h3 class="text-info mb-1">{{ $stats['learning_hours'] }}h</h3>
                <p class="text-muted mb-0">Learning Hours</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0"><i class="fas fa-bolt me-2 text-primary"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ route('dashboard.live-sessions.index') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-video fa-2x mb-2"></i>
                            <span>Live Sessions</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ route('dashboard.courses.index') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-book-open fa-2x mb-2"></i>
                            <span>Browse Courses</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ route('dashboard.certificates.index') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-certificate fa-2x mb-2"></i>
                            <span>View Certificates</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ route('dashboard.certificates.cpd') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <span>CPD Credits</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ route('profile.edit') }}" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-user-cog fa-2x mb-2"></i>
                            <span>Profile Settings</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="{{ route('dashboard.support.help') }}" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-headset fa-2x mb-2"></i>
                            <span>Get Support</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Dashboard Content -->
<div class="row">
    <!-- Learning Progress -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0"><i class="fas fa-graduation-cap me-2 text-primary"></i>Current Learning Path</h5>
            </div>
            <div class="card-body">
                @if($currentCourses->count() > 0)
                    <div class="row">
                        @foreach($currentCourses as $course)
                            @php
                                $enrollment = $course->enrollments->first();
                                $progress = $enrollment ? $enrollment->progress_percentage : 0;
                                $status = $enrollment ? $enrollment->status : 'enrolled';

                                $borderColor = 'border-primary';
                                $progressColor = 'bg-primary';
                                $buttonColor = 'btn-primary';
                                $buttonText = 'Continue';

                                if ($status === 'completed') {
                                    $borderColor = 'border-success';
                                    $progressColor = 'bg-success';
                                    $buttonColor = 'btn-success';
                                    $buttonText = 'Certificate';
                                } elseif ($progress === 0) {
                                    $borderColor = 'border-warning';
                                    $progressColor = 'bg-warning';
                                    $buttonColor = 'btn-warning';
                                    $buttonText = 'Start';
                                }
                            @endphp
                            <div class="col-md-4 mb-3">
                                <div class="card border {{ $borderColor }}">
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            @if($course->category === 'cardiology')
                                                <i class="fas fa-heart text-danger fa-3x"></i>
                                            @elseif($course->category === 'neurology')
                                                <i class="fas fa-brain text-info fa-3x"></i>
                                            @elseif($course->category === 'pediatrics')
                                                <i class="fas fa-child text-warning fa-3x"></i>
                                            @elseif($course->category === 'emergency')
                                                <i class="fas fa-ambulance text-danger fa-3x"></i>
                                            @else
                                                <i class="fas fa-graduation-cap text-primary fa-3x"></i>
                                            @endif
                                        </div>
                                        <h6>{{ $course->title }}</h6>
                                        <p class="text-muted small mb-3">{{ Str::limit($course->description, 50) }}</p>
                                        <div class="progress mb-3" style="height: 8px;">
                                            <div class="progress-bar {{ $progressColor }}" style="width: {{ $progress }}%"></div>
                                        </div>
                                        @if($status === 'completed')
                                            <small class="text-success">Completed</small>
                                        @else
                                            <small class="text-muted">{{ $progress }}% Complete</small>
                                        @endif
                                        <br>
                                        <a href="{{ route('dashboard.courses.show', $course) }}" class="btn {{ $buttonColor }} btn-sm mt-2">{{ $buttonText }}</a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Active Courses</h5>
                        <p class="text-muted">Start your learning journey by enrolling in a course.</p>
                        <a href="{{ route('dashboard.courses.index') }}" class="btn btn-primary">Browse Courses</a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Upcoming Sessions -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2 text-primary"></i>Upcoming Live Sessions</h5>
            </div>
            <div class="card-body">
                @if($upcomingSessions->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Session</th>
                                    <th>Instructor</th>
                                    <th>Date & Time</th>
                                    <th>CPD Credits</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($upcomingSessions as $session)
                                    @php
                                        $isRegistered = $session->registrations->count() > 0;
                                        $iconClass = 'fas fa-video';
                                        $iconColor = 'text-primary';

                                        if ($session->category === 'cardiology') {
                                            $iconClass = 'fas fa-heart';
                                            $iconColor = 'text-danger';
                                        } elseif ($session->category === 'neurology') {
                                            $iconClass = 'fas fa-brain';
                                            $iconColor = 'text-info';
                                        } elseif ($session->category === 'pediatrics') {
                                            $iconClass = 'fas fa-child';
                                            $iconColor = 'text-warning';
                                        } elseif ($session->category === 'emergency') {
                                            $iconClass = 'fas fa-ambulance';
                                            $iconColor = 'text-danger';
                                        }
                                    @endphp
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                                    <i class="{{ $iconClass }} {{ $iconColor }}"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-semibold">{{ $session->title }}</div>
                                                    <small class="text-muted">{{ Str::limit($session->description, 50) }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="fw-semibold">{{ $session->instructor }}</div>
                                                <small class="text-muted">{{ $session->instructor_title }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="fw-semibold">{{ $session->session_date->format('M j, Y') }}</div>
                                                <small class="text-muted">{{ $session->session_date->format('g:i A T') }}</small>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-primary">{{ $session->cpd_credits }} Credits</span></td>
                                        <td>
                                            @if($isRegistered)
                                                <a href="{{ $session->meeting_url }}" class="btn btn-sm btn-success" target="_blank">Join Session</a>
                                            @else
                                                <form action="{{ route('dashboard.live-sessions.register', $session) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-primary">Register</button>
                                                </form>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Upcoming Sessions</h5>
                        <p class="text-muted">Check back later for new live sessions.</p>
                        <a href="{{ route('dashboard.live-sessions.index') }}" class="btn btn-primary">View All Sessions</a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Recent Activities -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0"><i class="fas fa-clock me-2 text-primary"></i>Recent Activities</h5>
            </div>
            <div class="card-body">
                @if($recentActivities->count() > 0)
                    @foreach($recentActivities as $activity)
                        <div class="d-flex align-items-start {{ !$loop->last ? 'mb-3' : '' }}">
                            <div class="bg-{{ $activity['color'] }} bg-opacity-10 rounded-circle p-2 me-3">
                                <i class="{{ $activity['icon'] }} text-{{ $activity['color'] }}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-semibold">{{ $activity['title'] }}</div>
                                <small class="text-muted">{{ $activity['description'] }}</small>
                                <br>
                                <small class="text-muted">{{ $activity['time']->diffForHumans() }}</small>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No Recent Activities</h6>
                        <p class="text-muted small">Your recent learning activities will appear here.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2 text-primary"></i>Learning Progress</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-semibold">Overall Progress</span>
                        <span class="text-primary fw-bold">{{ $progressData['overall_progress'] }}%</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-primary" style="width: {{ $progressData['overall_progress'] }}%"></div>
                    </div>
                </div>

                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-semibold">CPD Credits</span>
                        <span class="text-success fw-bold">{{ $stats['cpd_credits'] }}/60</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-success" style="width: {{ $progressData['cpd_progress'] }}%"></div>
                    </div>
                </div>

                <div class="mb-0">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-semibold">Learning Hours</span>
                        <span class="text-info fw-bold">{{ $stats['learning_hours'] }}h</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-info" style="width: {{ $progressData['learning_hours_progress'] }}%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
@endsection
