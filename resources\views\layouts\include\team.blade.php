<div class="container-xxl py-5">
    <div class="container">
        <div class="text-center wow fadeInUp" data-wow-delay="0.1s">
            <h6 class="text-secondary text-uppercase">Our Team</h6>
            <h1 class="mb-5">Meet Our Expert Team</h1>
        </div>
        <div class="row g-4">
            @forelse($teamMembers as $index => $member)
                <div class="col-lg-3 col-md-6 wow fadeInUp" data-wow-delay="{{ 0.1 + ($index * 0.2) }}s">
                    <div class="team-item">
                        <div class="position-relative overflow-hidden">
                            @if($member->image)
                                <img class="img-fluid" src="{{ asset('storage/' . $member->image) }}" alt="{{ $member->name }}" style="height: 300px; width: 100%; object-fit: cover;">
                            @else
                                <div class="d-flex align-items-center justify-content-center bg-light text-primary" style="height: 300px;">
                                    <i class="fas fa-user fa-4x"></i>
                                </div>
                            @endif
                        </div>
                        <div class="team-text">
                            <div class="bg-light">
                                <h5 class="fw-bold mb-0">{{ $member->name }}</h5>
                                <small>{{ $member->position }}</small>
                            </div>
                            <div class="bg-primary">
                                @if($member->facebook)
                                    <a class="btn btn-square mx-1" href="{{ $member->facebook }}" target="_blank">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                @endif
                                @if($member->twitter)
                                    <a class="btn btn-square mx-1" href="{{ $member->twitter }}" target="_blank">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                @endif
                                @if($member->linkedin)
                                    <a class="btn btn-square mx-1" href="{{ $member->linkedin }}" target="_blank">
                                        <i class="fab fa-linkedin-in"></i>
                                    </a>
                                @endif
                                @if($member->email)
                                    <a class="btn btn-square mx-1" href="mailto:{{ $member->email }}">
                                        <i class="fas fa-envelope"></i>
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <!-- Fallback content if no team members are available -->
                <div class="col-lg-3 col-md-6 wow fadeInUp" data-wow-delay="0.1s">
                    <div class="team-item">
                        <div class="position-relative overflow-hidden">
                            <img class="img-fluid" src="{{ asset('img/team-1.jpg') }}" alt="Team Member">
                        </div>
                        <div class="team-text">
                            <div class="bg-light">
                                <h5 class="fw-bold mb-0">Dr. Sarah Johnson</h5>
                                <small>Chief Medical Officer</small>
                            </div>
                            <div class="bg-primary">
                                <a class="btn btn-square mx-1" href="#"><i class="fab fa-facebook-f"></i></a>
                                <a class="btn btn-square mx-1" href="#"><i class="fab fa-twitter"></i></a>
                                <a class="btn btn-square mx-1" href="#"><i class="fab fa-linkedin-in"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 wow fadeInUp" data-wow-delay="0.3s">
                    <div class="team-item">
                        <div class="position-relative overflow-hidden">
                            <img class="img-fluid" src="{{ asset('img/team-2.jpg') }}" alt="Team Member">
                        </div>
                        <div class="team-text">
                            <div class="bg-light">
                                <h5 class="fw-bold mb-0">Dr. Michael Chen</h5>
                                <small>Director of Education</small>
                            </div>
                            <div class="bg-primary">
                                <a class="btn btn-square mx-1" href="#"><i class="fab fa-facebook-f"></i></a>
                                <a class="btn btn-square mx-1" href="#"><i class="fab fa-twitter"></i></a>
                                <a class="btn btn-square mx-1" href="#"><i class="fab fa-linkedin-in"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            @endforelse
        </div>
    </div>
</div>
