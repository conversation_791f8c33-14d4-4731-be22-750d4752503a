<?php

namespace App\Http\Controllers;

use App\Models\CarouselSlide;
use App\Models\Service;
use App\Models\TeamMember;
use App\Models\Testimonial;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the homepage with dynamic content
     */
    public function index()
    {
        // Fetch active content ordered by their order field
        $carouselSlides = CarouselSlide::active()->ordered()->get();
        $services = Service::active()->ordered()->take(6)->get(); // Limit to 6 services for homepage
        $teamMembers = TeamMember::active()->ordered()->take(4)->get(); // Limit to 4 team members for homepage
        $testimonials = Testimonial::active()->ordered()->get();

        return view('pages.home', compact(
            'carouselSlides',
            'services', 
            'teamMembers',
            'testimonials'
        ));
    }
}
