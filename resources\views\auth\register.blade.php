@extends('layouts.auth')

@section('title', 'Register')

@section('content')
    <div class="text-center mb-4">
        <h3 class="text-dark mb-2">Create Account</h3>
        <p class="text-muted">Join Virtual CME Hub for professional development</p>
    </div>

    <form method="POST" action="{{ route('register') }}">
        @csrf

        <div class="row">
            <!-- Full Name -->
            <div class="col-12 mb-3">
                <label for="name" class="form-label text-dark fw-medium">{{ __('Full Name') }} <span class="text-danger">*</span></label>
                <input id="name" type="text" class="form-control @error('name') is-invalid @enderror"
                       name="name" value="{{ old('name') }}" required autofocus autocomplete="name"
                       placeholder="Enter your full name">
                @error('name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- Email Address -->
            <div class="col-12 mb-3">
                <label for="email" class="form-label text-dark fw-medium">{{ __('Professional Email') }} <span class="text-danger">*</span></label>
                <input id="email" type="email" class="form-control @error('email') is-invalid @enderror"
                       name="email" value="{{ old('email') }}" required autocomplete="username"
                       placeholder="Enter your professional email address">
                @error('email')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- KMLTTB No and National ID in same row -->
            <div class="col-md-6 mb-3">
                <label for="kmlttb_no" class="form-label text-dark fw-medium">{{ __('KMLTTB No') }}</label>
                <input id="kmlttb_no" type="text" class="form-control @error('kmlttb_no') is-invalid @enderror"
                       name="kmlttb_no" value="{{ old('kmlttb_no') }}" autocomplete="kmlttb_no"
                       placeholder="Enter KMLTTB number">
                @error('kmlttb_no')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="col-md-6 mb-3">
                <label for="national_id" class="form-label text-dark fw-medium">{{ __('National ID') }}</label>
                <input id="national_id" type="text" class="form-control @error('national_id') is-invalid @enderror"
                       name="national_id" value="{{ old('national_id') }}" autocomplete="national_id"
                       placeholder="Enter national ID number">
                @error('national_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- Password -->
            <div class="col-md-6 mb-3">
                <label for="password" class="form-label text-dark fw-medium">{{ __('Password') }} <span class="text-danger">*</span></label>
                <input id="password" type="password" class="form-control @error('password') is-invalid @enderror"
                       name="password" required autocomplete="new-password"
                       placeholder="Create a strong password">
                @error('password')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <!-- Confirm Password -->
            <div class="col-md-6 mb-3">
                <label for="password_confirmation" class="form-label text-dark fw-medium">{{ __('Confirm Password') }} <span class="text-danger">*</span></label>
                <input id="password_confirmation" type="password" class="form-control @error('password_confirmation') is-invalid @enderror"
                       name="password_confirmation" required autocomplete="new-password"
                       placeholder="Confirm your password">
                @error('password_confirmation')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <!-- Data Collection Consent -->
        <div class="mb-4">
            <div class="card border-0 bg-light">
                <div class="card-body p-3">
                    <h6 class="card-title text-primary mb-3">
                        <i class="fas fa-shield-alt me-2"></i>Data Collection & Privacy Consent
                    </h6>

                    <div class="form-check mb-3">
                        <input class="form-check-input @error('data_consent') is-invalid @enderror"
                               type="checkbox" id="data_consent" name="data_consent"
                               value="1" {{ old('data_consent') ? 'checked' : '' }} required>
                        <label class="form-check-label text-sm" for="data_consent">
                            <strong>I consent to data collection and processing</strong> <span class="text-danger">*</span>
                            <br>
                            <small class="text-muted">
                                I agree to allow Virtual CME Hub to collect, store, and process my personal and professional data
                                for the purpose of providing medical education services, tracking CPD credits, and improving
                                the learning experience.
                            </small>
                        </label>
                        @error('data_consent')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            By registering, you acknowledge that you have read and agree to our
                            <a href="#" class="auth-link">Privacy Policy</a> and
                            <a href="#" class="auth-link">Terms of Service</a>.
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="d-grid mb-4">
            <button type="submit" class="btn btn-auth">
                <i class="fas fa-user-plus me-2"></i>{{ __('Create Account') }}
            </button>
        </div>

        <!-- Links -->
        <div class="text-center">
            <p class="mb-0">
                {{ __('Already have an account?') }}
                <a href="{{ route('login') }}" class="auth-link fw-medium">
                    {{ __('Sign In') }}
                </a>
            </p>
        </div>
    </form>

    <style>
        .auth-card {
            max-width: 550px !important;
        }

        .form-check-label.text-sm {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .card {
            border-radius: 10px;
        }

        .form-check-input:checked {
            background-color: var(--medical-green);
            border-color: var(--medical-green);
        }

        .form-check-input:focus {
            border-color: var(--medical-green);
            box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
        }
    </style>
@endsection
