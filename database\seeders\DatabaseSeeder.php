<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🌱 Starting VCH Learning Management System database seeding...');

        // Check if we're in production
        if (app()->environment('production')) {
            $this->command->warn('⚠️  You are running this in production!');
            if (!$this->command->confirm('Do you really want to seed the database in production?')) {
                $this->command->info('Seeding cancelled.');
                return;
            }
        }

        // Run seeders based on environment
        if (app()->environment(['local', 'testing'])) {
            $this->command->info('🔧 Running development/testing seeders...');

            // Demo data for development
            $this->call([
                DemoDataSeeder::class,
                ContentSeeder::class,
                VCHSeeder::class, // Keep existing seeder
            ]);

            // Additional test data if requested
            if ($this->command->confirm('Would you like to create additional test data for testing?', false)) {
                $this->call([
                    TestDataSeeder::class,
                ]);
            }
        } else {
            $this->command->info('🏭 Running production seeders...');

            // Only essential data for production
            $this->call([
                DemoDataSeeder::class,
                ContentSeeder::class,
                VCHSeeder::class,
            ]);
        }

        $this->command->info('✅ Database seeding completed successfully!');
        $this->displaySeedingSummary();
    }

    /**
     * Display a summary of what was seeded
     */
    private function displaySeedingSummary(): void
    {
        $this->command->info('');
        $this->command->info('📊 Seeding Summary:');

        $summary = [
            ['Users', \App\Models\User::count()],
        ];

        // Add counts for models that exist
        $models = [
            'Courses' => '\App\Models\Course',
            'Live Sessions' => '\App\Models\LiveSession',
            'Carousel Slides' => '\App\Models\CarouselSlide',
            'Services' => '\App\Models\Service',
            'Team Members' => '\App\Models\TeamMember',
            'Testimonials' => '\App\Models\Testimonial',
            'FAQs' => '\App\Models\FAQ',
            'Support Tickets' => '\App\Models\SupportTicket',
        ];

        foreach ($models as $name => $class) {
            if (class_exists($class)) {
                $summary[] = [$name, $class::count()];
            }
        }

        $this->command->table(['Model', 'Count'], $summary);

        $this->command->info('');
        $this->command->info('🔑 Default Login Credentials:');
        $this->command->info('Admin: <EMAIL> / password');
        $this->command->info('Instructor: <EMAIL> / password');
        $this->command->info('User: <EMAIL> / password');
        $this->command->info('');
    }
}
