@extends('layouts.dashboard')

@section('page-title', 'Create Testimonial')

@section('content')
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Create Testimonial</h2>
                    <p class="text-muted mb-0">Add a new client testimonial</p>
                </div>
                <div>
                    <a href="{{ route('admin.content.testimonials.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Testimonials
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <form action="{{ route('admin.content.testimonials.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-user me-2 text-primary"></i>Client Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Client Name -->
                                <div class="mb-3">
                                    <label for="client_name" class="form-label">Client Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('client_name') is-invalid @enderror" 
                                           id="client_name" name="client_name" value="{{ old('client_name') }}" required>
                                    @error('client_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Client Position -->
                                <div class="mb-3">
                                    <label for="client_position" class="form-label">Position/Title</label>
                                    <input type="text" class="form-control @error('client_position') is-invalid @enderror" 
                                           id="client_position" name="client_position" value="{{ old('client_position') }}">
                                    @error('client_position')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <!-- Client Company -->
                                <div class="mb-3">
                                    <label for="client_company" class="form-label">Company/Organization</label>
                                    <input type="text" class="form-control @error('client_company') is-invalid @enderror" 
                                           id="client_company" name="client_company" value="{{ old('client_company') }}">
                                    @error('client_company')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Rating -->
                                <div class="mb-3">
                                    <label for="rating" class="form-label">Rating <span class="text-danger">*</span></label>
                                    <select class="form-select @error('rating') is-invalid @enderror" id="rating" name="rating" required>
                                        <option value="">Select rating...</option>
                                        <option value="5" {{ old('rating') == '5' ? 'selected' : '' }}>5 Stars - Excellent</option>
                                        <option value="4" {{ old('rating') == '4' ? 'selected' : '' }}>4 Stars - Very Good</option>
                                        <option value="3" {{ old('rating') == '3' ? 'selected' : '' }}>3 Stars - Good</option>
                                        <option value="2" {{ old('rating') == '2' ? 'selected' : '' }}>2 Stars - Fair</option>
                                        <option value="1" {{ old('rating') == '1' ? 'selected' : '' }}>1 Star - Poor</option>
                                    </select>
                                    @error('rating')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Client Image -->
                        <div class="mb-3">
                            <label for="client_image" class="form-label">Client Photo</label>
                            <input type="file" class="form-control @error('client_image') is-invalid @enderror" 
                                   id="client_image" name="client_image" accept="image/*">
                            @error('client_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Upload a photo of the client (optional). Recommended size: 300x300px</div>
                        </div>

                        <!-- Image Preview -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <img id="previewImg" src="" alt="Preview" class="img-fluid rounded-circle" style="max-height: 150px; max-width: 150px;">
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-quote-left me-2 text-primary"></i>Testimonial Content</h5>
                    </div>
                    <div class="card-body">
                        <!-- Testimonial Text with Rich Text Editor -->
                        <x-rich-text-editor 
                            name="testimonial" 
                            label="Testimonial" 
                            :required="true"
                            :value="old('testimonial')"
                            toolbar="simple"
                            :height="250"
                            help="Enter the client's testimonial. You can use basic formatting like bold, italic, and lists."
                        />
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-cog me-2 text-primary"></i>Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Order -->
                                <div class="mb-3">
                                    <label for="order" class="form-label">Display Order <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                           id="order" name="order" value="{{ old('order', 0) }}" min="0" required>
                                    @error('order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Lower numbers appear first</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Status -->
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">Status</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active (testimonial will be visible on the website)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.content.testimonials.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Cancel
                    </a>
                    <div>
                        <button type="submit" name="action" value="save" class="btn btn-outline-primary me-2">
                            <i class="fas fa-save me-1"></i>Save Draft
                        </button>
                        <button type="submit" name="action" value="publish" class="btn btn-primary">
                            <i class="fas fa-check me-1"></i>Save & Publish
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Preview Panel -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm sticky-top" style="top: 2rem;">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-eye me-2 text-primary"></i>Live Preview</h5>
                </div>
                <div class="card-body">
                    <div id="testimonialPreview" class="text-center">
                        <div class="text-muted">
                            <i class="fas fa-quote-left fa-3x mb-3"></i>
                            <p>Fill out the form to see a preview of the testimonial</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image preview
    document.getElementById('client_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('previewImg').src = e.target.result;
                document.getElementById('imagePreview').style.display = 'block';
                updatePreview();
            };
            reader.readAsDataURL(file);
        }
    });

    // Live preview update
    function updatePreview() {
        const clientName = document.getElementById('client_name').value;
        const clientPosition = document.getElementById('client_position').value;
        const clientCompany = document.getElementById('client_company').value;
        const rating = document.getElementById('rating').value;
        const testimonial = window.getRichTextContent ? window.getRichTextContent('testimonial') : document.querySelector('[name="testimonial"]').value;
        const previewImg = document.getElementById('previewImg').src;

        let previewHtml = '';
        
        if (previewImg && previewImg !== window.location.href) {
            previewHtml += `<img src="${previewImg}" class="rounded-circle mb-3" style="width: 80px; height: 80px; object-fit: cover;">`;
        }
        
        if (testimonial) {
            previewHtml += `<blockquote class="blockquote mb-3">"${testimonial}"</blockquote>`;
        }
        
        if (clientName) {
            previewHtml += `<h6 class="mb-1">${clientName}</h6>`;
        }
        
        if (clientPosition || clientCompany) {
            let subtitle = '';
            if (clientPosition) subtitle += clientPosition;
            if (clientPosition && clientCompany) subtitle += ', ';
            if (clientCompany) subtitle += clientCompany;
            previewHtml += `<p class="text-muted small mb-2">${subtitle}</p>`;
        }
        
        if (rating) {
            const stars = '★'.repeat(parseInt(rating)) + '☆'.repeat(5 - parseInt(rating));
            previewHtml += `<div class="text-warning">${stars}</div>`;
        }

        if (!previewHtml) {
            previewHtml = `
                <div class="text-muted">
                    <i class="fas fa-quote-left fa-3x mb-3"></i>
                    <p>Fill out the form to see a preview of the testimonial</p>
                </div>
            `;
        }

        document.getElementById('testimonialPreview').innerHTML = previewHtml;
    }

    // Form field listeners for live preview
    ['client_name', 'client_position', 'client_company', 'rating'].forEach(id => {
        document.getElementById(id).addEventListener('input', updatePreview);
        document.getElementById(id).addEventListener('change', updatePreview);
    });

    // Listen for rich text editor changes
    document.addEventListener('richTextAutoSave', function(e) {
        if (e.detail.editorId.includes('testimonial')) {
            updatePreview();
        }
    });

    // Form submission handling
    document.querySelector('form').addEventListener('submit', function(e) {
        const action = e.submitter.value;
        if (action === 'save') {
            document.getElementById('is_active').checked = false;
        } else if (action === 'publish') {
            document.getElementById('is_active').checked = true;
        }
    });
});
</script>
@endpush
@endsection
