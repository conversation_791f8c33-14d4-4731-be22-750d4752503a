<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;

class SystemSettingsController extends Controller
{
    /**
     * Display system settings.
     */
    public function index()
    {
        $settings = $this->getSystemSettings();
        $systemInfo = $this->getSystemInfo();
        $cacheInfo = $this->getCacheInfo();
        $storageInfo = $this->getStorageInfo();

        return view('admin.settings.index', compact('settings', 'systemInfo', 'cacheInfo', 'storageInfo'));
    }

    /**
     * Update system settings.
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'app_name' => 'required|string|max:255',
            'app_description' => 'nullable|string|max:500',
            'contact_email' => 'required|email|max:255',
            'support_email' => 'required|email|max:255',
            'maintenance_mode' => 'boolean',
            'user_registration' => 'boolean',
            'email_verification' => 'boolean',
            'max_file_size' => 'required|integer|min:1|max:100',
            'session_lifetime' => 'required|integer|min:1|max:1440',
            'timezone' => 'required|string',
        ]);

        foreach ($validated as $key => $value) {
            $this->updateSetting($key, $value);
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'System settings updated successfully.');
    }

    /**
     * Clear application cache.
     */
    public function clearCache(Request $request)
    {
        $type = $request->get('type', 'all');

        try {
            switch ($type) {
                case 'config':
                    Artisan::call('config:clear');
                    $message = 'Configuration cache cleared successfully.';
                    break;
                case 'route':
                    Artisan::call('route:clear');
                    $message = 'Route cache cleared successfully.';
                    break;
                case 'view':
                    Artisan::call('view:clear');
                    $message = 'View cache cleared successfully.';
                    break;
                case 'application':
                    Cache::flush();
                    $message = 'Application cache cleared successfully.';
                    break;
                case 'all':
                default:
                    Artisan::call('config:clear');
                    Artisan::call('route:clear');
                    Artisan::call('view:clear');
                    Cache::flush();
                    $message = 'All caches cleared successfully.';
                    break;
            }

            return redirect()->route('admin.settings.index')
                ->with('success', $message);
        } catch (\Exception $e) {
            return redirect()->route('admin.settings.index')
                ->with('error', 'Failed to clear cache: ' . $e->getMessage());
        }
    }

    /**
     * Optimize application.
     */
    public function optimize()
    {
        try {
            Artisan::call('config:cache');
            Artisan::call('route:cache');
            Artisan::call('view:cache');

            return redirect()->route('admin.settings.index')
                ->with('success', 'Application optimized successfully.');
        } catch (\Exception $e) {
            return redirect()->route('admin.settings.index')
                ->with('error', 'Failed to optimize application: ' . $e->getMessage());
        }
    }

    /**
     * Toggle maintenance mode.
     */
    public function toggleMaintenance()
    {
        try {
            if (app()->isDownForMaintenance()) {
                Artisan::call('up');
                $message = 'Maintenance mode disabled.';
            } else {
                Artisan::call('down', ['--render' => 'errors::503']);
                $message = 'Maintenance mode enabled.';
            }

            return redirect()->route('admin.settings.index')
                ->with('success', $message);
        } catch (\Exception $e) {
            return redirect()->route('admin.settings.index')
                ->with('error', 'Failed to toggle maintenance mode: ' . $e->getMessage());
        }
    }

    /**
     * Get system settings.
     */
    private function getSystemSettings()
    {
        return [
            'app_name' => config('app.name', 'VCH Learning'),
            'app_description' => $this->getSetting('app_description', 'Professional Healthcare Education Platform'),
            'contact_email' => $this->getSetting('contact_email', '<EMAIL>'),
            'support_email' => $this->getSetting('support_email', '<EMAIL>'),
            'maintenance_mode' => app()->isDownForMaintenance(),
            'user_registration' => $this->getSetting('user_registration', true),
            'email_verification' => $this->getSetting('email_verification', true),
            'max_file_size' => $this->getSetting('max_file_size', 10),
            'session_lifetime' => config('session.lifetime', 120),
            'timezone' => config('app.timezone', 'UTC'),
        ];
    }

    /**
     * Get system information.
     */
    private function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_connection' => config('database.default'),
            'cache_driver' => config('cache.default'),
            'session_driver' => config('session.driver'),
            'queue_driver' => config('queue.default'),
            'mail_driver' => config('mail.default'),
            'environment' => app()->environment(),
            'debug_mode' => config('app.debug'),
            'app_url' => config('app.url'),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
        ];
    }

    /**
     * Get cache information.
     */
    private function getCacheInfo()
    {
        $cacheSize = 0;
        $cacheFiles = 0;

        try {
            if (config('cache.default') === 'file') {
                $cachePath = storage_path('framework/cache/data');
                if (is_dir($cachePath)) {
                    $files = new \RecursiveIteratorIterator(
                        new \RecursiveDirectoryIterator($cachePath)
                    );
                    
                    foreach ($files as $file) {
                        if ($file->isFile()) {
                            $cacheSize += $file->getSize();
                            $cacheFiles++;
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // Ignore errors
        }

        return [
            'driver' => config('cache.default'),
            'size' => $this->formatBytes($cacheSize),
            'files' => $cacheFiles,
        ];
    }

    /**
     * Get storage information.
     */
    private function getStorageInfo()
    {
        $storageSize = 0;
        $storageFiles = 0;

        try {
            $storagePath = storage_path('app/public');
            if (is_dir($storagePath)) {
                $files = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($storagePath)
                );
                
                foreach ($files as $file) {
                    if ($file->isFile()) {
                        $storageSize += $file->getSize();
                        $storageFiles++;
                    }
                }
            }
        } catch (\Exception $e) {
            // Ignore errors
        }

        return [
            'size' => $this->formatBytes($storageSize),
            'files' => $storageFiles,
            'disk_free_space' => $this->formatBytes(disk_free_space(storage_path())),
            'disk_total_space' => $this->formatBytes(disk_total_space(storage_path())),
        ];
    }

    /**
     * Get a setting value.
     */
    private function getSetting($key, $default = null)
    {
        return Cache::get("setting.{$key}", $default);
    }

    /**
     * Update a setting value.
     */
    private function updateSetting($key, $value)
    {
        Cache::forever("setting.{$key}", $value);
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
