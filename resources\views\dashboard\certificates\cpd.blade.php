@extends('layouts.dashboard')

@section('page-title', 'CPD Credits')

@section('content')
<div class="dashboard-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">CPD Credits Tracking</h2>
                    <p class="text-muted mb-0">Monitor your Continuing Professional Development credits</p>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <form method="GET" class="d-flex align-items-center">
                        <label for="year" class="form-label me-2 mb-0">Year:</label>
                        <select name="year" id="year" class="form-select me-2" onchange="this.form.submit()">
                            @foreach($availableYears as $availableYear)
                                <option value="{{ $availableYear }}" {{ $year == $availableYear ? 'selected' : '' }}>
                                    {{ $availableYear }}
                                </option>
                            @endforeach
                        </select>
                    </form>

                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i>Export Report
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{{ route('dashboard.certificates.report', ['year' => $year, 'format' => 'pdf']) }}">
                                    <i class="fas fa-file-pdf me-2 text-danger"></i>Download PDF
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ route('dashboard.certificates.report', ['year' => $year, 'format' => 'csv']) }}">
                                    <i class="fas fa-file-csv me-2 text-success"></i>Download CSV
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-certificate text-primary fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-primary mb-1">{{ $stats['total_credits_year'] }}</h3>
                    <p class="text-muted mb-0">Total Credits ({{ $year }})</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-graduation-cap text-success fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-success mb-1">{{ $stats['course_credits'] }}</h3>
                    <p class="text-muted mb-0">Course Credits</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-video text-info fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-info mb-1">{{ $stats['session_credits'] }}</h3>
                    <p class="text-muted mb-0">Session Credits</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-award text-warning fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-warning mb-1">{{ $stats['total_certificates_year'] }}</h3>
                    <p class="text-muted mb-0">Certificates Earned</p>
                </div>
            </div>
        </div>
    </div>

    <!-- CPD Progress -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2 text-primary"></i>CPD Progress {{ $year }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Annual Target: 60 Credits</h6>
                            <div class="progress mb-3" style="height: 20px;">
                                <div class="progress-bar bg-primary"
                                     style="width: {{ min(100, ($stats['total_credits_year'] / 60) * 100) }}%">
                                    {{ $stats['total_credits_year'] }}/60
                                </div>
                            </div>
                            <small class="text-muted">
                                {{ 60 - $stats['total_credits_year'] > 0 ? (60 - $stats['total_credits_year']) . ' credits remaining' : 'Target achieved!' }}
                            </small>
                        </div>
                        <div class="col-md-6">
                            <h6>Credits by Type</h6>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Course Credits:</span>
                                <span class="fw-bold text-success">{{ $stats['course_credits'] }}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Session Credits:</span>
                                <span class="fw-bold text-info">{{ $stats['session_credits'] }}</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <span class="fw-bold">Total:</span>
                                <span class="fw-bold text-primary">{{ $stats['total_credits_year'] }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Breakdown -->
    @if($cpdByMonth->isNotEmpty())
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-calendar-alt me-2 text-primary"></i>Monthly Breakdown</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($cpdByMonth as $month => $credits)
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="text-center p-3 border rounded">
                                <h6 class="mb-1">{{ \Carbon\Carbon::createFromFormat('Y-m', $month)->format('M Y') }}</h6>
                                <h4 class="text-primary mb-0">{{ $credits }}</h4>
                                <small class="text-muted">credits</small>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Certificates List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-list me-2 text-primary"></i>Certificates Earned in {{ $year }}</h5>
                </div>
                <div class="card-body">
                    @forelse($certificates as $certificate)
                    <div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-{{ $certificate->certificate_type === 'course' ? 'success' : 'info' }} bg-opacity-10 rounded-circle p-2 me-3">
                                <i class="fas fa-{{ $certificate->certificate_type === 'course' ? 'graduation-cap' : 'video' }} text-{{ $certificate->certificate_type === 'course' ? 'success' : 'info' }}"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">{{ $certificate->title }}</h6>
                                <small class="text-muted">
                                    {{ $certificate->certificate_type === 'course' ? 'Course Certificate' : 'Session Certificate' }}
                                    • Issued: {{ $certificate->issued_at->format('M j, Y') }}
                                </small>
                            </div>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-primary fs-6">{{ $certificate->cpd_credits }} Credits</span>
                            <br>
                            <small class="text-muted">{{ $certificate->certificate_number }}</small>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-5">
                        <i class="fas fa-certificate fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No certificates earned in {{ $year }}</h5>
                        <p class="text-muted">Complete courses or attend live sessions to earn CPD credits.</p>
                        <a href="{{ route('dashboard.courses.index') }}" class="btn btn-primary me-2">
                            <i class="fas fa-graduation-cap me-1"></i>Browse Courses
                        </a>
                        <a href="{{ route('dashboard.live-sessions.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-video me-1"></i>View Sessions
                        </a>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // You can add charts here if needed
    console.log('CPD Credits page loaded');
});
</script>
@endpush
