<nav class="sidebar-nav">
    <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionKey => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="nav-section">
            <div class="nav-section-title"><?php echo e($section['title']); ?></div>
            
            <?php $__currentLoopData = $section['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="nav-item">
                    <a href="<?php echo e(route($item['route'])); ?>" 
                       class="nav-link <?php echo e($item['active'] ? 'active' : ''); ?>"
                       <?php if(isset($item['target'])): ?> target="<?php echo e($item['target']); ?>" <?php endif; ?>>
                        <i class="<?php echo e($item['icon']); ?>"></i>
                        <span class="nav-text"><?php echo e($item['text']); ?></span>
                        
                        <?php if(isset($item['badge']) && $item['badge']): ?>
                            <span class="nav-badge"><?php echo e($item['badge']); ?></span>
                        <?php endif; ?>
                    </a>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    

    
    <!-- Logout -->
    <div class="nav-section">
        <div class="nav-item">
            <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline w-100">
                <?php echo csrf_field(); ?>
                <button type="submit" class="nav-link logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="nav-text">Logout</span>
                </button>
            </form>
        </div>
    </div>
</nav>

<?php if (! $__env->hasRenderedOnce('57f757e1-b2d5-4acb-b816-5030a3fe9d29')): $__env->markAsRenderedOnce('57f757e1-b2d5-4acb-b816-5030a3fe9d29'); ?>
<?php $__env->startPush('styles'); ?>
<style>
.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 8px;
    margin: 0.25rem 1rem;
    transition: all 0.3s ease;
    position: relative;
    font-weight: 500;
}

.nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
}

.nav-link.active {
    color: white;
    background: linear-gradient(135deg, var(--medical-green), #34a853);
    box-shadow: 0 4px 12px rgba(46, 139, 87, 0.3);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: -1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: white;
    border-radius: 2px;
}

.nav-link i {
    font-size: 16px;
    margin-right: 12px;
    color: inherit;
    width: 20px;
    text-align: center;
}

.nav-text {
    flex: 1;
    font-size: 14px;
}

.nav-badge {
    background: #ff4757;
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

.logout-btn {
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.logout-btn:hover {
    background: rgba(255, 87, 87, 0.1);
    color: #ff5757;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .nav-link {
        margin: 0.2rem 0.5rem;
        padding: 0.6rem 1rem;
    }
    
    .nav-link i {
        margin-right: 10px;
        font-size: 14px;
    }
    
    .nav-text {
        font-size: 13px;
    }
}

/* Smooth transitions for sidebar toggle */
.sidebar {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.show {
    transform: translateX(0);
}

/* Enhanced hover effects */
.nav-link::after {
    content: '';
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%) translateX(10px);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid currentColor;
    opacity: 0;
    transition: all 0.3s ease;
}

.nav-link:hover::after {
    opacity: 0.6;
    transform: translateY(-50%) translateX(0);
}

.nav-link.active::after {
    display: none;
}

/* Section spacing */
.nav-section:not(:last-child) {
    margin-bottom: 1.5rem;
}

.nav-section-title {
    color: rgba(255, 255, 255, 0.6);
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0 1.5rem 0.5rem;
    margin-bottom: 0.5rem;
}

/* Loading states */
.nav-link.loading {
    pointer-events: none;
    opacity: 0.6;
}

.nav-link.loading::before {
    content: '';
    position: absolute;
    left: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}
</style>
<?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\VCH\resources\views/components/navigation.blade.php ENDPATH**/ ?>