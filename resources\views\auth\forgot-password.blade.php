@extends('layouts.auth')

@section('title', 'Forgot Password')

@section('content')
    <div class="text-center mb-4">
        <h3 class="text-dark mb-2">Forgot Password?</h3>
        <p class="text-muted">No problem! Enter your email and we'll send you a reset link</p>
    </div>

    <!-- Session Status -->
    @if (session('status'))
        <div class="alert alert-success mb-4">
            {{ session('status') }}
        </div>
    @endif

    <form method="POST" action="{{ route('password.email') }}">
        @csrf

        <!-- Email Address -->
        <div class="mb-4">
            <label for="email" class="form-label text-dark fw-medium">{{ __('Email Address') }}</label>
            <input id="email" type="email" class="form-control @error('email') is-invalid @enderror"
                   name="email" value="{{ old('email') }}" required autofocus
                   placeholder="Enter your email address">
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- Submit Button -->
        <div class="d-grid mb-4">
            <button type="submit" class="btn btn-auth">
                <i class="fas fa-paper-plane me-2"></i>{{ __('Send Reset Link') }}
            </button>
        </div>

        <!-- Links -->
        <div class="text-center">
            <p class="mb-0">
                {{ __('Remember your password?') }}
                <a href="{{ route('login') }}" class="auth-link fw-medium">
                    {{ __('Back to Login') }}
                </a>
            </p>
        </div>
    </form>
@endsection
