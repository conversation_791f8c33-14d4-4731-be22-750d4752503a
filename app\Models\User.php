<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Storage;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'permissions',
        'first_name',
        'last_name',
        'phone',
        'bio',
        'avatar',
        'profession',
        'organization',
        'job_title',
        'license_number',
        'date_of_birth',
        'gender',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'country',
        'medical_specialty',
        'years_of_experience',
        'certifications',
        'interests',
        'is_active',
        'last_login_at',
        'timezone',
        'notification_preferences',
        'preferences',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'permissions' => 'array',
            'date_of_birth' => 'date',
            'certifications' => 'array',
            'interests' => 'array',
            'is_active' => 'boolean',
            'last_login_at' => 'datetime',
            'notification_preferences' => 'array',
        ];
    }

    /**
     * Role-based authorization methods
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    public function isInstructor(): bool
    {
        return $this->role === 'instructor';
    }

    public function isModerator(): bool
    {
        return $this->role === 'moderator';
    }

    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    public function hasPermission(string $permission): bool
    {
        if ($this->isAdmin()) {
            return true; // Admins have all permissions
        }

        $permissions = $this->permissions ?? [];
        return in_array($permission, $permissions);
    }

    public function canManageContent(): bool
    {
        return $this->isAdmin() || $this->hasPermission('manage_content');
    }

    public function canManageUsers(): bool
    {
        return $this->isAdmin() || $this->hasPermission('manage_users');
    }

    public function canManageCourses(): bool
    {
        return $this->isAdmin() || $this->isInstructor() || $this->hasPermission('manage_courses');
    }

    /**
     * Profile helper methods
     */
    public function getFullNameAttribute(): string
    {
        if ($this->first_name && $this->last_name) {
            return $this->first_name . ' ' . $this->last_name;
        }
        return $this->name;
    }

    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return Storage::url($this->avatar);
        }

        // Generate initials-based avatar
        $initials = strtoupper(substr($this->name, 0, 1));
        if ($this->first_name && $this->last_name) {
            $initials = strtoupper(substr($this->first_name, 0, 1) . substr($this->last_name, 0, 1));
        }

        return "https://ui-avatars.com/api/?name={$initials}&background=2E8B57&color=fff&size=200";
    }

    public function getFormattedAddressAttribute(): string
    {
        $address = [];
        if ($this->address_line_1) $address[] = $this->address_line_1;
        if ($this->address_line_2) $address[] = $this->address_line_2;
        if ($this->city) $address[] = $this->city;
        if ($this->state) $address[] = $this->state;
        if ($this->postal_code) $address[] = $this->postal_code;
        if ($this->country) $address[] = $this->country;

        return implode(', ', $address);
    }

    /**
     * Relationships
     */
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    public function sessionRegistrations()
    {
        return $this->hasMany(SessionRegistration::class);
    }

    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    public function supportTickets()
    {
        return $this->hasMany(SupportTicket::class);
    }

    public function courses()
    {
        return $this->belongsToMany(Course::class, 'enrollments')
                    ->withPivot(['enrolled_at', 'started_at', 'completed_at', 'progress_percentage', 'status', 'progress_data'])
                    ->withTimestamps();
    }

    public function liveSessions()
    {
        return $this->belongsToMany(LiveSession::class, 'session_registrations')
                    ->withPivot(['registered_at', 'attended_at', 'attendance_duration', 'status', 'feedback'])
                    ->withTimestamps();
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByRole($query, $role)
    {
        return $query->where('role', $role);
    }

    public function scopeAdmins($query)
    {
        return $query->where('role', 'admin');
    }

    public function scopeInstructors($query)
    {
        return $query->where('role', 'instructor');
    }

    /**
     * Statistics methods
     */
    public function getTotalCpdCredits(): int
    {
        return $this->certificates()->where('status', 'active')->sum('cpd_credits');
    }

    public function getCompletedCoursesCount(): int
    {
        return $this->enrollments()->where('status', 'completed')->count();
    }

    public function getAttendedSessionsCount(): int
    {
        return $this->sessionRegistrations()->where('status', 'attended')->count();
    }

    public function getTotalLearningHours(): float
    {
        $courseHours = $this->enrollments()
            ->where('status', 'completed')
            ->with('course')
            ->get()
            ->sum(function ($enrollment) {
                return $enrollment->course->duration_hours ?? 0;
            });

        $sessionHours = $this->sessionRegistrations()
            ->where('status', 'attended')
            ->with('liveSession')
            ->get()
            ->sum(function ($registration) {
                return ($registration->liveSession->duration_minutes ?? 0) / 60;
            });

        return $courseHours + $sessionHours;
    }
}
