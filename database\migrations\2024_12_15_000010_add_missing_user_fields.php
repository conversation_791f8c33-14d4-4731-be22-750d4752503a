<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add job_title field if it doesn't exist
            if (!Schema::hasColumn('users', 'job_title')) {
                $table->string('job_title')->nullable()->after('organization');
            }
            
            // Add preferences field if it doesn't exist
            if (!Schema::hasColumn('users', 'preferences')) {
                $table->json('preferences')->nullable()->after('notification_preferences');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'job_title')) {
                $table->dropColumn('job_title');
            }
            
            if (Schema::hasColumn('users', 'preferences')) {
                $table->dropColumn('preferences');
            }
        });
    }
};
