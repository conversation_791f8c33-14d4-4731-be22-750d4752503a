<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TeamMember>
 */
class TeamMemberFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $teamMembers = [
            [
                'name' => 'Dr. <PERSON>',
                'position' => 'Chief Medical Officer',
                'bio' => 'Dr. <PERSON> brings over 20 years of experience in emergency medicine and medical education. She has authored numerous publications and is a recognized expert in healthcare training.',
                'email' => '<EMAIL>',
                'linkedin' => 'https://linkedin.com/in/sarah<PERSON><PERSON>son',
                'twitter' => 'https://twitter.com/drsarah<PERSON><PERSON><PERSON>',
            ],
            [
                'name' => 'Dr. <PERSON>',
                'position' => 'Director of Education',
                'bio' => 'Specializing in pediatric emergency medicine with extensive teaching experience. Dr. <PERSON> has developed innovative training programs used worldwide.',
                'email' => '<EMAIL>',
                'linkedin' => 'https://linkedin.com/in/michael<PERSON>',
            ],
            [
                'name' => 'Dr. <PERSON>',
                'position' => 'Quality Assurance Director',
                'bio' => 'Expert in healthcare quality management with a focus on patient safety and clinical excellence. She ensures all our training meets the highest standards.',
                'email' => '<EMAIL>',
                'linkedin' => 'https://linkedin.com/in/lisarodriguez',
            ],
            [
                'name' => 'Dr. Robert Kim',
                'position' => 'Technology Integration Specialist',
                'bio' => 'Leading the integration of cutting-edge technology in healthcare education. Dr. Kim specializes in e-learning platforms and virtual training environments.',
                'email' => '<EMAIL>',
                'linkedin' => 'https://linkedin.com/in/robertkim',
            ],
        ];

        $memberData = $this->faker->randomElement($teamMembers);
        
        return [
            'name' => $memberData['name'],
            'position' => $memberData['position'],
            'bio' => $memberData['bio'],
            'image' => 'team/team-' . $this->faker->numberBetween(1, 4) . '.jpg',
            'email' => $memberData['email'],
            'phone' => $this->faker->optional(0.7)->phoneNumber(),
            'linkedin' => $memberData['linkedin'] ?? null,
            'twitter' => $memberData['twitter'] ?? null,
            'facebook' => $this->faker->optional(0.3)->url(),
            'order' => $this->faker->numberBetween(1, 10),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'created_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the team member is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the team member is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the team member has all social links.
     */
    public function withAllSocialLinks(): static
    {
        return $this->state(function (array $attributes) {
            $name = strtolower(explode(' ', $attributes['name'])[1] ?? $this->faker->lastName());
            return [
                'linkedin' => 'https://linkedin.com/in/' . $name . rand(100, 999),
                'twitter' => 'https://twitter.com/' . $name . rand(100, 999),
                'facebook' => 'https://facebook.com/' . $name . rand(100, 999),
            ];
        });
    }
}