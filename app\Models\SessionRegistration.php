<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SessionRegistration extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'live_session_id',
        'registered_at',
        'attended_at',
        'attendance_duration',
        'status',
        'feedback',
    ];

    protected $casts = [
        'registered_at' => 'datetime',
        'attended_at' => 'datetime',
        'feedback' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function liveSession()
    {
        return $this->belongsTo(LiveSession::class);
    }

    public function scopeAttended($query)
    {
        return $query->where('status', 'attended');
    }

    public function scopeMissed($query)
    {
        return $query->where('status', 'missed');
    }

    public function scopeRegistered($query)
    {
        return $query->where('status', 'registered');
    }

    public function markAsAttended($duration = null)
    {
        $this->status = 'attended';
        $this->attended_at = now();
        if ($duration) {
            $this->attendance_duration = $duration;
        }
        $this->save();
    }

    public function markAsMissed()
    {
        $this->status = 'missed';
        $this->save();
    }

    public function addFeedback($feedbackData)
    {
        $this->feedback = array_merge($this->feedback ?? [], $feedbackData);
        $this->save();
    }

    public function getAttendancePercentage()
    {
        if (!$this->attendance_duration || !$this->liveSession->duration_minutes) {
            return 0;
        }

        return min(100, ($this->attendance_duration / $this->liveSession->duration_minutes) * 100);
    }
}
