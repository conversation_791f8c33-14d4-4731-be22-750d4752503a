<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'read_at',
        'priority',
        'channel',
    ];

    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function isRead()
    {
        return !is_null($this->read_at);
    }

    public function markAsRead()
    {
        if (!$this->read_at) {
            $this->update(['read_at' => now()]);
        }
    }

    public function getIconAttribute()
    {
        $icons = [
            'session_reminder' => 'fas fa-video',
            'course_completion' => 'fas fa-graduation-cap',
            'certificate_ready' => 'fas fa-certificate',
            'system_update' => 'fas fa-cog',
            'payment_reminder' => 'fas fa-credit-card',
            'general' => 'fas fa-bell',
        ];

        return $icons[$this->type] ?? $icons['general'];
    }

    public function getColorAttribute()
    {
        $colors = [
            'session_reminder' => 'primary',
            'course_completion' => 'success',
            'certificate_ready' => 'warning',
            'system_update' => 'info',
            'payment_reminder' => 'danger',
            'general' => 'secondary',
        ];

        return $colors[$this->type] ?? $colors['general'];
    }

    public static function createForUser($userId, $type, $title, $message, $data = null, $priority = 'normal')
    {
        return static::create([
            'user_id' => $userId,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'priority' => $priority,
        ]);
    }
}
