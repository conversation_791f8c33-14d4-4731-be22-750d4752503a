<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ZoomService
{
    protected $baseUrl;
    protected $accountId;
    protected $clientId;
    protected $clientSecret;

    public function __construct()
    {
        $this->baseUrl = 'https://api.zoom.us/v2';
        $this->accountId = config('services.zoom.account_id');
        $this->clientId = config('services.zoom.client_id');
        $this->clientSecret = config('services.zoom.client_secret');
    }

    /**
     * Get Server-to-Server OAuth access token
     */
    protected function getAccessToken()
    {
        $cacheKey = 'zoom_access_token';
        
        return Cache::remember($cacheKey, 3500, function () {
            try {
                $response = Http::asForm()->post('https://zoom.us/oauth/token', [
                    'grant_type' => 'account_credentials',
                    'account_id' => $this->accountId,
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    return $data['access_token'];
                }

                Log::error('Zoom OAuth failed', ['response' => $response->body()]);
                throw new \Exception('Failed to get Zoom access token');

            } catch (\Exception $e) {
                Log::error('Zoom OAuth error', ['error' => $e->getMessage()]);
                throw $e;
            }
        });
    }

    /**
     * Make authenticated request to Zoom API
     */
    protected function makeRequest($method, $endpoint, $data = [])
    {
        try {
            $token = $this->getAccessToken();
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json',
            ])->$method($this->baseUrl . $endpoint, $data);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('Zoom API request failed', [
                'method' => $method,
                'endpoint' => $endpoint,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('Zoom API error', [
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Create a Zoom meeting
     */
    public function createMeeting($meetingData)
    {
        $defaultSettings = [
            'host_video' => true,
            'participant_video' => false,
            'cn_meeting' => false,
            'in_meeting' => false,
            'join_before_host' => false,
            'mute_upon_entry' => true,
            'watermark' => false,
            'use_pmi' => false,
            'approval_type' => 0, // 0=auto approve, 1=manual approve, 2=no registration required
            'registration_type' => 1,
            'audio' => 'both',
            'auto_recording' => 'none',
            'enforce_login' => false,
            'waiting_room' => false,
            'allow_multiple_devices' => true,
        ];

        $meeting = [
            'topic' => $meetingData['topic'],
            'type' => $meetingData['type'] ?? 2, // 2 = Scheduled meeting
            'start_time' => $meetingData['start_time'],
            'duration' => $meetingData['duration'],
            'timezone' => $meetingData['timezone'] ?? 'Africa/Nairobi', // Default to East Africa Time
            'agenda' => $meetingData['agenda'] ?? '',
            'settings' => array_merge($defaultSettings, $meetingData['settings'] ?? []),
        ];

        return $this->makeRequest('post', '/users/me/meetings', $meeting);
    }

    /**
     * Update a Zoom meeting
     */
    public function updateMeeting($meetingId, $meetingData)
    {
        return $this->makeRequest('patch', "/meetings/{$meetingId}", $meetingData);
    }

    /**
     * Delete a Zoom meeting
     */
    public function deleteMeeting($meetingId)
    {
        return $this->makeRequest('delete', "/meetings/{$meetingId}");
    }

    /**
     * Get meeting details
     */
    public function getMeeting($meetingId)
    {
        return $this->makeRequest('get', "/meetings/{$meetingId}");
    }

    /**
     * End a meeting
     */
    public function endMeeting($meetingId)
    {
        return $this->makeRequest('patch', "/meetings/{$meetingId}/status", [
            'action' => 'end'
        ]);
    }

    /**
     * Get meeting participants
     */
    public function getMeetingParticipants($meetingId)
    {
        return $this->makeRequest('get', "/past_meetings/{$meetingId}/participants");
    }

    /**
     * Get meeting recordings
     */
    public function getMeetingRecordings($meetingId)
    {
        return $this->makeRequest('get', "/meetings/{$meetingId}/recordings");
    }

    /**
     * Create a webinar (for larger audiences)
     */
    public function createWebinar($webinarData)
    {
        $defaultSettings = [
            'host_video' => true,
            'panelists_video' => false,
            'practice_session' => false,
            'hd_video' => true,
            'approval_type' => 0, // 0=auto approve, 1=manual approve, 2=no registration required
            'registration_type' => 1,
            'audio' => 'both',
            'auto_recording' => 'none',
            'enforce_login' => false,
            'registrants_email_notification' => true,
            'close_registration' => false,
            'show_share_button' => true,
            'allow_multiple_devices' => true,
        ];

        $webinar = [
            'topic' => $webinarData['topic'],
            'type' => $webinarData['type'] ?? 5, // 5 = Webinar
            'start_time' => $webinarData['start_time'],
            'duration' => $webinarData['duration'],
            'timezone' => $webinarData['timezone'] ?? 'Africa/Nairobi', // Default to East Africa Time
            'agenda' => $webinarData['agenda'] ?? '',
            'settings' => array_merge($defaultSettings, $webinarData['settings'] ?? []),
        ];

        return $this->makeRequest('post', '/users/me/webinars', $webinar);
    }

    /**
     * Update a webinar
     */
    public function updateWebinar($webinarId, $webinarData)
    {
        return $this->makeRequest('patch', "/webinars/{$webinarId}", $webinarData);
    }

    /**
     * Delete a webinar
     */
    public function deleteWebinar($webinarId)
    {
        return $this->makeRequest('delete', "/webinars/{$webinarId}");
    }

    /**
     * Get webinar details
     */
    public function getWebinar($webinarId)
    {
        return $this->makeRequest('get', "/webinars/{$webinarId}");
    }

    /**
     * Get webinar participants
     */
    public function getWebinarParticipants($webinarId)
    {
        return $this->makeRequest('get', "/past_webinars/{$webinarId}/participants");
    }

    /**
     * Test Zoom connection
     */
    public function testConnection()
    {
        try {
            $token = $this->getAccessToken();
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->get($this->baseUrl . '/users/me');

            return $response->successful();

        } catch (\Exception $e) {
            Log::error('Zoom connection test failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Get user account details
     */
    public function getUserAccount()
    {
        return $this->makeRequest('get', '/users/me');
    }
}
