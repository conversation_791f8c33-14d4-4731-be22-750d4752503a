<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Course;
use App\Models\LiveSession;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds for testing purposes.
     */
    public function run(): void
    {
        $this->command->info('Creating test data...');

        // Create test users
        $this->command->info('Creating test users...');
        $users = User::factory(50)->create();
        
        // Create some admin users
        User::factory(3)->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create some instructor users
        User::factory(5)->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        // Create test courses
        $this->command->info('Creating test courses...');
        
        // Create published courses
        Course::factory(20)->published()->create();
        
        // Create featured courses
        Course::factory(5)->featured()->published()->create();
        
        // Create free courses
        Course::factory(8)->free()->published()->create();
        
        // Create premium courses
        Course::factory(10)->premium()->published()->create();
        
        // Create draft courses
        Course::factory(5)->create(['status' => 'draft']);

        // Create test live sessions
        $this->command->info('Creating test live sessions...');
        
        // Create scheduled sessions
        LiveSession::factory(15)->scheduled()->create();
        
        // Create completed sessions
        LiveSession::factory(10)->completed()->create();
        
        // Create featured sessions
        LiveSession::factory(5)->featured()->scheduled()->create();
        
        // Create free sessions
        LiveSession::factory(8)->free()->scheduled()->create();
        
        // Create sessions with CPD credits
        LiveSession::factory(12)->withCpdCredits()->scheduled()->create();

        $this->command->info('Test data created successfully!');
        $this->command->info('Created:');
        $this->command->info('- ' . User::count() . ' users');
        $this->command->info('- ' . Course::count() . ' courses');
        $this->command->info('- ' . LiveSession::count() . ' live sessions');
    }
}
