<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $filter = $request->get('filter', 'all');
        $type = $request->get('type');
        $priority = $request->get('priority');
        $search = $request->get('search');

        $query = Notification::where('user_id', $user->id);

        // Apply main filter
        switch ($filter) {
            case 'unread':
                $query->whereNull('read_at');
                break;
            case 'read':
                $query->whereNotNull('read_at');
                break;
            case 'important':
                $query->whereIn('priority', ['high', 'urgent']);
                break;
        }

        // Apply type filter
        if ($type) {
            $query->byType($type);
        }

        // Apply priority filter
        if ($priority) {
            $query->byPriority($priority);
        }

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $notifications = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get filter options
        $types = Notification::where('user_id', $user->id)
            ->distinct()
            ->pluck('type')
            ->map(function ($type) {
                return [
                    'value' => $type,
                    'label' => ucfirst(str_replace('_', ' ', $type))
                ];
            });

        $priorities = ['low', 'normal', 'high', 'urgent'];

        // Get statistics
        $stats = [
            'total_count' => Notification::where('user_id', $user->id)->count(),
            'unread_count' => Notification::where('user_id', $user->id)->whereNull('read_at')->count(),
            'read_count' => Notification::where('user_id', $user->id)->whereNotNull('read_at')->count(),
            'important_count' => Notification::where('user_id', $user->id)
                ->whereIn('priority', ['high', 'urgent'])
                ->count(),
        ];

        return view('dashboard.notifications.index', compact(
            'notifications',
            'stats',
            'filter',
            'type',
            'priority',
            'search',
            'types',
            'priorities'
        ));
    }

    public function markAsRead(Notification $notification)
    {
        // Check if user owns this notification
        if ($notification->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to notification.');
        }

        if (!$notification->read_at) {
            $notification->update(['read_at' => now()]);
        }

        return response()->json(['success' => true]);
    }

    public function markAllAsRead()
    {
        $user = Auth::user();

        Notification::where('user_id', $user->id)
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        return redirect()->back()->with('success', 'All notifications marked as read.');
    }

    public function destroy(Notification $notification)
    {
        // Check if user owns this notification
        if ($notification->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to notification.');
        }

        $notification->delete();

        return redirect()->back()->with('success', 'Notification deleted.');
    }

    public function getUnreadCount()
    {
        $user = Auth::user();
        $count = Notification::where('user_id', $user->id)->whereNull('read_at')->count();

        return response()->json(['count' => $count]);
    }

    public function getRecent()
    {
        $user = Auth::user();
        $notifications = Notification::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        return response()->json(['notifications' => $notifications]);
    }

    public function preferences()
    {
        $user = Auth::user();
        $preferences = $user->notification_preferences ?? [
            'session_reminders' => true,
            'course_updates' => true,
            'certificate_notifications' => true,
            'system_updates' => false,
            'payment_reminders' => true,
            'marketing_emails' => false,
            'email_notifications' => true,
            'push_notifications' => true,
        ];

        return view('dashboard.notifications.preferences', compact('preferences'));
    }

    public function updatePreferences(Request $request)
    {
        $user = Auth::user();

        $preferences = [
            'session_reminders' => $request->boolean('session_reminders'),
            'course_updates' => $request->boolean('course_updates'),
            'certificate_notifications' => $request->boolean('certificate_notifications'),
            'system_updates' => $request->boolean('system_updates'),
            'payment_reminders' => $request->boolean('payment_reminders'),
            'marketing_emails' => $request->boolean('marketing_emails'),
            'email_notifications' => $request->boolean('email_notifications'),
            'push_notifications' => $request->boolean('push_notifications'),
        ];

        $user->update(['notification_preferences' => $preferences]);

        return redirect()->back()->with('success', 'Notification preferences updated successfully.');
    }

    public function bulkAction(Request $request)
    {
        $user = Auth::user();
        $action = $request->input('action');
        $notificationIds = $request->input('notification_ids', []);

        if (empty($notificationIds)) {
            return redirect()->back()->with('error', 'No notifications selected.');
        }

        $query = Notification::where('user_id', $user->id)
            ->whereIn('id', $notificationIds);

        switch ($action) {
            case 'mark_read':
                $query->whereNull('read_at')->update(['read_at' => now()]);
                $message = 'Selected notifications marked as read.';
                break;
            case 'mark_unread':
                $query->update(['read_at' => null]);
                $message = 'Selected notifications marked as unread.';
                break;
            case 'delete':
                $query->delete();
                $message = 'Selected notifications deleted.';
                break;
            default:
                return redirect()->back()->with('error', 'Invalid action.');
        }

        return redirect()->back()->with('success', $message);
    }
}
