<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => 'content',
    'id' => null,
    'value' => '',
    'label' => 'Content',
    'required' => false,
    'height' => 300,
    'toolbar' => 'default',
    'placeholder' => '',
    'help' => null,
    'class' => '',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => 'content',
    'id' => null,
    'value' => '',
    'label' => 'Content',
    'required' => false,
    'height' => 300,
    'toolbar' => 'default',
    'placeholder' => '',
    'help' => null,
    'class' => '',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php
    $editorId = $id ?? $name . '_' . uniqid();
    
    $toolbars = [
        'default' => 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
        'simple' => 'bold italic | bullist numlist | link | removeformat',
        'full' => 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image media table | code fullscreen | help',
        'minimal' => 'bold italic | link | removeformat',
    ];
    
    $selectedToolbar = $toolbars[$toolbar] ?? $toolbars['default'];
?>

<div class="mb-3">
    <?php if($label): ?>
        <label for="<?php echo e($editorId); ?>" class="form-label">
            <?php echo e($label); ?>

            <?php if($required): ?>
                <span class="text-danger">*</span>
            <?php endif; ?>
        </label>
    <?php endif; ?>
    
    <textarea 
        id="<?php echo e($editorId); ?>" 
        name="<?php echo e($name); ?>" 
        class="form-control rich-text-editor <?php echo e($class); ?> <?php $__errorArgs = [$name];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
        <?php if($placeholder): ?> placeholder="<?php echo e($placeholder); ?>" <?php endif; ?>
        <?php if($required): ?> required <?php endif; ?>
        data-toolbar="<?php echo e($selectedToolbar); ?>"
        data-height="<?php echo e($height); ?>"
    ><?php echo e(old($name, $value)); ?></textarea>
    
    <?php $__errorArgs = [$name];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
        <div class="invalid-feedback"><?php echo e($message); ?></div>
    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    
    <?php if($help): ?>
        <div class="form-text"><?php echo e($help); ?></div>
    <?php endif; ?>
</div>

<?php if (! $__env->hasRenderedOnce('5ed79bf9-4bca-4ced-b0cf-f7225f756751')): $__env->markAsRenderedOnce('5ed79bf9-4bca-4ced-b0cf-f7225f756751'); ?>
<?php $__env->startPush('scripts'); ?>
<!-- TinyMCE -->
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all rich text editors
    document.querySelectorAll('.rich-text-editor').forEach(function(textarea) {
        const toolbar = textarea.dataset.toolbar || 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help';
        const height = parseInt(textarea.dataset.height) || 300;
        
        tinymce.init({
            target: textarea,
            height: height,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons'
            ],
            toolbar: toolbar,
            // Image upload configuration
            images_upload_url: '<?php echo e(route("admin.media.upload")); ?>',
            images_upload_handler: function (blobInfo, success, failure, progress) {
                const xhr = new XMLHttpRequest();
                xhr.withCredentials = false;
                xhr.open('POST', '<?php echo e(route("admin.media.upload")); ?>');

                xhr.upload.onprogress = function (e) {
                    progress(e.loaded / e.total * 100);
                };

                xhr.onload = function() {
                    if (xhr.status === 403) {
                        failure('HTTP Error: ' + xhr.status, { remove: true });
                        return;
                    }

                    if (xhr.status < 200 || xhr.status >= 300) {
                        failure('HTTP Error: ' + xhr.status);
                        return;
                    }

                    const json = JSON.parse(xhr.responseText);

                    if (!json || typeof json.location != 'string') {
                        failure('Invalid JSON: ' + xhr.responseText);
                        return;
                    }

                    success(json.location);
                };

                xhr.onerror = function () {
                    failure('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
                };

                const formData = new FormData();
                formData.append('file', blobInfo.blob(), blobInfo.filename());
                formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);

                xhr.send(formData);
            },
            // File browser configuration
            file_picker_callback: function(callback, value, meta) {
                openMediaBrowser(callback, value, meta);
            },
            content_style: `
                body { 
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; 
                    font-size: 14px; 
                    line-height: 1.6;
                    color: #333;
                }
                h1, h2, h3, h4, h5, h6 { 
                    margin-top: 1rem; 
                    margin-bottom: 0.5rem; 
                    font-weight: 600;
                }
                p { margin-bottom: 1rem; }
                ul, ol { margin-bottom: 1rem; padding-left: 2rem; }
                blockquote { 
                    border-left: 4px solid #e9ecef; 
                    padding-left: 1rem; 
                    margin: 1rem 0; 
                    font-style: italic;
                }
                code { 
                    background-color: #f8f9fa; 
                    padding: 0.2rem 0.4rem; 
                    border-radius: 0.25rem; 
                    font-family: 'Monaco', 'Consolas', monospace;
                }
                table { 
                    border-collapse: collapse; 
                    width: 100%; 
                    margin-bottom: 1rem;
                }
                table td, table th { 
                    border: 1px solid #dee2e6; 
                    padding: 0.5rem; 
                }
                table th { 
                    background-color: #f8f9fa; 
                    font-weight: 600;
                }
            `,
            branding: false,
            promotion: false,
            resize: 'vertical',
            elementpath: false,
            statusbar: true,
            paste_data_images: true,
            paste_as_text: false,
            paste_webkit_styles: 'none',
            paste_retain_style_properties: 'color font-size font-family font-weight text-decoration text-align',
            image_advtab: true,
            image_caption: true,
            image_description: false,
            image_dimensions: false,
            image_title: true,
            link_title: false,
            link_target_list: [
                {title: 'Same window', value: ''},
                {title: 'New window', value: '_blank'}
            ],
            table_default_attributes: {
                'class': 'table table-bordered'
            },
            table_default_styles: {},
            formats: {
                alignleft: {selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'text-start'},
                aligncenter: {selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'text-center'},
                alignright: {selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'text-end'},
                alignjustify: {selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', classes: 'text-justify'}
            },
            style_formats: [
                {title: 'Headings', items: [
                    {title: 'Heading 1', format: 'h1'},
                    {title: 'Heading 2', format: 'h2'},
                    {title: 'Heading 3', format: 'h3'},
                    {title: 'Heading 4', format: 'h4'},
                    {title: 'Heading 5', format: 'h5'},
                    {title: 'Heading 6', format: 'h6'}
                ]},
                {title: 'Inline', items: [
                    {title: 'Bold', format: 'bold'},
                    {title: 'Italic', format: 'italic'},
                    {title: 'Underline', format: 'underline'},
                    {title: 'Strikethrough', format: 'strikethrough'},
                    {title: 'Code', format: 'code'}
                ]},
                {title: 'Blocks', items: [
                    {title: 'Paragraph', format: 'p'},
                    {title: 'Blockquote', format: 'blockquote'},
                    {title: 'Div', format: 'div'},
                    {title: 'Pre', format: 'pre'}
                ]},
                {title: 'Alignment', items: [
                    {title: 'Left', format: 'alignleft'},
                    {title: 'Center', format: 'aligncenter'},
                    {title: 'Right', format: 'alignright'},
                    {title: 'Justify', format: 'alignjustify'}
                ]}
            ],
            setup: function(editor) {
                // Custom button for Bootstrap components
                editor.ui.registry.addMenuButton('bootstrap', {
                    text: 'Bootstrap',
                    fetch: function(callback) {
                        var items = [
                            {
                                type: 'menuitem',
                                text: 'Alert - Info',
                                onAction: function() {
                                    editor.insertContent('<div class="alert alert-info" role="alert">This is an info alert.</div>');
                                }
                            },
                            {
                                type: 'menuitem',
                                text: 'Alert - Warning',
                                onAction: function() {
                                    editor.insertContent('<div class="alert alert-warning" role="alert">This is a warning alert.</div>');
                                }
                            },
                            {
                                type: 'menuitem',
                                text: 'Alert - Success',
                                onAction: function() {
                                    editor.insertContent('<div class="alert alert-success" role="alert">This is a success alert.</div>');
                                }
                            },
                            {
                                type: 'menuitem',
                                text: 'Button - Primary',
                                onAction: function() {
                                    editor.insertContent('<a href="#" class="btn btn-primary">Button Text</a>');
                                }
                            },
                            {
                                type: 'menuitem',
                                text: 'Button - Secondary',
                                onAction: function() {
                                    editor.insertContent('<a href="#" class="btn btn-secondary">Button Text</a>');
                                }
                            }
                        ];
                        callback(items);
                    }
                });
                
                // Auto-save functionality
                let autoSaveTimer;
                editor.on('change', function() {
                    clearTimeout(autoSaveTimer);
                    autoSaveTimer = setTimeout(function() {
                        // Trigger custom event for auto-save
                        const event = new CustomEvent('richTextAutoSave', {
                            detail: {
                                editorId: editor.id,
                                content: editor.getContent()
                            }
                        });
                        document.dispatchEvent(event);
                    }, 2000); // Auto-save after 2 seconds of inactivity
                });
                
                // Word count display
                editor.on('init', function() {
                    const wordCountElement = document.createElement('div');
                    wordCountElement.className = 'text-muted small mt-1';
                    wordCountElement.id = editor.id + '_wordcount';
                    editor.getContainer().appendChild(wordCountElement);
                    
                    function updateWordCount() {
                        const wordCount = editor.plugins.wordcount.getCount();
                        wordCountElement.textContent = `Words: ${wordCount}`;
                    }
                    
                    editor.on('keyup change', updateWordCount);
                    updateWordCount();
                });
            }
        });
    });
    
    // Global function to get content from all editors
    window.getRichTextContent = function(editorId) {
        const editor = tinymce.get(editorId);
        return editor ? editor.getContent() : '';
    };
    
    // Global function to set content in editor
    window.setRichTextContent = function(editorId, content) {
        const editor = tinymce.get(editorId);
        if (editor) {
            editor.setContent(content);
        }
    };
    
    // Form submission handler to sync content
    document.addEventListener('submit', function(e) {
        if (e.target.tagName === 'FORM') {
            tinymce.triggerSave();
        }
    });

    // Media browser functionality
    window.openMediaBrowser = function(callback, value, meta) {
        // Create modal for media browser
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'mediaBrowserModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Media Library</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <select class="form-select" id="mediaTypeFilter">
                                    <option value="all">All Files</option>
                                    <option value="images">Images</option>
                                    <option value="documents">Documents</option>
                                    <option value="videos">Videos</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="mediaUpload" multiple accept="image/*,video/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx">
                                    <button class="btn btn-primary" type="button" onclick="uploadFiles()">Upload</button>
                                </div>
                            </div>
                        </div>
                        <div id="mediaGrid" class="row">
                            <div class="col-12 text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                        <div id="mediaPagination" class="d-flex justify-content-center mt-3"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="selectMediaBtn" disabled>Select</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);

        let selectedFile = null;
        let currentCallback = callback;

        // Load media files
        function loadMedia(type = 'all', page = 1) {
            fetch(`<?php echo e(route('admin.media.browse')); ?>?type=${type}&page=${page}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderMediaGrid(data.files);
                        renderPagination(data.pagination);
                    }
                })
                .catch(error => {
                    console.error('Error loading media:', error);
                    document.getElementById('mediaGrid').innerHTML = '<div class="col-12 text-center text-danger">Error loading media files</div>';
                });
        }

        function renderMediaGrid(files) {
            const grid = document.getElementById('mediaGrid');
            if (files.length === 0) {
                grid.innerHTML = '<div class="col-12 text-center text-muted">No files found</div>';
                return;
            }

            grid.innerHTML = files.map(file => `
                <div class="col-md-3 col-sm-4 col-6 mb-3">
                    <div class="card media-item" data-file='${JSON.stringify(file)}' style="cursor: pointer;">
                        <div class="card-body p-2 text-center">
                            ${file.is_image ?
                                `<img src="${file.url}" class="img-fluid rounded mb-2" style="max-height: 100px; object-fit: cover;">` :
                                `<i class="fas fa-file fa-3x text-muted mb-2"></i>`
                            }
                            <h6 class="card-title small">${file.name}</h6>
                            <small class="text-muted">${formatFileSize(file.size)}</small>
                        </div>
                    </div>
                </div>
            `).join('');

            // Add click handlers
            document.querySelectorAll('.media-item').forEach(item => {
                item.addEventListener('click', function() {
                    // Remove previous selection
                    document.querySelectorAll('.media-item').forEach(i => i.classList.remove('border-primary'));

                    // Select current item
                    this.classList.add('border-primary');
                    selectedFile = JSON.parse(this.dataset.file);
                    document.getElementById('selectMediaBtn').disabled = false;
                });
            });
        }

        function renderPagination(pagination) {
            const paginationEl = document.getElementById('mediaPagination');
            if (pagination.last_page <= 1) {
                paginationEl.innerHTML = '';
                return;
            }

            let paginationHtml = '<nav><ul class="pagination">';

            for (let i = 1; i <= pagination.last_page; i++) {
                paginationHtml += `
                    <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadMedia(document.getElementById('mediaTypeFilter').value, ${i}); return false;">${i}</a>
                    </li>
                `;
            }

            paginationHtml += '</ul></nav>';
            paginationEl.innerHTML = paginationHtml;
        }

        function formatFileSize(bytes) {
            const units = ['B', 'KB', 'MB', 'GB'];
            let size = bytes;
            let unitIndex = 0;

            while (size >= 1024 && unitIndex < units.length - 1) {
                size /= 1024;
                unitIndex++;
            }

            return Math.round(size * 100) / 100 + ' ' + units[unitIndex];
        }

        // Event handlers
        document.getElementById('mediaTypeFilter').addEventListener('change', function() {
            loadMedia(this.value);
        });

        document.getElementById('selectMediaBtn').addEventListener('click', function() {
            if (selectedFile) {
                currentCallback(selectedFile.url, {
                    alt: selectedFile.name,
                    title: selectedFile.name
                });
                bsModal.hide();
            }
        });

        // Upload functionality
        window.uploadFiles = function() {
            const fileInput = document.getElementById('mediaUpload');
            const files = fileInput.files;

            if (files.length === 0) return;

            Array.from(files).forEach(file => {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);

                fetch('<?php echo e(route("admin.media.upload")); ?>', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadMedia(document.getElementById('mediaTypeFilter').value);
                        fileInput.value = '';
                    }
                })
                .catch(error => {
                    console.error('Upload error:', error);
                });
            });
        };

        // Clean up modal when closed
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });

        // Load initial media
        loadMedia();
        bsModal.show();
    };
});
</script>
<?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\VCH\resources\views/components/rich-text-editor.blade.php ENDPATH**/ ?>