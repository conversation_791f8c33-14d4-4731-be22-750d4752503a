<?php $__env->startSection('title', 'Login'); ?>

<?php $__env->startSection('content'); ?>
    <div class="text-center mb-4">
        <h3 class="text-dark mb-2">Welcome Back!</h3>
        <p class="text-muted">Sign in to your account to continue</p>
    </div>

    <!-- Session Status -->
    <?php if(session('status')): ?>
        <div class="alert alert-success mb-4">
            <?php echo e(session('status')); ?>

        </div>
    <?php endif; ?>

    <form method="POST" action="<?php echo e(route('login')); ?>">
        <?php echo csrf_field(); ?>

        <!-- Email Address -->
        <div class="mb-3">
            <label for="email" class="form-label text-dark fw-medium"><?php echo e(__('Email Address')); ?></label>
            <input id="email" type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                   name="email" value="<?php echo e(old('email')); ?>" required autofocus autocomplete="username"
                   placeholder="Enter your email address">
            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <!-- Password -->
        <div class="mb-3">
            <label for="password" class="form-label text-dark fw-medium"><?php echo e(__('Password')); ?></label>
            <input id="password" type="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                   name="password" required autocomplete="current-password"
                   placeholder="Enter your password">
            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <!-- Remember Me -->
        <div class="mb-4">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="remember_me" name="remember">
                <label class="form-check-label text-muted" for="remember_me">
                    <?php echo e(__('Remember me')); ?>

                </label>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="d-grid mb-4">
            <button type="submit" class="btn btn-auth">
                <i class="fas fa-sign-in-alt me-2"></i><?php echo e(__('Sign In')); ?>

            </button>
        </div>

        <!-- Links -->
        <div class="text-center">
            <?php if(Route::has('password.request')): ?>
                <p class="mb-2">
                    <a href="<?php echo e(route('password.request')); ?>" class="auth-link">
                        <i class="fas fa-key me-1"></i><?php echo e(__('Forgot your password?')); ?>

                    </a>
                </p>
            <?php endif; ?>

            <?php if(Route::has('register')): ?>
                <p class="mb-0">
                    <?php echo e(__("Don't have an account?")); ?>

                    <a href="<?php echo e(route('register')); ?>" class="auth-link fw-medium">
                        <?php echo e(__('Create Account')); ?>

                    </a>
                </p>
            <?php endif; ?>
        </div>
    </form>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\VCH\resources\views/auth/login.blade.php ENDPATH**/ ?>