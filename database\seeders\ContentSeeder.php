<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CarouselSlide;
use App\Models\Service;
use App\Models\TeamMember;
use App\Models\Testimonial;
use App\Models\FAQ;
use App\Models\SupportTicket;
use App\Models\User;

class ContentSeeder extends Seeder
{
    /**
     * Run the database seeds for content management.
     */
    public function run(): void
    {
        $this->command->info('Creating content management data...');

        // Create carousel slides using factory
        $this->command->info('Creating carousel slides...');
        CarouselSlide::factory(5)->active()->create();
        CarouselSlide::factory(2)->inactive()->create();

        // Create services
        $this->command->info('Creating services...');
        $services = [
            [
                'title' => 'Professional Training',
                'description' => 'Comprehensive healthcare training programs designed by industry experts to advance your career.',
                'icon' => 'fas fa-graduation-cap',
                'order' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'Certification Programs',
                'description' => 'Earn recognized certifications that meet industry standards and boost your professional credentials.',
                'icon' => 'fas fa-certificate',
                'order' => 2,
                'is_active' => true,
            ],
            [
                'title' => 'Live Interactive Sessions',
                'description' => 'Join live sessions with expert instructors for real-time learning and immediate feedback.',
                'icon' => 'fas fa-video',
                'order' => 3,
                'is_active' => true,
            ],
            [
                'title' => 'CPD Credit Tracking',
                'description' => 'Automatically track your Continuing Professional Development credits and maintain compliance.',
                'icon' => 'fas fa-clock',
                'order' => 4,
                'is_active' => true,
            ],
            [
                'title' => 'Mobile Learning',
                'description' => 'Access your courses anytime, anywhere with our mobile-optimized learning platform.',
                'icon' => 'fas fa-mobile-alt',
                'order' => 5,
                'is_active' => true,
            ],
            [
                'title' => '24/7 Support',
                'description' => 'Get help when you need it with our comprehensive support system and knowledge base.',
                'icon' => 'fas fa-headset',
                'order' => 6,
                'is_active' => true,
            ],
        ];

        foreach ($services as $serviceData) {
            Service::create($serviceData);
        }

        // Create team members
        $this->command->info('Creating team members...');
        $teamMembers = [
            [
                'name' => 'Dr. Sarah Johnson',
                'position' => 'Chief Medical Officer',
                'bio' => 'Dr. Johnson brings over 20 years of experience in emergency medicine and medical education. She has authored numerous publications and is a recognized expert in healthcare training.',
                'image' => 'team/sarah-johnson.jpg',
                'email' => '<EMAIL>',
                'linkedin' => 'https://linkedin.com/in/sarahjohnson',
                'twitter' => 'https://twitter.com/drsarahjohnson',
                'order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Dr. Michael Chen',
                'position' => 'Director of Education',
                'bio' => 'Specializing in pediatric emergency medicine with extensive teaching experience. Dr. Chen has developed innovative training programs used worldwide.',
                'image' => 'team/michael-chen.jpg',
                'email' => '<EMAIL>',
                'linkedin' => 'https://linkedin.com/in/michaelchen',
                'order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Dr. Lisa Rodriguez',
                'position' => 'Quality Assurance Director',
                'bio' => 'Expert in healthcare quality management with a focus on patient safety and clinical excellence. She ensures all our training meets the highest standards.',
                'image' => 'team/lisa-rodriguez.jpg',
                'email' => '<EMAIL>',
                'linkedin' => 'https://linkedin.com/in/lisarodriguez',
                'order' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'Dr. Robert Kim',
                'position' => 'Technology Integration Specialist',
                'bio' => 'Leading the integration of cutting-edge technology in healthcare education. Dr. Kim specializes in e-learning platforms and virtual training environments.',
                'image' => 'team/robert-kim.jpg',
                'email' => '<EMAIL>',
                'linkedin' => 'https://linkedin.com/in/robertkim',
                'order' => 4,
                'is_active' => true,
            ],
        ];

        foreach ($teamMembers as $memberData) {
            TeamMember::create($memberData);
        }

        // Create testimonials
        $this->command->info('Creating testimonials...');
        $testimonials = [
            [
                'client_name' => 'Jennifer Martinez',
                'client_position' => 'Registered Nurse',
                'client_company' => 'City General Hospital',
                'testimonial' => 'The ACLS course was incredibly comprehensive and well-structured. The instructors were knowledgeable and the hands-on practice was invaluable for my professional development.',
                'rating' => 5,
                'client_image' => 'testimonials/jennifer-martinez.jpg',
                'order' => 1,
                'is_active' => true,
            ],
            [
                'client_name' => 'Dr. Robert Kim',
                'client_position' => 'Emergency Physician',
                'client_company' => 'Metro Medical Center',
                'testimonial' => 'Excellent training platform with high-quality content. The certification process was smooth and the credentials are well-recognized in our industry.',
                'rating' => 5,
                'client_image' => 'testimonials/robert-kim.jpg',
                'order' => 2,
                'is_active' => true,
            ],
            [
                'client_name' => 'Maria Santos',
                'client_position' => 'Healthcare Administrator',
                'client_company' => 'Regional Health Network',
                'testimonial' => 'The healthcare management courses have transformed how we approach quality improvement. The practical applications are immediately useful in our daily operations.',
                'rating' => 5,
                'client_image' => 'testimonials/maria-santos.jpg',
                'order' => 3,
                'is_active' => true,
            ],
            [
                'client_name' => 'David Thompson',
                'client_position' => 'Paramedic Supervisor',
                'client_company' => 'Emergency Medical Services',
                'testimonial' => 'The live sessions are fantastic! Being able to interact with instructors and other professionals in real-time makes the learning experience much more engaging.',
                'rating' => 5,
                'client_image' => 'testimonials/david-thompson.jpg',
                'order' => 4,
                'is_active' => true,
            ],
        ];

        foreach ($testimonials as $testimonialData) {
            Testimonial::create($testimonialData);
        }

        // Create FAQs
        $this->command->info('Creating FAQs...');
        $faqs = [
            [
                'question' => 'How do I enroll in a course?',
                'answer' => 'You can enroll in courses by browsing our course catalog and clicking the "Enroll Now" button. Payment is processed securely through our platform, and you\'ll have immediate access to course materials.',
                'category' => 'enrollment',
                'order' => 1,
                'is_active' => true,
            ],
            [
                'question' => 'Are the certifications recognized?',
                'answer' => 'Yes, our certifications are recognized by major healthcare organizations and meet industry standards for continuing education. They are accredited by relevant professional bodies.',
                'category' => 'certification',
                'order' => 2,
                'is_active' => true,
            ],
            [
                'question' => 'Can I access courses on mobile devices?',
                'answer' => 'Absolutely! Our platform is fully responsive and works seamlessly on smartphones, tablets, and desktop computers. You can learn anytime, anywhere.',
                'category' => 'technical',
                'order' => 3,
                'is_active' => true,
            ],
            [
                'question' => 'How do CPD credits work?',
                'answer' => 'CPD (Continuing Professional Development) credits are automatically tracked when you complete eligible courses. You can view your credit history in your dashboard and download certificates as needed.',
                'category' => 'cpd',
                'order' => 4,
                'is_active' => true,
            ],
            [
                'question' => 'What payment methods do you accept?',
                'answer' => 'We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers. All payments are processed securely using industry-standard encryption.',
                'category' => 'billing',
                'order' => 5,
                'is_active' => true,
            ],
            [
                'question' => 'Can I get a refund if I\'m not satisfied?',
                'answer' => 'Yes, we offer a 30-day money-back guarantee for all paid courses. If you\'re not satisfied with your purchase, contact our support team for a full refund.',
                'category' => 'billing',
                'order' => 6,
                'is_active' => true,
            ],
        ];

        foreach ($faqs as $faqData) {
            FAQ::create($faqData);
        }

        // Create sample support tickets using factory
        $this->command->info('Creating support tickets...');
        $users = User::where('role', 'user')->take(10)->get();
        
        if ($users->count() > 0) {
            // Create various types of tickets
            SupportTicket::factory(5)->open()->technical()->create(['user_id' => $users->random()->id]);
            SupportTicket::factory(3)->resolved()->billing()->create(['user_id' => $users->random()->id]);
            SupportTicket::factory(4)->open()->create(['user_id' => $users->random()->id]);
            SupportTicket::factory(2)->urgent()->open()->create(['user_id' => $users->random()->id]);
        }

        $this->command->info('Content management data created successfully!');
        $this->command->info('Created:');
        $this->command->info('- ' . CarouselSlide::count() . ' carousel slides');
        $this->command->info('- ' . Service::count() . ' services');
        $this->command->info('- ' . TeamMember::count() . ' team members');
        $this->command->info('- ' . Testimonial::count() . ' testimonials');
        $this->command->info('- ' . FAQ::count() . ' FAQs');
        $this->command->info('- ' . SupportTicket::count() . ' support tickets');
    }
}
