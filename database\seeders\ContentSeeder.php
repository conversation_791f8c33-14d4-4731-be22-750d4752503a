<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CarouselSlide;
use App\Models\Service;
use App\Models\TeamMember;
use App\Models\Testimonial;
use App\Models\FAQ;
use App\Models\SupportTicket;
use App\Models\User;

class ContentSeeder extends Seeder
{
    /**
     * Run the database seeds for content management.
     */
    public function run(): void
    {
        $this->command->info('Creating content management data...');

        // Create carousel slides using factory
        $this->command->info('Creating carousel slides...');
        CarouselSlide::factory(5)->active()->create();
        CarouselSlide::factory(2)->inactive()->create();

        // Create services
        $this->command->info('Creating services...');
        Service::factory(6)->active()->create();
        Service::factory(2)->inactive()->create();
        
        // Create services with specific order
        $serviceTypes = [
            'Professional Training' => 'fas fa-graduation-cap',
            'Certification Programs' => 'fas fa-certificate',
            'Live Interactive Sessions' => 'fas fa-video',
            'CPD Credit Tracking' => 'fas fa-clock',
            'Mobile Learning' => 'fas fa-mobile-alt',
            '24/7 Support' => 'fas fa-headset',
        ];
        
        $order = 1;
        foreach ($serviceTypes as $title => $icon) {
            Service::factory()->create([
                'title' => $title,
                'icon' => $icon,
                'order' => $order++,
                'is_active' => true,
            ]);
        }

        // Create team members
        $this->command->info('Creating team members...');
        TeamMember::factory(6)->active()->create();
        TeamMember::factory(2)->inactive()->create();
        
        // Create specific team members with defined order
        $teamMemberData = [
            [
                'name' => 'Dr. Sarah Johnson',
                'position' => 'Chief Medical Officer',
                'order' => 1,
            ],
            [
                'name' => 'Dr. Michael Chen',
                'position' => 'Director of Education',
                'order' => 2,
            ],
            [
                'name' => 'Dr. Lisa Rodriguez',
                'position' => 'Quality Assurance Director',
                'order' => 3,
            ],
            [
                'name' => 'Dr. Robert Kim',
                'position' => 'Technology Integration Specialist',
                'order' => 4,
            ],
        ];
        
        foreach ($teamMemberData as $data) {
            TeamMember::factory()->create([
                'name' => $data['name'],
                'position' => $data['position'],
                'order' => $data['order'],
                'is_active' => true,
            ]);
        }

        // Create testimonials
        $this->command->info('Creating testimonials...');
        Testimonial::factory(6)->active()->highRating()->withImage()->create();
        Testimonial::factory(2)->inactive()->create();
        
        // Create specific testimonials with defined order
        $testimonialData = [
            [
                'client_name' => 'Jennifer Martinez',
                'position' => 'Registered Nurse',
                'company' => 'City General Hospital',
                'order' => 1,
            ],
            [
                'client_name' => 'Dr. Robert Kim',
                'position' => 'Emergency Physician',
                'company' => 'Metro Medical Center',
                'order' => 2,
            ],
            [
                'client_name' => 'Maria Santos',
                'position' => 'Healthcare Administrator',
                'company' => 'Regional Health Network',
                'order' => 3,
            ],
            [
                'client_name' => 'David Thompson',
                'position' => 'Paramedic Supervisor',
                'company' => 'Emergency Medical Services',
                'order' => 4,
            ],
        ];
        
        foreach ($testimonialData as $data) {
            Testimonial::factory()->highRating()->withImage()->create([
                'client_name' => $data['client_name'],
                'client_position' => $data['position'],
                'client_company' => $data['company'],
                'order' => $data['order'],
                'is_active' => true,
            ]);
        }

        // Create FAQs
        $this->command->info('Creating FAQs...');
        // Create FAQs for each category
        $categories = ['general', 'courses', 'certification', 'payment', 'technical'];
        
        foreach ($categories as $category) {
            FAQ::factory(2)->active()->category($category)->create();
        }
        
        // Create some inactive FAQs
        FAQ::factory(3)->inactive()->create();
        
        // Create popular FAQs
        FAQ::factory(4)->active()->popular()->create();
        
        // Create specific FAQs with defined order
        $faqData = [
            [
                'question' => 'How do I enroll in a course?',
                'category' => 'courses',
                'order' => 1,
            ],
            [
                'question' => 'Are the certifications recognized?',
                'category' => 'certification',
                'order' => 2,
            ],
            [
                'question' => 'Can I access courses on mobile devices?',
                'category' => 'technical',
                'order' => 3,
            ],
            [
                'question' => 'How do CPD credits work?',
                'category' => 'courses',
                'order' => 4,
            ],
            [
                'question' => 'What payment methods do you accept?',
                'category' => 'payment',
                'order' => 5,
            ],
            [
                'question' => 'Can I get a refund if I\'m not satisfied?',
                'category' => 'payment',
                'order' => 6,
            ],
        ];
        
        foreach ($faqData as $data) {
            FAQ::factory()->active()->create([
                'question' => $data['question'],
                'category' => $data['category'],
                'order' => $data['order'],
                'is_active' => true,
            ]);
        }

        // Create sample support tickets using factory
        $this->command->info('Creating support tickets...');
        $users = User::where('role', 'user')->take(10)->get();
        
        if ($users->count() > 0) {
            // Create various types of tickets
            SupportTicket::factory(5)->open()->technical()->create(['user_id' => $users->random()->id]);
            SupportTicket::factory(3)->resolved()->billing()->create(['user_id' => $users->random()->id]);
            SupportTicket::factory(4)->open()->create(['user_id' => $users->random()->id]);
            SupportTicket::factory(2)->urgent()->open()->create(['user_id' => $users->random()->id]);
        }

        $this->command->info('Content management data created successfully!');
        $this->command->info('Created:');
        $this->command->info('- ' . CarouselSlide::count() . ' carousel slides');
        $this->command->info('- ' . Service::count() . ' services');
        $this->command->info('- ' . TeamMember::count() . ' team members');
        $this->command->info('- ' . Testimonial::count() . ' testimonials');
        $this->command->info('- ' . FAQ::count() . ' FAQs');
        $this->command->info('- ' . SupportTicket::count() . ' support tickets');
    }
}
