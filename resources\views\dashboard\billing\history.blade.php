@extends('layouts.dashboard')

@section('page-title', 'Billing History')

@section('content')
<div class="dashboard-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Billing History</h2>
                    <p class="text-muted mb-0">View and download your billing history</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('dashboard.billing.invoices.download-all') }}" class="btn btn-outline-success">
                        <i class="fas fa-download me-2"></i>Download All
                    </a>
                    <a href="{{ route('dashboard.billing.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Billing
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row align-items-center">
                        <div class="col-md-3">
                            <label for="year" class="form-label">Year</label>
                            <select name="year" id="year" class="form-select" onchange="this.form.submit()">
                                @foreach($availableYears as $availableYear)
                                    <option value="{{ $availableYear }}" {{ $year == $availableYear ? 'selected' : '' }}>
                                        {{ $availableYear }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-9">
                            <div class="d-flex justify-content-end align-items-end h-100">
                                <span class="text-muted">
                                    <i class="fas fa-receipt me-1"></i>
                                    Showing {{ $invoices->count() }} invoices for {{ $year }}
                                </span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Billing History -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-history me-2 text-primary"></i>Invoice History - {{ $year }}</h5>
                </div>
                <div class="card-body">
                    @if($invoices->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Invoice #</th>
                                        <th>Date</th>
                                        <th>Description</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($invoices as $invoice)
                                    <tr>
                                        <td>
                                            <strong>{{ $invoice['id'] }}</strong>
                                        </td>
                                        <td>
                                            {{ \Carbon\Carbon::parse($invoice['date'])->format('M j, Y') }}
                                        </td>
                                        <td>
                                            {{ $invoice['description'] }}
                                        </td>
                                        <td>
                                            <strong>${{ number_format($invoice['amount'], 2) }}</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $invoice['status'] === 'paid' ? 'success' : ($invoice['status'] === 'pending' ? 'warning' : 'danger') }}">
                                                {{ ucfirst($invoice['status']) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('dashboard.billing.invoice.download', $invoice['id']) }}" 
                                                   class="btn btn-outline-primary" title="Download PDF">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <button class="btn btn-outline-secondary" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#invoiceModal"
                                                        data-invoice="{{ json_encode($invoice) }}"
                                                        title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="3">Total for {{ $year }}</th>
                                        <th>${{ number_format($invoices->sum('amount'), 2) }}</th>
                                        <th colspan="2"></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No invoices found for {{ $year }}</h5>
                            <p class="text-muted">Your billing history for this year will appear here.</p>
                            @if($year == now()->year)
                                <a href="{{ route('dashboard.billing.index') }}" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Current Billing
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    @if($invoices->count() > 0)
    <div class="row mt-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-receipt fa-2x text-primary mb-2"></i>
                    <h4 class="text-primary">{{ $invoices->count() }}</h4>
                    <small class="text-muted">Total Invoices</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                    <h4 class="text-success">${{ number_format($invoices->sum('amount'), 2) }}</h4>
                    <small class="text-muted">Total Amount</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x text-info mb-2"></i>
                    <h4 class="text-info">{{ $invoices->where('status', 'paid')->count() }}</h4>
                    <small class="text-muted">Paid Invoices</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-calculator fa-2x text-warning mb-2"></i>
                    <h4 class="text-warning">${{ number_format($invoices->sum('amount') / 12, 2) }}</h4>
                    <small class="text-muted">Average Monthly</small>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Invoice Details Modal -->
<div class="modal fade" id="invoiceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Invoice Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="invoiceDetails">
                    <!-- Invoice details will be populated here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="#" id="downloadInvoiceBtn" class="btn btn-primary">
                    <i class="fas fa-download me-1"></i>Download PDF
                </a>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const invoiceModal = document.getElementById('invoiceModal');
    const invoiceDetails = document.getElementById('invoiceDetails');
    const downloadBtn = document.getElementById('downloadInvoiceBtn');

    invoiceModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const invoice = JSON.parse(button.getAttribute('data-invoice'));
        
        const date = new Date(invoice.date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        invoiceDetails.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Invoice Information</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Invoice #:</strong></td>
                            <td>${invoice.id}</td>
                        </tr>
                        <tr>
                            <td><strong>Date:</strong></td>
                            <td>${date}</td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                <span class="badge bg-${invoice.status === 'paid' ? 'success' : 'warning'}">
                                    ${invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Billing Details</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Description:</strong></td>
                            <td>${invoice.description}</td>
                        </tr>
                        <tr>
                            <td><strong>Amount:</strong></td>
                            <td>$${parseFloat(invoice.amount).toFixed(2)}</td>
                        </tr>
                        <tr>
                            <td><strong>Tax:</strong></td>
                            <td>$0.00</td>
                        </tr>
                        <tr class="table-active">
                            <td><strong>Total:</strong></td>
                            <td><strong>$${parseFloat(invoice.amount).toFixed(2)}</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        `;

        downloadBtn.href = `/dashboard/billing/invoice/${invoice.id}/download`;
    });
});
</script>
@endpush
@endsection
