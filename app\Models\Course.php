<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'content',
        'instructor',
        'instructor_title',
        'duration_hours',
        'cpd_credits',
        'category',
        'level',
        'status',
        'image',
        'price',
        'learning_objectives',
        'start_date',
        'end_date',
    ];

    protected $casts = [
        'learning_objectives' => 'array',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'price' => 'decimal:2',
    ];

    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'enrollments')
                    ->withPivot(['enrolled_at', 'started_at', 'completed_at', 'progress_percentage', 'status', 'progress_data'])
                    ->withTimestamps();
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    public function getProgressForUser($userId)
    {
        $enrollment = $this->enrollments()->where('user_id', $userId)->first();
        return $enrollment ? $enrollment->progress_percentage : 0;
    }

    public function isEnrolledByUser($userId)
    {
        return $this->enrollments()->where('user_id', $userId)->exists();
    }

    public function isCompletedByUser($userId)
    {
        return $this->enrollments()
                    ->where('user_id', $userId)
                    ->where('status', 'completed')
                    ->exists();
    }
}
