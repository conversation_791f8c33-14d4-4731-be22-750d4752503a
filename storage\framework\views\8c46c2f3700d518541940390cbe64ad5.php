<?php $__env->startSection('page-title', 'Edit Carousel Slide'); ?>

<?php $__env->startSection('content'); ?>
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Edit Carousel Slide</h2>
                    <p class="text-muted mb-0">Modify an existing slide in the homepage carousel</p>
                </div>
                <div>
                    <a href="<?php echo e(route('admin.content.carousel.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Carousel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <form action="<?php echo e(route('admin.content.carousel.update', $slide)); ?>" method="POST" enctype="multipart/form-data" id="carouselForm">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-edit me-2 text-primary"></i>Slide Content</h5>
                    </div>
                    <div class="card-body">
                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="title" name="title" value="<?php echo e(old('title', $slide->title)); ?>" required>
                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Subtitle -->
                        <div class="mb-3">
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" class="form-control <?php $__errorArgs = ['subtitle'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="subtitle" name="subtitle" value="<?php echo e(old('subtitle', $slide->subtitle)); ?>">
                            <?php $__errorArgs = ['subtitle'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Description with Rich Text Editor -->
                        <?php if (isset($component)) { $__componentOriginal8910ad36d6e49197b6cc58bf0f8fe11b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8910ad36d6e49197b6cc58bf0f8fe11b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.rich-text-editor','data' => ['name' => 'description','label' => 'Description','required' => true,'value' => old('description', $slide->description),'toolbar' => 'default','height' => 300,'help' => 'Use the rich text editor to format your content with bold, italic, links, images, and more.']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('rich-text-editor'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'description','label' => 'Description','required' => true,'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('description', $slide->description)),'toolbar' => 'default','height' => 300,'help' => 'Use the rich text editor to format your content with bold, italic, links, images, and more.']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8910ad36d6e49197b6cc58bf0f8fe11b)): ?>
<?php $attributes = $__attributesOriginal8910ad36d6e49197b6cc58bf0f8fe11b; ?>
<?php unset($__attributesOriginal8910ad36d6e49197b6cc58bf0f8fe11b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8910ad36d6e49197b6cc58bf0f8fe11b)): ?>
<?php $component = $__componentOriginal8910ad36d6e49197b6cc58bf0f8fe11b; ?>
<?php unset($__componentOriginal8910ad36d6e49197b6cc58bf0f8fe11b); ?>
<?php endif; ?>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-image me-2 text-primary"></i>Slide Image</h5>
                    </div>
                    <div class="card-body">
                        <!-- Current Image -->
                        <?php if($slide->image): ?>
                        <div class="mb-3">
                            <label class="form-label">Current Image</label>
                            <div class="mb-3">
                                <img src="<?php echo e(Storage::url($slide->image)); ?>" alt="<?php echo e($slide->title); ?>" class="img-fluid rounded" style="max-height: 200px;">
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Image Upload -->
                        <div class="mb-3">
                            <label for="image" class="form-label">Replace Image</label>
                            <input type="file" class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="image" name="image" accept="image/*">
                            <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">Upload a new image (JPEG, PNG, GIF). Recommended size: 1920x800px</div>
                        </div>

                        <!-- Image Preview -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <img id="previewImg" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-mouse-pointer me-2 text-primary"></i>Call-to-Action Buttons</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Primary Button -->
                            <div class="col-md-6">
                                <h6 class="text-primary">Primary Button</h6>
                                <div class="mb-3">
                                    <label for="primary_button_text" class="form-label">Button Text</label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['primary_button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="primary_button_text" name="primary_button_text" value="<?php echo e(old('primary_button_text', $slide->primary_button_text)); ?>">
                                    <?php $__errorArgs = ['primary_button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="mb-3">
                                    <label for="primary_button_link" class="form-label">Button Link</label>
                                    <input type="url" class="form-control <?php $__errorArgs = ['primary_button_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="primary_button_link" name="primary_button_link" value="<?php echo e(old('primary_button_link', $slide->primary_button_link)); ?>"
                                           placeholder="https://example.com">
                                    <?php $__errorArgs = ['primary_button_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Secondary Button -->
                            <div class="col-md-6">
                                <h6 class="text-secondary">Secondary Button</h6>
                                <div class="mb-3">
                                    <label for="secondary_button_text" class="form-label">Button Text</label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['secondary_button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="secondary_button_text" name="secondary_button_text" value="<?php echo e(old('secondary_button_text', $slide->secondary_button_text)); ?>">
                                    <?php $__errorArgs = ['secondary_button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="mb-3">
                                    <label for="secondary_button_link" class="form-label">Button Link</label>
                                    <input type="url" class="form-control <?php $__errorArgs = ['secondary_button_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="secondary_button_link" name="secondary_button_link" value="<?php echo e(old('secondary_button_link', $slide->secondary_button_link)); ?>"
                                           placeholder="https://example.com">
                                    <?php $__errorArgs = ['secondary_button_link'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-cog me-2 text-primary"></i>Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Order -->
                                <div class="mb-3">
                                    <label for="order" class="form-label">Display Order <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="order" name="order" value="<?php echo e(old('order', $slide->order)); ?>" min="0" required>
                                    <?php $__errorArgs = ['order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <div class="form-text">Lower numbers appear first</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Status -->
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">Status</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               value="1" <?php echo e(old('is_active', $slide->is_active) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_active">
                                            Active (slide will be visible on the website)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <a href="<?php echo e(route('admin.content.carousel.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Cancel
                    </a>
                    <div>
                        <button type="submit" name="action" value="save" class="btn btn-outline-primary me-2">
                            <i class="fas fa-save me-1"></i>Save Draft
                        </button>
                        <button type="submit" name="action" value="publish" class="btn btn-primary">
                            <i class="fas fa-check me-1"></i>Save & Publish
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Preview Panel -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm sticky-top" style="top: 2rem;">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-eye me-2 text-primary"></i>Live Preview</h5>
                </div>
                <div class="card-body">
                    <div id="slidePreview" class="border rounded p-3 bg-light">
                        <?php if($slide->image): ?>
                            <img src="<?php echo e(Storage::url($slide->image)); ?>" class="img-fluid rounded mb-3" style="max-height: 150px; width: 100%; object-fit: cover;">
                        <?php endif; ?>
                        <?php if($slide->title): ?>
                            <h5 class="text-primary"><?php echo e($slide->title); ?></h5>
                        <?php endif; ?>
                        <?php if($slide->subtitle): ?>
                            <p class="text-muted small"><?php echo e($slide->subtitle); ?></p>
                        <?php endif; ?>
                        <?php if($slide->description): ?>
                            <div class="small"><?php echo $slide->description; ?></div>
                        <?php endif; ?>
                        <?php if($slide->primary_button_text || $slide->secondary_button_text): ?>
                            <div class="mt-3">
                                <?php if($slide->primary_button_text): ?>
                                    <span class="badge bg-primary me-1"><?php echo e($slide->primary_button_text); ?></span>
                                <?php endif; ?>
                                <?php if($slide->secondary_button_text): ?>
                                    <span class="badge bg-outline-secondary"><?php echo e($slide->secondary_button_text); ?></span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {

    // Image preview
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('previewImg').src = e.target.result;
                document.getElementById('imagePreview').style.display = 'block';
                updatePreview();
            };
            reader.readAsDataURL(file);
        }
    });

    // Live preview update
    function updatePreview() {
        const title = document.getElementById('title').value;
        const subtitle = document.getElementById('subtitle').value;
        const description = window.getRichTextContent ? window.getRichTextContent('description') : document.querySelector('[name="description"]').value;
        const primaryBtn = document.getElementById('primary_button_text').value;
        const secondaryBtn = document.getElementById('secondary_button_text').value;
        const previewImg = document.getElementById('previewImg').src;

        let previewHtml = '';
        
        if (previewImg && previewImg !== window.location.href) {
            previewHtml += `<img src="${previewImg}" class="img-fluid rounded mb-3" style="max-height: 150px; width: 100%; object-fit: cover;">`;
        } else if ('<?php echo e($slide->image); ?>') {
            previewHtml += `<img src="<?php echo e(Storage::url($slide->image)); ?>" class="img-fluid rounded mb-3" style="max-height: 150px; width: 100%; object-fit: cover;">`;
        }
        
        if (title) {
            previewHtml += `<h5 class="text-primary">${title}</h5>`;
        }
        
        if (subtitle) {
            previewHtml += `<p class="text-muted small">${subtitle}</p>`;
        }
        
        if (description) {
            previewHtml += `<div class="small">${description}</div>`;
        }
        
        if (primaryBtn || secondaryBtn) {
            previewHtml += '<div class="mt-3">';
            if (primaryBtn) {
                previewHtml += `<span class="badge bg-primary me-1">${primaryBtn}</span>`;
            }
            if (secondaryBtn) {
                previewHtml += `<span class="badge bg-outline-secondary">${secondaryBtn}</span>`;
            }
            previewHtml += '</div>';
        }

        if (!previewHtml) {
            previewHtml = `
                <div class="text-center text-muted">
                    <i class="fas fa-image fa-3x mb-3"></i>
                    <p>Fill out the form to see a preview of your slide</p>
                </div>
            `;
        }

        document.getElementById('slidePreview').innerHTML = previewHtml;
    }

    // Form field listeners for live preview
    ['title', 'subtitle', 'primary_button_text', 'secondary_button_text'].forEach(id => {
        document.getElementById(id).addEventListener('input', updatePreview);
    });

    // Listen for rich text editor changes
    document.addEventListener('richTextAutoSave', function(e) {
        if (e.detail.editorId.includes('description')) {
            updatePreview();
        }
    });

    // Form submission handling
    document.getElementById('carouselForm').addEventListener('submit', function(e) {
        // Sync TinyMCE content
        tinymce.triggerSave();
        
        const action = e.submitter.value;
        if (action === 'save') {
            document.getElementById('is_active').checked = false;
        } else if (action === 'publish') {
            document.getElementById('is_active').checked = true;
        }
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\VCH\resources\views/admin/content/carousel/edit.blade.php ENDPATH**/ ?>