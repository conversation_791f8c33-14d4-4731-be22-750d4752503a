<div class="container-fluid p-0 mb-5">
    <div class="owl-carousel header-carousel position-relative">
        @forelse($carouselSlides as $slide)
            <div class="owl-carousel-item position-relative">
                <img class="img-fluid" src="{{ $slide->image ? asset('storage/' . $slide->image) : asset('img/carousel-1.jpg') }}" alt="{{ $slide->title }}">
                <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center" style="background: rgba(46, 139, 87, .7);">
                    <div class="container">
                        <div class="row justify-content-start">
                            <div class="col-10 col-lg-8">
                                @if($slide->subtitle)
                                    <h5 class="text-white text-uppercase mb-3 animated slideInDown">{{ $slide->subtitle }}</h5>
                                @endif
                                <h1 class="display-3 text-white animated slideInDown mb-4">{{ $slide->title }}</h1>
                                <p class="fs-5 fw-medium text-white mb-4 pb-2">{{ $slide->description }}</p>
                                <div class="d-flex flex-wrap gap-3">
                                    @if($slide->primary_button_text && $slide->primary_button_link)
                                        <a href="{{ $slide->primary_button_link }}" class="btn btn-primary py-md-3 px-md-5 animated slideInLeft">
                                            <i class="fas fa-info-circle me-2"></i>{{ $slide->primary_button_text }}
                                        </a>
                                    @endif
                                    @if($slide->secondary_button_text && $slide->secondary_button_link)
                                        <a href="{{ $slide->secondary_button_link }}" class="btn btn-secondary py-md-3 px-md-5 animated slideInRight">
                                            <i class="fas fa-video me-2"></i>{{ $slide->secondary_button_text }}
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <!-- Fallback content if no slides are available -->
            <div class="owl-carousel-item position-relative">
                <img class="img-fluid" src="{{ asset('img/carousel-1.jpg') }}" alt="Virtual Medical Education">
                <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center" style="background: rgba(46, 139, 87, .7);">
                    <div class="container">
                        <div class="row justify-content-start">
                            <div class="col-10 col-lg-8">
                                <h5 class="text-white text-uppercase mb-3 animated slideInDown">Virtual Medical Education</h5>
                                <h1 class="display-3 text-white animated slideInDown mb-4">Advanced Continuing Professional Development</h1>
                                <p class="fs-5 fw-medium text-white mb-4 pb-2">Join thousands of healthcare professionals in our interactive virtual learning environment. Access cutting-edge medical education from anywhere, anytime.</p>
                                <a href="{{ route('about') }}" class="btn btn-primary py-md-3 px-md-5 me-3 animated slideInLeft">
                                    <i class="fas fa-info-circle me-2"></i>Learn More
                                </a>
                                <a href="{{ route('booking') }}" class="btn btn-secondary py-md-3 px-md-5 animated slideInRight">
                                    <i class="fas fa-video me-2"></i>Join Live Session
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforelse
    </div>
</div>
