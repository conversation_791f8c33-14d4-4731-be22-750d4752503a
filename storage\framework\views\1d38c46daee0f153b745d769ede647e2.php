<div class="container-xxl py-5 wow fadeInUp" data-wow-delay="0.1s">
    <div class="container">
        <div class="text-center">
            <h6 class="text-secondary text-uppercase">Testimonials</h6>
            <h1 class="mb-5">What Our Clients Say!</h1>
        </div>
        <div class="owl-carousel testimonial-carousel position-relative wow fadeInUp" data-wow-delay="0.1s">
            <?php $__empty_1 = true; $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="testimonial-item text-center">
                    <div class="testimonial-text bg-light text-center p-4 mb-4">
                        <p class="mb-0"><?php echo e($testimonial->testimonial); ?></p>
                    </div>
                    <?php if($testimonial->client_image): ?>
                        <img class="bg-light rounded-circle p-2 mx-auto mb-2" src="<?php echo e(asset('storage/' . $testimonial->client_image)); ?>" style="width: 80px; height: 80px; object-fit: cover;" alt="<?php echo e($testimonial->client_name); ?>">
                    <?php else: ?>
                        <div class="bg-light rounded-circle p-2 mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-user fa-2x text-primary"></i>
                        </div>
                    <?php endif; ?>
                    <div class="mb-2">
                        <?php for($i = 1; $i <= 5; $i++): ?>
                            <?php if($i <= $testimonial->rating): ?>
                                <small class="fa fa-star text-warning"></small>
                            <?php else: ?>
                                <small class="fa fa-star text-muted"></small>
                            <?php endif; ?>
                        <?php endfor; ?>
                    </div>
                    <h5 class="mb-1"><?php echo e($testimonial->client_name); ?></h5>
                    <p class="m-0">
                        <?php if($testimonial->client_position && $testimonial->client_company): ?>
                            <?php echo e($testimonial->client_position); ?> at <?php echo e($testimonial->client_company); ?>

                        <?php elseif($testimonial->client_position): ?>
                            <?php echo e($testimonial->client_position); ?>

                        <?php elseif($testimonial->client_company): ?>
                            <?php echo e($testimonial->client_company); ?>

                        <?php else: ?>
                            Valued Client
                        <?php endif; ?>
                    </p>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <!-- Fallback content if no testimonials are available -->
                <div class="testimonial-item text-center">
                    <div class="testimonial-text bg-light text-center p-4 mb-4">
                        <p class="mb-0">The Virtual CME Hub has transformed our medical education approach. The interactive sessions and expert instructors provide exceptional learning experiences that directly impact patient care.</p>
                    </div>
                    <img class="bg-light rounded-circle p-2 mx-auto mb-2" src="<?php echo e(asset('img/testimonial-1.jpg')); ?>" style="width: 80px; height: 80px;">
                    <div class="mb-2">
                        <small class="fa fa-star text-warning"></small>
                        <small class="fa fa-star text-warning"></small>
                        <small class="fa fa-star text-warning"></small>
                        <small class="fa fa-star text-warning"></small>
                        <small class="fa fa-star text-warning"></small>
                    </div>
                    <h5 class="mb-1">Dr. Sarah Johnson</h5>
                    <p class="m-0">Emergency Medicine Physician</p>
                </div>
                <div class="testimonial-item text-center">
                    <div class="testimonial-text bg-light text-center p-4 mb-4">
                        <p class="mb-0">Outstanding platform for continuing medical education. The CPD credit tracking and mobile accessibility make it perfect for busy healthcare professionals.</p>
                    </div>
                    <img class="bg-light rounded-circle p-2 mx-auto mb-2" src="<?php echo e(asset('img/testimonial-2.jpg')); ?>" style="width: 80px; height: 80px;">
                    <div class="mb-2">
                        <small class="fa fa-star text-warning"></small>
                        <small class="fa fa-star text-warning"></small>
                        <small class="fa fa-star text-warning"></small>
                        <small class="fa fa-star text-warning"></small>
                        <small class="fa fa-star text-warning"></small>
                    </div>
                    <h5 class="mb-1">Dr. Michael Chen</h5>
                    <p class="m-0">Pediatric Specialist</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\VCH\resources\views/layouts/include/testimonial.blade.php ENDPATH**/ ?>