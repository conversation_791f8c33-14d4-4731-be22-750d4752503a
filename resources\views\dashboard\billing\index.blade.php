@extends('layouts.dashboard')

@section('page-title', 'Billing & Subscription')

@section('content')
<div class="dashboard-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Billing & Subscription</h2>
                    <p class="text-muted mb-0">Manage your subscription and payment information</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('dashboard.billing.history') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-history me-2"></i>View History
                    </a>
                    <a href="{{ route('dashboard.support.contact') }}" class="btn btn-outline-primary">
                        <i class="fas fa-headset me-2"></i>Billing Support
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Plan -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                                    <i class="fas fa-crown text-primary fa-2x"></i>
                                </div>
                                <div>
                                    <h4 class="mb-1">{{ $stats['current_plan'] }} Plan</h4>
                                    <p class="text-muted mb-0">
                                        ${{ number_format($stats['monthly_cost'], 2) }}/{{ $stats['billing_cycle'] }} • 
                                        Next billing: {{ $stats['next_billing_date']->format('M j, Y') }}
                                    </p>
                                    <span class="badge bg-{{ $stats['subscription_status'] === 'active' ? 'success' : 'warning' }} mt-1">
                                        {{ ucfirst($stats['subscription_status']) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group">
                                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#changePlanModal">
                                    <i class="fas fa-edit me-1"></i>Change Plan
                                </button>
                                <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelModal">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-dollar-sign text-success fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-success mb-1">${{ number_format($stats['total_spent'], 2) }}</h3>
                    <p class="text-muted mb-0">Total Spent</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-calendar-alt text-primary fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-primary mb-1">{{ $stats['next_billing_date']->diffInDays(now()) }}</h3>
                    <p class="text-muted mb-0">Days Until Next Bill</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-receipt text-info fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-info mb-1">{{ count($invoices) }}</h3>
                    <p class="text-muted mb-0">Total Invoices</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Payment Methods -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-credit-card me-2 text-primary"></i>Payment Methods</h5>
                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#paymentMethodModal">
                            <i class="fas fa-plus me-1"></i>Add Method
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @forelse($paymentMethods as $method)
                    <div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                <i class="fab fa-cc-{{ strtolower($method['brand']) }} text-primary fa-lg"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">{{ $method['brand'] }} •••• {{ $method['last_four'] }}</h6>
                                <small class="text-muted">Expires {{ $method['expires'] }}</small>
                                @if($method['is_default'])
                                    <span class="badge bg-success ms-2">Default</span>
                                @endif
                            </div>
                        </div>
                        <div class="btn-group btn-group-sm">
                            @if(!$method['is_default'])
                                <button class="btn btn-outline-primary">Set Default</button>
                            @endif
                            <button class="btn btn-outline-secondary">Edit</button>
                            <button class="btn btn-outline-danger">Remove</button>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-4">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No payment methods</h6>
                        <p class="text-muted">Add a payment method to manage your subscription.</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#paymentMethodModal">
                            <i class="fas fa-plus me-1"></i>Add Payment Method
                        </button>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Recent Billing History -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-history me-2 text-primary"></i>Recent Invoices</h5>
                        <a href="{{ route('dashboard.billing.history') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-eye me-1"></i>View All
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @forelse($invoices->take(3) as $invoice)
                    <div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3">
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">{{ $invoice['id'] }}</h6>
                                <span class="badge bg-{{ $invoice['status'] === 'paid' ? 'success' : 'warning' }}">
                                    {{ ucfirst($invoice['status']) }}
                                </span>
                            </div>
                            <p class="text-muted mb-1">{{ $invoice['description'] }}</p>
                            <small class="text-muted">{{ \Carbon\Carbon::parse($invoice['date'])->format('M j, Y') }}</small>
                        </div>
                        <div class="text-end ms-3">
                            <h6 class="mb-2">${{ number_format($invoice['amount'], 2) }}</h6>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ route('dashboard.billing.invoice.download', $invoice['id']) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-4">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No billing history</h6>
                        <p class="text-muted">Your invoices will appear here once you start your subscription.</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Plan Comparison -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-layer-group me-2 text-primary"></i>Available Plans</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($plans as $plan)
                        <div class="col-md-4 mb-3">
                            <div class="card border h-100 {{ $plan['is_current'] ? 'border-primary' : '' }} {{ $plan['is_popular'] ? 'position-relative' : '' }}">
                                @if($plan['is_popular'])
                                    <div class="position-absolute top-0 start-50 translate-middle">
                                        <span class="badge bg-warning text-dark">Most Popular</span>
                                    </div>
                                @endif
                                <div class="card-body text-center">
                                    <h5 class="card-title">{{ $plan['name'] }}</h5>
                                    <h2 class="text-primary">${{ number_format($plan['price'], 2) }}<small class="text-muted">/{{ $plan['billing_cycle'] }}</small></h2>
                                    <ul class="list-unstyled mt-3 mb-4">
                                        @foreach($plan['features'] as $feature)
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>{{ $feature }}</li>
                                        @endforeach
                                    </ul>
                                    @if($plan['is_current'])
                                        <button class="btn btn-primary w-100" disabled>Current Plan</button>
                                    @else
                                        <form action="{{ route('dashboard.billing.change-plan') }}" method="POST" class="d-inline w-100">
                                            @csrf
                                            <input type="hidden" name="plan" value="{{ strtolower($plan['name']) }}">
                                            <button type="submit" class="btn btn-outline-primary w-100">
                                                {{ $plan['price'] > $stats['monthly_cost'] ? 'Upgrade' : 'Downgrade' }}
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Plan Modal -->
<div class="modal fade" id="changePlanModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Change Subscription Plan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('dashboard.billing.change-plan') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <p>Select a new plan for your subscription. Changes will take effect on your next billing cycle.</p>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="plan" value="basic" id="basicPlan">
                        <label class="form-check-label" for="basicPlan">
                            <strong>Basic Plan</strong> - $19.99/month
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="plan" value="professional" id="professionalPlan" checked>
                        <label class="form-check-label" for="professionalPlan">
                            <strong>Professional Plan</strong> - $29.99/month (Current)
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="plan" value="enterprise" id="enterprisePlan">
                        <label class="form-check-label" for="enterprisePlan">
                            <strong>Enterprise Plan</strong> - $49.99/month
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Change Plan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Cancel Subscription Modal -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('dashboard.billing.cancel') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Your subscription will remain active until {{ $stats['next_billing_date']->format('M j, Y') }}.
                    </div>
                    
                    <div class="mb-3">
                        <label for="cancellationReason" class="form-label">Reason for cancellation (optional)</label>
                        <textarea class="form-control" id="cancellationReason" name="cancellation_reason" rows="3" 
                                  placeholder="Help us improve by telling us why you're canceling..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Subscription</button>
                    <button type="submit" class="btn btn-danger">Cancel Subscription</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Payment Method Modal -->
<div class="modal fade" id="paymentMethodModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Payment Method</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('dashboard.billing.payment-method.update') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="cardholderName" class="form-label">Cardholder Name</label>
                        <input type="text" class="form-control" id="cardholderName" name="cardholder_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="cardNumber" class="form-label">Card Number</label>
                        <input type="text" class="form-control" id="cardNumber" name="card_number" 
                               placeholder="1234 5678 9012 3456" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="expiryMonth" class="form-label">Month</label>
                            <select class="form-select" id="expiryMonth" name="expiry_month" required>
                                <option value="">MM</option>
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}">{{ str_pad($i, 2, '0', STR_PAD_LEFT) }}</option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="expiryYear" class="form-label">Year</label>
                            <select class="form-select" id="expiryYear" name="expiry_year" required>
                                <option value="">YYYY</option>
                                @for($i = date('Y'); $i <= date('Y') + 10; $i++)
                                    <option value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="cvv" class="form-label">CVV</label>
                            <input type="text" class="form-control" id="cvv" name="cvv" placeholder="123" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Payment Method</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
