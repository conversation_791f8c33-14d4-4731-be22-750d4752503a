@props([
    'type' => 'info',
    'message' => null,
    'title' => null,
    'dismissible' => true,
    'autoHide' => true,
    'hideDelay' => 5000,
    'showIcon' => true,
    'class' => '',
    'position' => 'static', // static, fixed-top, fixed-bottom, toast
])

@php
    $alertTypes = [
        'success' => ['icon' => 'fas fa-check-circle', 'class' => 'alert-success'],
        'error' => ['icon' => 'fas fa-exclamation-circle', 'class' => 'alert-danger'],
        'warning' => ['icon' => 'fas fa-exclamation-triangle', 'class' => 'alert-warning'],
        'info' => ['icon' => 'fas fa-info-circle', 'class' => 'alert-info'],
        'primary' => ['icon' => 'fas fa-star', 'class' => 'alert-primary'],
        'secondary' => ['icon' => 'fas fa-cog', 'class' => 'alert-secondary'],
    ];
    
    $config = $alertTypes[$type] ?? $alertTypes['info'];
    $alertClass = $config['class'];
    $iconClass = $config['icon'];
    
    // Determine position classes
    $positionClass = '';
    switch ($position) {
        case 'fixed-top':
            $positionClass = 'position-fixed top-0 start-50 translate-middle-x';
            break;
        case 'fixed-bottom':
            $positionClass = 'position-fixed bottom-0 start-50 translate-middle-x';
            break;
        case 'toast':
            $positionClass = 'position-fixed top-0 end-0 m-3';
            break;
    }
@endphp

<!-- Session-based messages -->
@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show user-feedback {{ $class }} {{ $positionClass }}" 
         role="alert" 
         data-auto-hide="{{ $autoHide ? 'true' : 'false' }}" 
         data-hide-delay="{{ $hideDelay }}">
        <div class="d-flex align-items-start">
            @if($showIcon)
                <i class="fas fa-check-circle me-3 mt-1 flex-shrink-0"></i>
            @endif
            <div class="flex-grow-1">
                @if($title)
                    <h6 class="alert-heading mb-1">{{ $title }}</h6>
                @endif
                <div>{{ session('success') }}</div>
            </div>
        </div>
        @if($dismissible)
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        @endif
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show user-feedback {{ $class }} {{ $positionClass }}" 
         role="alert" 
         data-auto-hide="{{ $autoHide ? 'true' : 'false' }}" 
         data-hide-delay="{{ $hideDelay }}">
        <div class="d-flex align-items-start">
            @if($showIcon)
                <i class="fas fa-exclamation-circle me-3 mt-1 flex-shrink-0"></i>
            @endif
            <div class="flex-grow-1">
                @if($title)
                    <h6 class="alert-heading mb-1">{{ $title }}</h6>
                @endif
                <div>{{ session('error') }}</div>
            </div>
        </div>
        @if($dismissible)
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        @endif
    </div>
@endif

@if(session('warning'))
    <div class="alert alert-warning alert-dismissible fade show user-feedback {{ $class }} {{ $positionClass }}" 
         role="alert" 
         data-auto-hide="{{ $autoHide ? 'true' : 'false' }}" 
         data-hide-delay="{{ $hideDelay }}">
        <div class="d-flex align-items-start">
            @if($showIcon)
                <i class="fas fa-exclamation-triangle me-3 mt-1 flex-shrink-0"></i>
            @endif
            <div class="flex-grow-1">
                @if($title)
                    <h6 class="alert-heading mb-1">{{ $title }}</h6>
                @endif
                <div>{{ session('warning') }}</div>
            </div>
        </div>
        @if($dismissible)
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        @endif
    </div>
@endif

@if(session('info'))
    <div class="alert alert-info alert-dismissible fade show user-feedback {{ $class }} {{ $positionClass }}" 
         role="alert" 
         data-auto-hide="{{ $autoHide ? 'true' : 'false' }}" 
         data-hide-delay="{{ $hideDelay }}">
        <div class="d-flex align-items-start">
            @if($showIcon)
                <i class="fas fa-info-circle me-3 mt-1 flex-shrink-0"></i>
            @endif
            <div class="flex-grow-1">
                @if($title)
                    <h6 class="alert-heading mb-1">{{ $title }}</h6>
                @endif
                <div>{{ session('info') }}</div>
            </div>
        </div>
        @if($dismissible)
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        @endif
    </div>
@endif

<!-- Custom message -->
@if($message)
    <div class="alert {{ $alertClass }} {{ $dismissible ? 'alert-dismissible' : '' }} fade show user-feedback {{ $class }} {{ $positionClass }}" 
         role="alert" 
         data-auto-hide="{{ $autoHide ? 'true' : 'false' }}" 
         data-hide-delay="{{ $hideDelay }}">
        <div class="d-flex align-items-start">
            @if($showIcon)
                <i class="{{ $iconClass }} me-3 mt-1 flex-shrink-0"></i>
            @endif
            <div class="flex-grow-1">
                @if($title)
                    <h6 class="alert-heading mb-1">{{ $title }}</h6>
                @endif
                <div>{{ $message }}</div>
            </div>
        </div>
        @if($dismissible)
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        @endif
    </div>
@endif

@once
@push('scripts')
<script>
// User Feedback Management
window.UserFeedback = {
    // Show a feedback message
    show: function(type, message, options = {}) {
        const defaults = {
            title: null,
            dismissible: true,
            autoHide: true,
            hideDelay: 5000,
            showIcon: true,
            position: 'fixed-top',
            container: null
        };
        
        const config = { ...defaults, ...options };
        const alertId = 'feedback-' + Date.now();
        
        const alertTypes = {
            'success': { icon: 'fas fa-check-circle', class: 'alert-success' },
            'error': { icon: 'fas fa-exclamation-circle', class: 'alert-danger' },
            'warning': { icon: 'fas fa-exclamation-triangle', class: 'alert-warning' },
            'info': { icon: 'fas fa-info-circle', class: 'alert-info' },
            'primary': { icon: 'fas fa-star', class: 'alert-primary' },
            'secondary': { icon: 'fas fa-cog', class: 'alert-secondary' }
        };
        
        const alertConfig = alertTypes[type] || alertTypes['info'];
        
        let positionClass = '';
        switch (config.position) {
            case 'fixed-top':
                positionClass = 'position-fixed top-0 start-50 translate-middle-x';
                break;
            case 'fixed-bottom':
                positionClass = 'position-fixed bottom-0 start-50 translate-middle-x';
                break;
            case 'toast':
                positionClass = 'position-fixed top-0 end-0 m-3';
                break;
        }
        
        const alertHtml = `
            <div id="${alertId}" class="alert ${alertConfig.class} ${config.dismissible ? 'alert-dismissible' : ''} fade show user-feedback ${positionClass}" 
                 role="alert" style="z-index: 1060; min-width: 300px; max-width: 500px;">
                <div class="d-flex align-items-start">
                    ${config.showIcon ? `<i class="${alertConfig.icon} me-3 mt-1 flex-shrink-0"></i>` : ''}
                    <div class="flex-grow-1">
                        ${config.title ? `<h6 class="alert-heading mb-1">${config.title}</h6>` : ''}
                        <div>${message}</div>
                    </div>
                </div>
                ${config.dismissible ? '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' : ''}
            </div>
        `;
        
        const container = config.container ? document.querySelector(config.container) : document.body;
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        const alertElement = document.getElementById(alertId);
        
        // Auto-hide functionality
        if (config.autoHide) {
            setTimeout(() => {
                if (alertElement && alertElement.parentNode) {
                    const bsAlert = new bootstrap.Alert(alertElement);
                    bsAlert.close();
                }
            }, config.hideDelay);
        }
        
        return alertElement;
    },
    
    // Convenience methods
    success: function(message, options = {}) {
        return this.show('success', message, options);
    },
    
    error: function(message, options = {}) {
        return this.show('error', message, options);
    },
    
    warning: function(message, options = {}) {
        return this.show('warning', message, options);
    },
    
    info: function(message, options = {}) {
        return this.show('info', message, options);
    },
    
    // Clear all feedback messages
    clear: function() {
        const alerts = document.querySelectorAll('.user-feedback');
        alerts.forEach(alert => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }
};

document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide functionality for existing alerts
    const alerts = document.querySelectorAll('.user-feedback[data-auto-hide="true"]');
    alerts.forEach(alert => {
        const hideDelay = parseInt(alert.dataset.hideDelay) || 5000;
        setTimeout(() => {
            if (alert.parentNode) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, hideDelay);
    });
    
    // Enhanced alert animations
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1 && node.classList.contains('user-feedback')) {
                    // Add entrance animation
                    node.style.transform = 'translateY(-20px)';
                    node.style.opacity = '0';
                    
                    setTimeout(() => {
                        node.style.transition = 'all 0.3s ease';
                        node.style.transform = 'translateY(0)';
                        node.style.opacity = '1';
                    }, 10);
                }
            });
        });
    });
    
    observer.observe(document.body, { childList: true, subtree: true });
});

// Global error handler for AJAX requests
document.addEventListener('DOMContentLoaded', function() {
    // Intercept fetch requests
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        return originalFetch.apply(this, args)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response;
            })
            .catch(error => {
                UserFeedback.error('Network error: ' + error.message);
                throw error;
            });
    };
    
    // Intercept XMLHttpRequest
    const originalXHR = window.XMLHttpRequest;
    window.XMLHttpRequest = function() {
        const xhr = new originalXHR();
        const originalSend = xhr.send;
        
        xhr.send = function(...args) {
            xhr.addEventListener('error', function() {
                UserFeedback.error('Network error occurred. Please try again.');
            });
            
            xhr.addEventListener('timeout', function() {
                UserFeedback.warning('Request timed out. Please try again.');
            });
            
            return originalSend.apply(this, args);
        };
        
        return xhr;
    };
});
</script>
@endpush

@push('styles')
<style>
.user-feedback {
    border-left: 4px solid;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-feedback.alert-success {
    border-left-color: #198754;
}

.user-feedback.alert-danger {
    border-left-color: #dc3545;
}

.user-feedback.alert-warning {
    border-left-color: #ffc107;
}

.user-feedback.alert-info {
    border-left-color: #0dcaf0;
}

.user-feedback.alert-primary {
    border-left-color: #0d6efd;
}

.user-feedback.alert-secondary {
    border-left-color: #6c757d;
}

.user-feedback .alert-heading {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Fixed position alerts */
.user-feedback.position-fixed {
    z-index: 1060;
    min-width: 300px;
    max-width: 500px;
}

/* Toast-style alerts */
.user-feedback.position-fixed.top-0.end-0 {
    margin: 1rem;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .user-feedback.position-fixed {
        left: 1rem !important;
        right: 1rem !important;
        transform: none !important;
        min-width: auto;
        max-width: none;
    }
}

/* Enhanced close button */
.user-feedback .btn-close {
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.user-feedback .btn-close:hover {
    opacity: 1;
}

/* Loading state for forms */
.form-loading {
    position: relative;
    pointer-events: none;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.form-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2rem;
    height: 2rem;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 11;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
</style>
@endpush
@endonce
