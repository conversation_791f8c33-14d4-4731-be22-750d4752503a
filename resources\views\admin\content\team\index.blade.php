@extends('layouts.dashboard')

@section('page-title', 'Team Members Management')

@section('content')
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Team Members Management</h2>
                    <p class="text-muted mb-0">Manage instructors, experts and staff profiles</p>
                </div>
                <div>
                    <a href="{{ route('admin.content.team.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Member
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters & Search -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ route('admin.content.team.index') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0"><i class="fas fa-search text-muted"></i></span>
                        <input type="text" class="form-control bg-light border-0" name="search" 
                               placeholder="Search members..." value="{{ request('search') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select bg-light border-0">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="sort" class="form-select bg-light border-0">
                        <option value="order_asc" {{ request('sort') == 'order_asc' ? 'selected' : '' }}>Order (Low to High)</option>
                        <option value="order_desc" {{ request('sort') == 'order_desc' ? 'selected' : '' }}>Order (High to Low)</option>
                        <option value="name_asc" {{ request('sort') == 'name_asc' ? 'selected' : '' }}>Name (A-Z)</option>
                        <option value="name_desc" {{ request('sort') == 'name_desc' ? 'selected' : '' }}>Name (Z-A)</option>
                        <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest First</option>
                        <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>Oldest First</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Team Members List -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input select-all" type="checkbox" id="selectAll">
                                    <label class="form-check-label" for="selectAll"></label>
                                </div>
                            </th>
                            <th width="60">Order</th>
                            <th width="80">Photo</th>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Contact</th>
                            <th width="100">Status</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="sortable" data-url="{{ route('admin.content.reorder', ['type' => 'team']) }}">
                        @forelse($teamMembers ?? [] as $member)
                        <tr data-id="{{ $member->id }}">
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input select-item" type="checkbox" 
                                           id="member{{ $member->id }}" name="selected_items[]" value="{{ $member->id }}">
                                    <label class="form-check-label" for="member{{ $member->id }}"></label>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">{{ $member->order }}</span>
                                <i class="fas fa-grip-vertical text-muted ms-2 drag-handle"></i>
                            </td>
                            <td>
                                @if($member->image)
                                    <img src="{{ asset($member->image) }}" alt="{{ $member->name }}" 
                                         class="rounded-circle" width="50" height="50" style="object-fit: cover;">
                                @else
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 50px; height: 50px;">
                                        <i class="fas fa-user text-muted"></i>
                                    </div>
                                @endif
                            </td>
                            <td>
                                <h6 class="mb-0">{{ $member->name }}</h6>
                            </td>
                            <td>
                                <span class="text-muted">{{ $member->position }}</span>
                            </td>
                            <td>
                                @if($member->email)
                                    <a href="mailto:{{ $member->email }}" class="text-decoration-none">
                                        <i class="fas fa-envelope me-1 text-muted"></i>{{ $member->email }}
                                    </a><br>
                                @endif
                                @if($member->phone)
                                    <a href="tel:{{ $member->phone }}" class="text-decoration-none">
                                        <i class="fas fa-phone me-1 text-muted"></i>{{ $member->phone }}
                                    </a>
                                @endif
                            </td>
                            <td>
                                @if($member->is_active)
                                    <span class="badge bg-success-soft">Active</span>
                                @else
                                    <span class="badge bg-danger-soft">Inactive</span>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex">
                                    <a href="{{ route('admin.content.team.edit', $member->id) }}" class="btn btn-sm btn-outline-primary me-2">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.content.team.destroy', $member->id) }}" method="POST" class="delete-form">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <h5>No Team Members Found</h5>
                                    <p class="text-muted">Get started by adding your first team member</p>
                                    <a href="{{ route('admin.content.team.create') }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus me-2"></i>Add New Member
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    @if(isset($teamMembers) && $teamMembers->count() > 0)
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ route('admin.content.bulk-action', ['type' => 'team']) }}" method="POST" id="bulkActionForm">
                @csrf
                <div class="row g-3 align-items-center">
                    <div class="col-auto">
                        <h5 class="mb-0">Bulk Actions</h5>
                    </div>
                    <div class="col-auto">
                        <select name="action" class="form-select" id="bulkAction">
                            <option value="">Select Action</option>
                            <option value="activate">Activate</option>
                            <option value="deactivate">Deactivate</option>
                            <option value="delete">Delete</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-primary" id="applyBulkAction" disabled>
                            Apply
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-center">
        {{ $teamMembers->links() }}
    </div>
    @endif
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Select all functionality
        const selectAll = document.getElementById('selectAll');
        const selectItems = document.querySelectorAll('.select-item');
        const applyBulkAction = document.getElementById('applyBulkAction');
        const bulkAction = document.getElementById('bulkAction');
        
        if (selectAll) {
            selectAll.addEventListener('change', function() {
                selectItems.forEach(item => {
                    item.checked = this.checked;
                });
                updateBulkActionButton();
            });
        }
        
        selectItems.forEach(item => {
            item.addEventListener('change', function() {
                updateBulkActionButton();
                updateSelectAllCheckbox();
            });
        });
        
        bulkAction.addEventListener('change', updateBulkActionButton);
        
        function updateBulkActionButton() {
            const hasCheckedItems = Array.from(selectItems).some(item => item.checked);
            const hasSelectedAction = bulkAction.value !== '';
            applyBulkAction.disabled = !(hasCheckedItems && hasSelectedAction);
        }
        
        function updateSelectAllCheckbox() {
            const allChecked = Array.from(selectItems).every(item => item.checked);
            const someChecked = Array.from(selectItems).some(item => item.checked);
            
            selectAll.checked = allChecked;
            selectAll.indeterminate = someChecked && !allChecked;
        }
        
        // Confirm delete
        document.querySelectorAll('.delete-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                if (confirm('Are you sure you want to delete this team member? This action cannot be undone.')) {
                    this.submit();
                }
            });
        });
        
        // Confirm bulk delete
        document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
            const action = document.getElementById('bulkAction').value;
            if (action === 'delete') {
                e.preventDefault();
                if (confirm('Are you sure you want to delete the selected team members? This action cannot be undone.')) {
                    this.submit();
                }
            }
        });
        
        // Sortable functionality for drag and drop reordering
        if (typeof Sortable !== 'undefined') {
            const sortableList = document.querySelector('.sortable');
            if (sortableList) {
                new Sortable(sortableList, {
                    handle: '.drag-handle',
                    animation: 150,
                    onEnd: function(evt) {
                        const itemIds = Array.from(sortableList.querySelectorAll('tr[data-id]'))
                            .map((row, index) => {
                                return {
                                    id: row.dataset.id,
                                    order: index
                                };
                            });
                            
                        fetch(sortableList.dataset.url, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({ items: itemIds })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Update the order numbers displayed in the UI
                                itemIds.forEach(item => {
                                    const row = sortableList.querySelector(`tr[data-id="${item.id}"]`);
                                    const orderBadge = row.querySelector('.badge');
                                    if (orderBadge) {
                                        orderBadge.textContent = item.order;
                                    }
                                });
                            }
                        })
                        .catch(error => console.error('Error updating order:', error));
                    }
                });
            }
        }
    });
</script>
@endpush
@endsection