@extends('layouts.dashboard')

@section('title', 'Live Session Management')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Live Session Management</h1>
            <p class="mb-0 text-muted">Manage live sessions and webinars with Zoom integration</p>
        </div>
        <a href="{{ route('admin.sessions.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create New Session
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Sessions</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-video fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Upcoming</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['upcoming']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Live Now</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['live']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-broadcast-tower fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Completed</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['completed']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Sessions</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.sessions.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="Search by title or instructor">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="scheduled" {{ request('status') === 'scheduled' ? 'selected' : '' }}>Scheduled</option>
                                <option value="live" {{ request('status') === 'live' ? 'selected' : '' }}>Live</option>
                                <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from">From Date</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to">To Date</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="{{ route('admin.sessions.index') }}" class="btn btn-secondary">Clear</a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Sessions Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Live Sessions</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Session</th>
                            <th>Instructor</th>
                            <th>Date & Time</th>
                            <th>Duration</th>
                            <th>Status</th>
                            <th>Participants</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($sessions as $session)
                        <tr>
                            <td>
                                <div>
                                    <div class="font-weight-bold">{{ $session->title }}</div>
                                    @if($session->category)
                                        <span class="badge badge-secondary">{{ $session->category }}</span>
                                    @endif
                                    @if($session->level)
                                        <span class="badge badge-info">{{ $session->level }}</span>
                                    @endif
                                    @if($session->is_free)
                                        <span class="badge badge-success">Free</span>
                                    @else
                                        <span class="badge badge-warning">${{ number_format($session->price, 2) }}</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div class="font-weight-bold">{{ $session->instructor }}</div>
                                    @if($session->instructor_title)
                                        <div class="small text-muted">{{ $session->instructor_title }}</div>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div class="font-weight-bold">{{ $session->session_date->format('M j, Y') }}</div>
                                    <div class="small text-muted">{{ $session->session_date->format('g:i A') }}</div>
                                </div>
                            </td>
                            <td>{{ $session->duration_minutes }} min</td>
                            <td>
                                <span class="badge badge-{{ 
                                    $session->status === 'live' ? 'success' : 
                                    ($session->status === 'scheduled' ? 'primary' : 
                                    ($session->status === 'completed' ? 'secondary' : 'danger')) 
                                }}">
                                    {{ ucfirst($session->status) }}
                                </span>
                                @if($session->zoom_meeting_id)
                                    <br><small class="text-muted">
                                        <i class="fab fa-zoom text-primary"></i> Zoom Integrated
                                    </small>
                                @endif
                            </td>
                            <td>
                                @if($session->max_participants)
                                    {{ $session->registration_count ?? 0 }} / {{ $session->max_participants }}
                                @else
                                    {{ $session->registration_count ?? 0 }} / Unlimited
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.sessions.show', $session) }}" class="btn btn-sm btn-info" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.sessions.edit', $session) }}" class="btn btn-sm btn-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    @if($session->status === 'scheduled' && $session->zoom_start_url)
                                        <a href="{{ route('admin.sessions.start', $session) }}" class="btn btn-sm btn-success" 
                                           title="Start Session" onclick="return confirm('Are you sure you want to start this session?')">
                                            <i class="fas fa-play"></i>
                                        </a>
                                    @endif
                                    
                                    @if($session->status === 'live')
                                        <form method="POST" action="{{ route('admin.sessions.end', $session) }}" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="btn btn-sm btn-warning" title="End Session"
                                                    onclick="return confirm('Are you sure you want to end this session?')">
                                                <i class="fas fa-stop"></i>
                                            </button>
                                        </form>
                                    @endif
                                    
                                    @if($session->zoom_join_url)
                                        <a href="{{ $session->zoom_join_url }}" target="_blank" class="btn btn-sm btn-secondary" title="Join Session">
                                            <i class="fab fa-zoom"></i>
                                        </a>
                                    @endif
                                    
                                    <form method="POST" action="{{ route('admin.sessions.destroy', $session) }}" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete"
                                                onclick="return confirm('Are you sure you want to delete this session? This will also delete the Zoom meeting.')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-video fa-3x mb-3"></i>
                                    <p>No live sessions found.</p>
                                    <a href="{{ route('admin.sessions.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Create Your First Session
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($sessions->hasPages())
                <div class="d-flex justify-content-center">
                    {{ $sessions->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.text-gray-800 { color: #5a5c69 !important; }
.text-gray-300 { color: #dddfeb !important; }
</style>
@endpush
