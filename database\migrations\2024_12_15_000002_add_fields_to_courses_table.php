<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('courses', 'short_description')) {
                $table->text('short_description')->nullable()->after('description');
            }
            
            if (!Schema::hasColumn('courses', 'slug')) {
                $table->string('slug')->unique()->after('title');
            }
            
            if (!Schema::hasColumn('courses', 'category')) {
                $table->string('category')->nullable()->after('short_description');
            }
            
            if (!Schema::hasColumn('courses', 'level')) {
                $table->string('level')->nullable()->after('category');
            }
            
            if (!Schema::hasColumn('courses', 'duration_minutes')) {
                $table->integer('duration_minutes')->nullable()->after('level');
            }
            
            if (!Schema::hasColumn('courses', 'price')) {
                $table->decimal('price', 8, 2)->default(0)->after('duration_minutes');
            }
            
            if (!Schema::hasColumn('courses', 'is_free')) {
                $table->boolean('is_free')->default(true)->after('price');
            }
            
            if (!Schema::hasColumn('courses', 'instructor_name')) {
                $table->string('instructor_name')->nullable()->after('is_free');
            }
            
            if (!Schema::hasColumn('courses', 'instructor_bio')) {
                $table->text('instructor_bio')->nullable()->after('instructor_name');
            }
            
            if (!Schema::hasColumn('courses', 'instructor_credentials')) {
                $table->string('instructor_credentials')->nullable()->after('instructor_bio');
            }
            
            if (!Schema::hasColumn('courses', 'cpd_credits')) {
                $table->decimal('cpd_credits', 3, 1)->nullable()->after('instructor_credentials');
            }
            
            if (!Schema::hasColumn('courses', 'status')) {
                $table->string('status')->default('draft')->after('cpd_credits');
            }
            
            if (!Schema::hasColumn('courses', 'is_featured')) {
                $table->boolean('is_featured')->default(false)->after('status');
            }
            
            if (!Schema::hasColumn('courses', 'max_participants')) {
                $table->integer('max_participants')->nullable()->after('is_featured');
            }
            
            if (!Schema::hasColumn('courses', 'prerequisites')) {
                $table->text('prerequisites')->nullable()->after('max_participants');
            }
            
            if (!Schema::hasColumn('courses', 'learning_objectives')) {
                $table->json('learning_objectives')->nullable()->after('prerequisites');
            }
            
            if (!Schema::hasColumn('courses', 'course_outline')) {
                $table->json('course_outline')->nullable()->after('learning_objectives');
            }
            
            if (!Schema::hasColumn('courses', 'featured_image')) {
                $table->string('featured_image')->nullable()->after('course_outline');
            }
            
            if (!Schema::hasColumn('courses', 'video_preview_url')) {
                $table->string('video_preview_url')->nullable()->after('featured_image');
            }
            
            if (!Schema::hasColumn('courses', 'certificate_template')) {
                $table->string('certificate_template')->nullable()->after('video_preview_url');
            }
            
            if (!Schema::hasColumn('courses', 'enrollment_start')) {
                $table->timestamp('enrollment_start')->nullable()->after('certificate_template');
            }
            
            if (!Schema::hasColumn('courses', 'enrollment_end')) {
                $table->timestamp('enrollment_end')->nullable()->after('enrollment_start');
            }
            
            if (!Schema::hasColumn('courses', 'course_start')) {
                $table->timestamp('course_start')->nullable()->after('enrollment_end');
            }
            
            if (!Schema::hasColumn('courses', 'course_end')) {
                $table->timestamp('course_end')->nullable()->after('course_start');
            }
            
            // Add indexes for performance
            $table->index('slug');
            $table->index('category');
            $table->index('level');
            $table->index('status');
            $table->index('is_featured');
            $table->index('is_free');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['slug']);
            $table->dropIndex(['category']);
            $table->dropIndex(['level']);
            $table->dropIndex(['status']);
            $table->dropIndex(['is_featured']);
            $table->dropIndex(['is_free']);
            
            // Drop columns
            $columns = [
                'short_description',
                'slug',
                'category',
                'level',
                'duration_minutes',
                'price',
                'is_free',
                'instructor_name',
                'instructor_bio',
                'instructor_credentials',
                'cpd_credits',
                'status',
                'is_featured',
                'max_participants',
                'prerequisites',
                'learning_objectives',
                'course_outline',
                'featured_image',
                'video_preview_url',
                'certificate_template',
                'enrollment_start',
                'enrollment_end',
                'course_start',
                'course_end'
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('courses', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
