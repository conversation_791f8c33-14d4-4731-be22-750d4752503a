<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'billing_interval',
        'billing_interval_count',
        'trial_period_days',
        'features',
        'limits',
        'is_active',
        'sort_order',
        'stripe_price_id',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'features' => 'array',
        'limits' => 'array',
        'is_active' => 'boolean',
    ];

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'plan_id');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    public function getFormattedPriceAttribute()
    {
        return '$' . number_format($this->price, 2);
    }

    public function getBillingCycleAttribute()
    {
        $interval = $this->billing_interval_count > 1 
            ? $this->billing_interval_count . ' ' . $this->billing_interval . 's'
            : $this->billing_interval;

        return $interval;
    }

    public function getDisplayPriceAttribute()
    {
        return $this->formatted_price . '/' . $this->billing_interval;
    }

    public function hasFeature($feature)
    {
        return in_array($feature, $this->features ?? []);
    }

    public function getLimit($limitType)
    {
        return $this->limits[$limitType] ?? null;
    }

    public function isPopular()
    {
        return $this->slug === 'professional'; // Mark professional plan as popular
    }

    public function canAccessFeature($feature)
    {
        return $this->hasFeature($feature);
    }

    public static function getBasicPlan()
    {
        return static::where('slug', 'basic')->first();
    }

    public static function getProfessionalPlan()
    {
        return static::where('slug', 'professional')->first();
    }

    public static function getEnterprisePlan()
    {
        return static::where('slug', 'enterprise')->first();
    }
}
