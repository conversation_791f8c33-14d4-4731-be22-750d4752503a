@extends('layouts.dashboard')

@section('page-title', 'Notifications')

@section('content')
<div class="dashboard-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Notifications</h2>
                    <p class="text-muted mb-0">Stay updated with your learning activities and system updates</p>
                </div>
                <div>
                    @if($unreadCount > 0)
                        <form action="{{ route('dashboard.notifications.mark-all-read') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-check-double me-2"></i>Mark All Read
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-bell text-primary fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-primary mb-1">{{ $stats['total_count'] }}</h3>
                    <p class="text-muted mb-0">Total Notifications</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-exclamation-circle text-warning fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-warning mb-1">{{ $stats['unread_count'] }}</h3>
                    <p class="text-muted mb-0">Unread</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-check-circle text-success fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-success mb-1">{{ $stats['read_count'] }}</h3>
                    <p class="text-muted mb-0">Read</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-calendar-day text-info fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-info mb-1">{{ $stats['today_count'] }}</h3>
                    <p class="text-muted mb-0">Today</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <!-- Filter Tabs -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="btn-group flex-wrap" role="group">
                                <a href="{{ route('dashboard.notifications.index', ['filter' => 'all']) }}"
                                   class="btn {{ $filter === 'all' ? 'btn-primary' : 'btn-outline-primary' }}">
                                    <i class="fas fa-list me-1"></i>All
                                </a>
                                <a href="{{ route('dashboard.notifications.index', ['filter' => 'unread']) }}"
                                   class="btn {{ $filter === 'unread' ? 'btn-warning' : 'btn-outline-warning' }}">
                                    <i class="fas fa-exclamation-circle me-1"></i>Unread
                                    @if($stats['unread_count'] > 0)
                                        <span class="badge bg-white text-warning ms-1">{{ $stats['unread_count'] }}</span>
                                    @endif
                                </a>
                                <a href="{{ route('dashboard.notifications.index', ['filter' => 'read']) }}"
                                   class="btn {{ $filter === 'read' ? 'btn-success' : 'btn-outline-success' }}">
                                    <i class="fas fa-check-circle me-1"></i>Read
                                </a>
                                <a href="{{ route('dashboard.notifications.index', ['filter' => 'important']) }}"
                                   class="btn {{ $filter === 'important' ? 'btn-danger' : 'btn-outline-danger' }}">
                                    <i class="fas fa-star me-1"></i>Important
                                    @if($stats['important_count'] > 0)
                                        <span class="badge bg-white text-danger ms-1">{{ $stats['important_count'] }}</span>
                                    @endif
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Search and Filters -->
                    <div class="row">
                        <div class="col-12">
                            <form method="GET" action="{{ route('dashboard.notifications.index') }}" class="row g-3">
                                <input type="hidden" name="filter" value="{{ $filter }}">

                                <div class="col-md-4">
                                    <label for="search" class="form-label">Search Notifications</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" id="search" name="search"
                                               value="{{ $search }}" placeholder="Search by title or message...">
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <label for="type" class="form-label">Type</label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="">All Types</option>
                                        @foreach($types as $typeOption)
                                            <option value="{{ $typeOption['value'] }}" {{ $type === $typeOption['value'] ? 'selected' : '' }}>
                                                {{ $typeOption['label'] }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label for="priority" class="form-label">Priority</label>
                                    <select class="form-select" id="priority" name="priority">
                                        <option value="">All Priorities</option>
                                        @foreach($priorities as $priorityOption)
                                            <option value="{{ $priorityOption }}" {{ $priority === $priorityOption ? 'selected' : '' }}>
                                                {{ ucfirst($priorityOption) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-1"></i>Filter
                                        </button>
                                        <a href="{{ route('dashboard.notifications.index', ['filter' => $filter]) }}"
                                           class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </a>
                                        <a href="{{ route('dashboard.notifications.preferences') }}"
                                           class="btn btn-outline-info">
                                            <i class="fas fa-cog me-1"></i>Settings
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <span class="text-muted">
                                <i class="fas fa-filter me-1"></i>
                                Showing {{ $notifications->count() }} of {{ $notifications->total() }} notifications
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    @forelse($notifications as $notification)
                    <div class="notification-item border-bottom p-4 {{ !$notification->isRead() ? 'bg-light' : '' }}" 
                         data-notification-id="{{ $notification->id }}">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon me-3">
                                <div class="bg-{{ $notification->color }} bg-opacity-10 rounded-circle p-2">
                                    <i class="{{ $notification->icon }} text-{{ $notification->color }}"></i>
                                </div>
                            </div>
                            
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0 {{ !$notification->isRead() ? 'fw-bold' : '' }}">
                                        {{ $notification->title }}
                                    </h6>
                                    <div class="d-flex align-items-center">
                                        @if(!$notification->isRead())
                                            <span class="badge bg-primary me-2">New</span>
                                        @endif
                                        <small class="text-muted">
                                            {{ $notification->created_at->diffForHumans() }}
                                        </small>
                                    </div>
                                </div>
                                
                                <p class="text-muted mb-2">{{ $notification->message }}</p>
                                
                                @if($notification->priority === 'high' || $notification->priority === 'urgent')
                                    <span class="badge bg-{{ $notification->priority === 'urgent' ? 'danger' : 'warning' }} mb-2">
                                        {{ ucfirst($notification->priority) }} Priority
                                    </span>
                                @endif
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-tag me-1"></i>{{ ucfirst(str_replace('_', ' ', $notification->type)) }}
                                    </small>
                                    
                                    <div class="btn-group btn-group-sm">
                                        @if(!$notification->isRead())
                                            <form action="{{ route('dashboard.notifications.mark-read', $notification) }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-check me-1"></i>Mark Read
                                                </button>
                                            </form>
                                        @endif
                                        
                                        @if($notification->data && isset($notification->data['action_url']))
                                            <a href="{{ $notification->data['action_url'] }}" class="btn btn-primary btn-sm">
                                                <i class="fas fa-external-link-alt me-1"></i>View
                                            </a>
                                        @endif
                                        
                                        <form action="{{ route('dashboard.notifications.delete', $notification) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                    onclick="return confirm('Are you sure you want to delete this notification?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-5">
                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No notifications found</h5>
                        <p class="text-muted">
                            @if($filter === 'unread')
                                You don't have any unread notifications.
                            @elseif($filter === 'read')
                                You don't have any read notifications.
                            @else
                                You don't have any notifications yet. We'll notify you about important updates and activities.
                            @endif
                        </p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    @if($notifications->hasPages())
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-center">
                {{ $notifications->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-mark notifications as read when clicked
    document.querySelectorAll('.notification-item').forEach(function(item) {
        item.addEventListener('click', function(e) {
            // Don't trigger if clicking on buttons
            if (e.target.closest('button') || e.target.closest('a')) {
                return;
            }
            
            const notificationId = this.dataset.notificationId;
            const isUnread = this.classList.contains('bg-light');
            
            if (isUnread) {
                // Mark as read via AJAX
                fetch(`/dashboard/notifications/${notificationId}/mark-read`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.classList.remove('bg-light');
                        const newBadge = this.querySelector('.badge.bg-primary');
                        if (newBadge) {
                            newBadge.remove();
                        }
                        const title = this.querySelector('h6');
                        if (title) {
                            title.classList.remove('fw-bold');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error marking notification as read:', error);
                });
            }
        });
    });
    
    // Refresh notifications every 30 seconds
    setInterval(function() {
        // Only refresh if we're on the unread filter
        if (window.location.search.includes('filter=unread') || !window.location.search.includes('filter=')) {
            window.location.reload();
        }
    }, 30000);
});
</script>
@endpush
