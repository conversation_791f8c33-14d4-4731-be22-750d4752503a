<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Enrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CourseController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $filter = $request->get('filter', 'all');
        $category = $request->get('category');
        $level = $request->get('level');
        $search = $request->get('search');
        $sort = $request->get('sort', 'title');

        $query = Course::active();

        // Apply filters
        switch ($filter) {
            case 'enrolled':
                $query->whereHas('enrollments', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                });
                break;
            case 'in_progress':
                $query->whereHas('enrollments', function ($q) use ($user) {
                    $q->where('user_id', $user->id)->where('status', 'in_progress');
                });
                break;
            case 'completed':
                $query->whereHas('enrollments', function ($q) use ($user) {
                    $q->where('user_id', $user->id)->where('status', 'completed');
                });
                break;
            case 'available':
                $query->whereDoesntHave('enrollments', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                });
                break;
        }

        if ($category) {
            $query->byCategory($category);
        }

        if ($level) {
            $query->byLevel($level);
        }

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('instructor', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        switch ($sort) {
            case 'title':
                $query->orderBy('title');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'duration':
                $query->orderBy('duration_hours');
                break;
            case 'credits':
                $query->orderBy('cpd_credits', 'desc');
                break;
            default:
                $query->orderBy('title');
        }

        $courses = $query->with(['enrollments' => function ($q) use ($user) {
            $q->where('user_id', $user->id);
        }])->paginate(12);

        // Get filter options
        $categories = Course::active()->distinct()->pluck('category')->filter();
        $levels = Course::active()->distinct()->pluck('level')->filter();

        // Get user statistics
        $stats = [
            'total_courses' => Course::active()->count(),
            'enrolled_count' => Enrollment::where('user_id', $user->id)->count(),
            'completed_count' => Enrollment::where('user_id', $user->id)
                ->where('status', 'completed')->count(),
            'in_progress_count' => Enrollment::where('user_id', $user->id)
                ->where('status', 'in_progress')->count(),
        ];

        return view('dashboard.courses.index', compact(
            'courses',
            'stats',
            'filter',
            'category',
            'level',
            'search',
            'sort',
            'categories',
            'levels'
        ));
    }

    public function show(Course $course)
    {
        $user = Auth::user();
        $enrollment = $course->enrollments()->where('user_id', $user->id)->first();

        return view('dashboard.courses.show', compact('course', 'enrollment'));
    }

    public function enroll(Request $request, Course $course)
    {
        $user = Auth::user();

        // Check if already enrolled
        if ($course->isEnrolledByUser($user->id)) {
            return redirect()->back()->with('error', 'You are already enrolled in this course.');
        }

        // Check if course is active
        if ($course->status !== 'active') {
            return redirect()->back()->with('error', 'This course is not available for enrollment.');
        }

        Enrollment::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'enrolled_at' => now(),
            'status' => 'enrolled',
        ]);

        return redirect()->back()->with('success', 'Successfully enrolled in the course!');
    }

    public function start(Course $course)
    {
        $user = Auth::user();

        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if (!$enrollment) {
            return redirect()->back()->with('error', 'You must be enrolled to start this course.');
        }

        if ($enrollment->status === 'enrolled') {
            $enrollment->status = 'in_progress';
            $enrollment->started_at = now();
            $enrollment->save();
        }

        return view('dashboard.courses.learn', compact('course', 'enrollment'));
    }

    public function updateProgress(Request $request, Course $course)
    {
        $user = Auth::user();

        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if (!$enrollment) {
            return response()->json(['error' => 'Not enrolled in this course'], 403);
        }

        $request->validate([
            'progress_percentage' => 'required|integer|min:0|max:100',
            'module_data' => 'nullable|array',
        ]);

        $enrollment->updateProgress(
            $request->progress_percentage,
            $request->module_data
        );

        // If course is completed, generate certificate
        if ($enrollment->isCompleted()) {
            $this->generateCertificate($enrollment);
        }

        return response()->json([
            'success' => true,
            'progress' => $enrollment->progress_percentage,
            'status' => $enrollment->status,
        ]);
    }

    public function unenroll(Request $request, Course $course)
    {
        $user = Auth::user();

        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if (!$enrollment) {
            return redirect()->back()->with('error', 'You are not enrolled in this course.');
        }

        if ($enrollment->status === 'completed') {
            return redirect()->back()->with('error', 'Cannot unenroll from a completed course.');
        }

        $enrollment->delete();

        return redirect()->back()->with('success', 'Successfully unenrolled from the course.');
    }

    public function continue(Course $course)
    {
        $user = Auth::user();

        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if (!$enrollment) {
            return redirect()->route('dashboard.courses.index')
                ->with('error', 'You must be enrolled to access this course.');
        }

        // If not started yet, mark as started
        if ($enrollment->status === 'enrolled') {
            $enrollment->status = 'in_progress';
            $enrollment->started_at = now();
            $enrollment->save();
        }

        return view('dashboard.courses.learn', compact('course', 'enrollment'));
    }

    public function completeModule(Request $request, Course $course)
    {
        $user = Auth::user();

        $request->validate([
            'module_id' => 'required|string',
            'module_progress' => 'required|integer|min:0|max:100',
        ]);

        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if (!$enrollment) {
            return response()->json(['error' => 'Not enrolled in this course'], 403);
        }

        $progressData = $enrollment->progress_data ?? [];
        $progressData['modules'][$request->module_id] = [
            'progress' => $request->module_progress,
            'completed_at' => now(),
        ];

        // Calculate overall progress
        $totalModules = count($progressData['modules'] ?? []);
        $completedModules = collect($progressData['modules'] ?? [])
            ->where('progress', 100)
            ->count();

        $overallProgress = $totalModules > 0 ? ($completedModules / $totalModules) * 100 : 0;

        $enrollment->updateProgress($overallProgress, $progressData);

        return response()->json([
            'success' => true,
            'progress' => $enrollment->progress_percentage,
            'status' => $enrollment->status,
            'completed' => $enrollment->isCompleted(),
        ]);
    }

    private function generateCertificate(Enrollment $enrollment)
    {
        // Check if certificate already exists
        $existingCertificate = \App\Models\Certificate::where('user_id', $enrollment->user_id)
            ->where('course_id', $enrollment->course_id)
            ->where('certificate_type', 'course')
            ->first();

        if ($existingCertificate) {
            return;
        }

        \App\Models\Certificate::create([
            'user_id' => $enrollment->user_id,
            'certificate_type' => 'course',
            'course_id' => $enrollment->course_id,
            'title' => $enrollment->course->title,
            'description' => 'Certificate of completion for ' . $enrollment->course->title,
            'cpd_credits' => $enrollment->course->cpd_credits,
            'issued_at' => now(),
            'status' => 'active',
        ]);
    }
}
