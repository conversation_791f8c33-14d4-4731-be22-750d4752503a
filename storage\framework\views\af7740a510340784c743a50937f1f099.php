<div class="container-fluid p-0 mb-5">
    <div class="owl-carousel header-carousel position-relative">
        <?php $__empty_1 = true; $__currentLoopData = $carouselSlides; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slide): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="owl-carousel-item position-relative">
                <img class="img-fluid" src="<?php echo e($slide->image ? asset('storage/' . $slide->image) : asset('img/carousel-1.jpg')); ?>" alt="<?php echo e($slide->title); ?>">
                <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center" style="background: rgba(46, 139, 87, .7);">
                    <div class="container">
                        <div class="row justify-content-start">
                            <div class="col-10 col-lg-8">
                                <?php if($slide->subtitle): ?>
                                    <h5 class="text-white text-uppercase mb-3 animated slideInDown"><?php echo e($slide->subtitle); ?></h5>
                                <?php endif; ?>
                                <h1 class="display-3 text-white animated slideInDown mb-4"><?php echo e($slide->title); ?></h1>
                                <p class="fs-5 fw-medium text-white mb-4 pb-2"><?php echo e($slide->description); ?></p>
                                <div class="d-flex flex-wrap gap-3">
                                    <?php if($slide->primary_button_text && $slide->primary_button_link): ?>
                                        <a href="<?php echo e($slide->primary_button_link); ?>" class="btn btn-primary py-md-3 px-md-5 animated slideInLeft">
                                            <i class="fas fa-info-circle me-2"></i><?php echo e($slide->primary_button_text); ?>

                                        </a>
                                    <?php endif; ?>
                                    <?php if($slide->secondary_button_text && $slide->secondary_button_link): ?>
                                        <a href="<?php echo e($slide->secondary_button_link); ?>" class="btn btn-secondary py-md-3 px-md-5 animated slideInRight">
                                            <i class="fas fa-video me-2"></i><?php echo e($slide->secondary_button_text); ?>

                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <!-- Fallback content if no slides are available -->
            <div class="owl-carousel-item position-relative">
                <img class="img-fluid" src="<?php echo e(asset('img/carousel-1.jpg')); ?>" alt="Virtual Medical Education">
                <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center" style="background: rgba(46, 139, 87, .7);">
                    <div class="container">
                        <div class="row justify-content-start">
                            <div class="col-10 col-lg-8">
                                <h5 class="text-white text-uppercase mb-3 animated slideInDown">Virtual Medical Education</h5>
                                <h1 class="display-3 text-white animated slideInDown mb-4">Advanced Continuing Professional Development</h1>
                                <p class="fs-5 fw-medium text-white mb-4 pb-2">Join thousands of healthcare professionals in our interactive virtual learning environment. Access cutting-edge medical education from anywhere, anytime.</p>
                                <a href="<?php echo e(route('about')); ?>" class="btn btn-primary py-md-3 px-md-5 me-3 animated slideInLeft">
                                    <i class="fas fa-info-circle me-2"></i>Learn More
                                </a>
                                <a href="<?php echo e(route('booking')); ?>" class="btn btn-secondary py-md-3 px-md-5 animated slideInRight">
                                    <i class="fas fa-video me-2"></i>Join Live Session
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\VCH\resources\views/layouts/include/carousel.blade.php ENDPATH**/ ?>