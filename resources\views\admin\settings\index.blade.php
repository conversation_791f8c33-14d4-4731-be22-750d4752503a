@extends('layouts.dashboard')

@section('title', 'System Settings')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">System Settings</h1>
            <p class="mb-0 text-muted">Configure system-wide settings and maintenance options</p>
        </div>
        <div class="btn-group">
            <form method="POST" action="{{ route('admin.settings.clear-cache') }}" class="d-inline">
                @csrf
                <button type="submit" class="btn btn-warning" onclick="return confirm('Are you sure you want to clear all caches?')">
                    <i class="fas fa-broom me-2"></i>Clear Cache
                </button>
            </form>
            <form method="POST" action="{{ route('admin.settings.optimize') }}" class="d-inline">
                @csrf
                <button type="submit" class="btn btn-success" onclick="return confirm('Are you sure you want to optimize the application?')">
                    <i class="fas fa-rocket me-2"></i>Optimize
                </button>
            </form>
        </div>
    </div>

    <div class="row">
        <!-- Settings Form -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Application Settings</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.settings.update') }}">
                        @csrf
                        @method('PUT')

                        <!-- General Settings -->
                        <div class="form-section mb-4">
                            <h5 class="section-title">General Settings</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="app_name">Application Name</label>
                                        <input type="text" class="form-control @error('app_name') is-invalid @enderror" 
                                               id="app_name" name="app_name" value="{{ old('app_name', $settings['app_name']) }}" required>
                                        @error('app_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="timezone">Timezone</label>
                                        <select class="form-control @error('timezone') is-invalid @enderror" id="timezone" name="timezone" required>
                                            <option value="UTC" {{ $settings['timezone'] === 'UTC' ? 'selected' : '' }}>UTC</option>
                                            <option value="America/New_York" {{ $settings['timezone'] === 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                                            <option value="America/Chicago" {{ $settings['timezone'] === 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                                            <option value="America/Denver" {{ $settings['timezone'] === 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                                            <option value="America/Los_Angeles" {{ $settings['timezone'] === 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                                            <option value="Europe/London" {{ $settings['timezone'] === 'Europe/London' ? 'selected' : '' }}>London</option>
                                        </select>
                                        @error('timezone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-3">
                                <label for="app_description">Application Description</label>
                                <textarea class="form-control @error('app_description') is-invalid @enderror" 
                                          id="app_description" name="app_description" rows="3">{{ old('app_description', $settings['app_description']) }}</textarea>
                                @error('app_description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Contact Settings -->
                        <div class="form-section mb-4">
                            <h5 class="section-title">Contact Settings</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="contact_email">Contact Email</label>
                                        <input type="email" class="form-control @error('contact_email') is-invalid @enderror" 
                                               id="contact_email" name="contact_email" value="{{ old('contact_email', $settings['contact_email']) }}" required>
                                        @error('contact_email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="support_email">Support Email</label>
                                        <input type="email" class="form-control @error('support_email') is-invalid @enderror" 
                                               id="support_email" name="support_email" value="{{ old('support_email', $settings['support_email']) }}" required>
                                        @error('support_email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- User Settings -->
                        <div class="form-section mb-4">
                            <h5 class="section-title">User Settings</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input type="hidden" name="user_registration" value="0">
                                        <input type="checkbox" class="form-check-input" id="user_registration" 
                                               name="user_registration" value="1" {{ $settings['user_registration'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="user_registration">
                                            Allow User Registration
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input type="hidden" name="email_verification" value="0">
                                        <input type="checkbox" class="form-check-input" id="email_verification" 
                                               name="email_verification" value="1" {{ $settings['email_verification'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="email_verification">
                                            Require Email Verification
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Settings -->
                        <div class="form-section mb-4">
                            <h5 class="section-title">System Settings</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="max_file_size">Max File Size (MB)</label>
                                        <input type="number" class="form-control @error('max_file_size') is-invalid @enderror" 
                                               id="max_file_size" name="max_file_size" value="{{ old('max_file_size', $settings['max_file_size']) }}" 
                                               min="1" max="100" required>
                                        @error('max_file_size')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="session_lifetime">Session Lifetime (minutes)</label>
                                        <input type="number" class="form-control @error('session_lifetime') is-invalid @enderror" 
                                               id="session_lifetime" name="session_lifetime" value="{{ old('session_lifetime', $settings['session_lifetime']) }}" 
                                               min="1" max="1440" required>
                                        @error('session_lifetime')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="col-lg-4">
            <!-- System Info -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
                </div>
                <div class="card-body">
                    <div class="system-info">
                        <div class="info-item">
                            <strong>PHP Version:</strong> {{ $systemInfo['php_version'] }}
                        </div>
                        <div class="info-item">
                            <strong>Laravel Version:</strong> {{ $systemInfo['laravel_version'] }}
                        </div>
                        <div class="info-item">
                            <strong>Environment:</strong> 
                            <span class="badge badge-{{ $systemInfo['environment'] === 'production' ? 'success' : 'warning' }}">
                                {{ ucfirst($systemInfo['environment']) }}
                            </span>
                        </div>
                        <div class="info-item">
                            <strong>Debug Mode:</strong> 
                            <span class="badge badge-{{ $systemInfo['debug_mode'] ? 'danger' : 'success' }}">
                                {{ $systemInfo['debug_mode'] ? 'Enabled' : 'Disabled' }}
                            </span>
                        </div>
                        <div class="info-item">
                            <strong>Database:</strong> {{ ucfirst($systemInfo['database_connection']) }}
                        </div>
                        <div class="info-item">
                            <strong>Cache Driver:</strong> {{ ucfirst($systemInfo['cache_driver']) }}
                        </div>
                        <div class="info-item">
                            <strong>Memory Limit:</strong> {{ $systemInfo['memory_limit'] }}
                        </div>
                        <div class="info-item">
                            <strong>Upload Max Size:</strong> {{ $systemInfo['upload_max_filesize'] }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cache Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Cache Information</h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            Clear Cache
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <form method="POST" action="{{ route('admin.settings.clear-cache') }}">
                                    @csrf
                                    <input type="hidden" name="type" value="config">
                                    <button type="submit" class="dropdown-item">Config Cache</button>
                                </form>
                            </li>
                            <li>
                                <form method="POST" action="{{ route('admin.settings.clear-cache') }}">
                                    @csrf
                                    <input type="hidden" name="type" value="route">
                                    <button type="submit" class="dropdown-item">Route Cache</button>
                                </form>
                            </li>
                            <li>
                                <form method="POST" action="{{ route('admin.settings.clear-cache') }}">
                                    @csrf
                                    <input type="hidden" name="type" value="view">
                                    <button type="submit" class="dropdown-item">View Cache</button>
                                </form>
                            </li>
                            <li>
                                <form method="POST" action="{{ route('admin.settings.clear-cache') }}">
                                    @csrf
                                    <input type="hidden" name="type" value="application">
                                    <button type="submit" class="dropdown-item">Application Cache</button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="system-info">
                        <div class="info-item">
                            <strong>Driver:</strong> {{ ucfirst($cacheInfo['driver']) }}
                        </div>
                        <div class="info-item">
                            <strong>Size:</strong> {{ $cacheInfo['size'] }}
                        </div>
                        <div class="info-item">
                            <strong>Files:</strong> {{ number_format($cacheInfo['files']) }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Storage Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Storage Information</h6>
                </div>
                <div class="card-body">
                    <div class="system-info">
                        <div class="info-item">
                            <strong>Used Space:</strong> {{ $storageInfo['size'] }}
                        </div>
                        <div class="info-item">
                            <strong>Files:</strong> {{ number_format($storageInfo['files']) }}
                        </div>
                        <div class="info-item">
                            <strong>Free Space:</strong> {{ $storageInfo['disk_free_space'] }}
                        </div>
                        <div class="info-item">
                            <strong>Total Space:</strong> {{ $storageInfo['disk_total_space'] }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Maintenance Mode -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Maintenance Mode</h6>
                </div>
                <div class="card-body text-center">
                    @if($settings['maintenance_mode'])
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Maintenance mode is currently <strong>ENABLED</strong>
                        </div>
                        <form method="POST" action="{{ route('admin.settings.toggle-maintenance') }}">
                            @csrf
                            <button type="submit" class="btn btn-success" onclick="return confirm('Are you sure you want to disable maintenance mode?')">
                                <i class="fas fa-play me-2"></i>Disable Maintenance Mode
                            </button>
                        </form>
                    @else
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            Maintenance mode is currently <strong>DISABLED</strong>
                        </div>
                        <form method="POST" action="{{ route('admin.settings.toggle-maintenance') }}">
                            @csrf
                            <button type="submit" class="btn btn-warning" onclick="return confirm('Are you sure you want to enable maintenance mode? This will make the site unavailable to users.')">
                                <i class="fas fa-pause me-2"></i>Enable Maintenance Mode
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.form-section {
    border-bottom: 1px solid #e3e6f0;
    padding-bottom: 1.5rem;
}

.section-title {
    color: #5a5c69;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #4e73df;
    display: inline-block;
}

.system-info .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f1f1;
}

.system-info .info-item:last-child {
    border-bottom: none;
}

.text-gray-800 { color: #5a5c69 !important; }
</style>
@endpush
