<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Testimonial>
 */
class TestimonialFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $testimonials = [
            [
                'client_name' => 'Dr. <PERSON>',
                'position' => 'Emergency Physician',
                'company' => 'Central Hospital',
                'testimonial' => 'The advanced cardiac life support course was exceptional. The hands-on training and simulation scenarios prepared me for real-life emergencies better than any other course I\'ve taken.',
                'rating' => 5,
            ],
            [
                'client_name' => 'Nurse <PERSON>',
                'position' => 'Head Nurse',
                'company' => 'Community Health Center',
                'testimonial' => 'The pediatric emergency care certification has transformed our department\'s approach to treating children. The instructors were knowledgeable and the course materials were comprehensive.',
                'rating' => 5,
            ],
            [
                'client_name' => 'Dr. <PERSON>',
                'position' => 'Medical Director',
                'company' => 'Regional Medical Center',
                'testimonial' => 'VCH\'s training programs have become an essential part of our staff development. The quality of instruction and flexibility of the platform make it our preferred provider for continuing education.',
                'rating' => 4,
            ],
            [
                'client_name' => '<PERSON>',
                'position' => 'Healthcare Administrator',
                'company' => 'Valley Health System',
                'testimonial' => 'Implementing VCH\'s training platform across our health system has standardized our approach to continuing education and significantly improved staff satisfaction with professional development opportunities.',
                'rating' => 5,
            ],
            [
                'client_name' => 'Dr. Emily Chen',
                'position' => 'Pediatrician',
                'company' => 'Children\'s Medical Group',
                'testimonial' => 'The pediatric advanced life support course was incredibly well-designed. The mix of theory and practical exercises helped cement the knowledge in a way that traditional courses don\'t achieve.',
                'rating' => 4,
            ],
        ];

        $testimonialData = $this->faker->randomElement($testimonials);

        return [
            'client_name' => $testimonialData['client_name'],
            'client_position' => $testimonialData['position'],
            'client_company' => $testimonialData['company'],
            'testimonial' => $testimonialData['testimonial'],
            'rating' => $testimonialData['rating'],
            'client_image' => $this->faker->optional(0.8)->passthrough('testimonials/testimonial-' . $this->faker->numberBetween(1, 5) . '.jpg'),
            'order' => $this->faker->numberBetween(1, 10),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'created_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the testimonial is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the testimonial is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the testimonial has a high rating.
     */
    public function highRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => $this->faker->numberBetween(4, 5),
        ]);
    }

    /**
     * Indicate that the testimonial has an image.
     */
    public function withImage(): static
    {
        return $this->state(fn (array $attributes) => [
            'client_image' => 'testimonials/testimonial-' . $this->faker->numberBetween(1, 5) . '.jpg',
        ]);
    }
}