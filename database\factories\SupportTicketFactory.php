<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SupportTicket>
 */
class SupportTicketFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = ['technical', 'billing', 'course', 'certification', 'account', 'general'];
        $priorities = ['low', 'medium', 'high', 'urgent'];
        $statuses = ['open', 'in_progress', 'resolved', 'closed'];

        $subjects = [
            'Unable to access course materials',
            'Certificate download issue',
            'Payment not processed',
            'Login problems',
            'Video playback issues',
            'Course enrollment error',
            'Profile update problems',
            'Notification settings not working',
            'Live session connection issues',
            'CPD credits not showing',
            'Password reset not working',
            'Course progress not saving',
            'Mobile app issues',
            'Browser compatibility problems',
            'Account verification issues'
        ];

        $messages = [
            'I am experiencing issues with accessing my course materials. The page keeps loading but never displays the content.',
            'I completed my course but cannot download my certificate. The download link appears to be broken.',
            'My payment was processed but I still cannot access the premium content. Please help resolve this issue.',
            'I cannot log into my account. I have tried resetting my password multiple times but the email never arrives.',
            'The course videos are not playing properly. They buffer constantly and the audio is out of sync.',
            'I tried to enroll in a course but received an error message. The payment went through but I am not enrolled.',
            'I cannot update my profile information. Every time I try to save changes, I get an error message.',
            'My notification settings are not working correctly. I am receiving emails even though I disabled them.',
            'I cannot connect to the live session. The meeting link does not work and I keep getting connection errors.',
            'My CPD credits are not showing up in my profile even though I completed several courses.',
        ];

        $ticketNumber = 'VCH-' . now()->year . '-' . str_pad($this->faker->unique()->numberBetween(1, 9999), 4, '0', STR_PAD_LEFT);

        return [
            'user_id' => User::factory(),
            'ticket_number' => $ticketNumber,
            'subject' => $this->faker->randomElement($subjects),
            'message' => $this->faker->randomElement($messages),
            'category' => $this->faker->randomElement($categories),
            'priority' => $this->faker->randomElement($priorities),
            'status' => $this->faker->randomElement($statuses),
            'assigned_to' => $this->faker->optional(0.6)->randomElement([
                'Support Agent 1',
                'Support Agent 2',
                'Technical Support',
                'Billing Support',
                'Course Support'
            ]),
            'resolution' => $this->faker->optional(0.4)->paragraph(),
            'resolved_at' => $this->faker->optional(0.4)->dateTimeBetween('-1 month', 'now'),
            'created_at' => $this->faker->dateTimeBetween('-3 months', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ];
    }

    /**
     * Indicate that the ticket is open.
     */
    public function open(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'open',
            'resolved_at' => null,
            'resolution' => null,
        ]);
    }

    /**
     * Indicate that the ticket is resolved.
     */
    public function resolved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'resolved',
            'resolved_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'resolution' => $this->faker->paragraph(),
        ]);
    }

    /**
     * Indicate that the ticket is high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'high',
        ]);
    }

    /**
     * Indicate that the ticket is urgent.
     */
    public function urgent(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'urgent',
        ]);
    }

    /**
     * Indicate that the ticket is technical.
     */
    public function technical(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'technical',
        ]);
    }

    /**
     * Indicate that the ticket is billing related.
     */
    public function billing(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'billing',
        ]);
    }
}
