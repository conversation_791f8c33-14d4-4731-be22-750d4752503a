<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Course;
use App\Models\LiveSession;
use App\Models\CarouselSlide;
use App\Models\Service;
use App\Models\TeamMember;
use App\Models\Testimonial;
use App\Models\FAQ;
use App\Models\SupportTicket;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'role' => 'admin',
                'phone' => '+1234567890',
                'organization' => 'VCH Administration',
                'job_title' => 'System Administrator',
                'bio' => 'System administrator with full access to all platform features.',
                'is_active' => true,
            ]
        );

        // Create instructor user
        $instructor = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Dr. <PERSON>',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'role' => 'instructor',
                'phone' => '+**********',
                'organization' => 'VCH Medical Center',
                'job_title' => 'Senior Medical Instructor',
                'bio' => 'Experienced medical professional with 15+ years in healthcare education.',
                'is_active' => true,
            ]
        );

        // Create regular users
        $users = [];
        for ($i = 1; $i <= 10; $i++) {
            $users[] = User::updateOrCreate(
                ['email' => "user{$i}@example.com"],
                [
                    'name' => "User {$i}",
                    'email_verified_at' => now(),
                    'password' => Hash::make('password'),
                    'role' => 'user',
                    'phone' => '+123456789' . $i,
                    'organization' => 'Healthcare Organization ' . $i,
                    'job_title' => ['Nurse', 'Doctor', 'Administrator', 'Technician', 'Manager'][array_rand(['Nurse', 'Doctor', 'Administrator', 'Technician', 'Manager'])],
                    'is_active' => true,
                ]
            );
        }

        // Create courses
        $courses = [
            [
                'title' => 'Advanced Cardiac Life Support (ACLS)',
                'description' => 'Comprehensive training in advanced cardiovascular life support techniques for healthcare professionals.',
                'short_description' => 'Learn advanced cardiac life support techniques and protocols.',
                'category' => 'Emergency Medicine',
                'level' => 'Advanced',
                'duration_minutes' => 480,
                'price' => 199.99,
                'instructor_name' => 'Dr. Sarah Johnson',
                'cpd_credits' => 8.0,
                'status' => 'published',
                'is_featured' => true,
            ],
            [
                'title' => 'Pediatric Emergency Medicine',
                'description' => 'Specialized training in emergency medical care for pediatric patients.',
                'short_description' => 'Emergency care protocols specifically designed for children.',
                'category' => 'Emergency Medicine',
                'level' => 'Intermediate',
                'duration_minutes' => 360,
                'price' => 149.99,
                'instructor_name' => 'Dr. Michael Chen',
                'cpd_credits' => 6.0,
                'status' => 'published',
                'is_featured' => true,
            ],
            [
                'title' => 'Healthcare Quality Management',
                'description' => 'Learn the fundamentals of quality management in healthcare settings.',
                'short_description' => 'Quality improvement strategies for healthcare organizations.',
                'category' => 'Healthcare Management',
                'level' => 'Beginner',
                'duration_minutes' => 240,
                'price' => 99.99,
                'instructor_name' => 'Dr. Lisa Rodriguez',
                'cpd_credits' => 4.0,
                'status' => 'published',
                'is_featured' => false,
            ],
        ];

        foreach ($courses as $courseData) {
            $courseData['slug'] = \Illuminate\Support\Str::slug($courseData['title']);
            $courseData['instructor'] = $courseData['instructor_name']; // Add instructor field
            $courseData['content'] = $courseData['description']; // Add content field
            $courseData['duration_hours'] = round($courseData['duration_minutes'] / 60, 1); // Add duration_hours field
            Course::updateOrCreate(
                ['slug' => $courseData['slug']],
                $courseData
            );
        }

        // Create live sessions
        $sessions = [
            [
                'title' => 'COVID-19 Update: Latest Protocols',
                'description' => 'Stay updated with the latest COVID-19 treatment protocols and safety measures.',
                'instructor' => 'Dr. Sarah Johnson',
                'instructor_title' => 'Chief Medical Officer',
                'session_date' => now()->addDays(7),
                'duration_minutes' => 90,
                'max_participants' => 100,
                'status' => 'scheduled',
                'cpd_credits' => 1.5,
                'category' => 'Emergency Medicine',
            ],
            [
                'title' => 'Mental Health in Healthcare Workers',
                'description' => 'Addressing mental health challenges faced by healthcare professionals.',
                'instructor' => 'Dr. Michael Chen',
                'instructor_title' => 'Director of Education',
                'session_date' => now()->addDays(14),
                'duration_minutes' => 120,
                'max_participants' => 50,
                'status' => 'scheduled',
                'cpd_credits' => 2.0,
                'category' => 'Healthcare Management',
            ],
        ];

        foreach ($sessions as $sessionData) {
            LiveSession::updateOrCreate(
                ['title' => $sessionData['title']],
                $sessionData
            );
        }

        // Create carousel slides
        $slides = [
            [
                'title' => 'Welcome to VCH Learning',
                'subtitle' => 'Professional Healthcare Education',
                'description' => 'Advance your healthcare career with our comprehensive training programs.',
                'image' => 'carousel/slide1.jpg',
                'primary_button_text' => 'Explore Courses',
                'primary_button_link' => '/courses',
                'order' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'Live Interactive Sessions',
                'subtitle' => 'Learn from Industry Experts',
                'description' => 'Join live sessions with experienced healthcare professionals.',
                'image' => 'carousel/slide2.jpg',
                'primary_button_text' => 'View Sessions',
                'primary_button_link' => '/live-sessions',
                'order' => 2,
                'is_active' => true,
            ],
        ];

        foreach ($slides as $slideData) {
            CarouselSlide::create($slideData);
        }

        // Create services
        $services = [
            [
                'title' => 'Professional Training',
                'description' => 'Comprehensive healthcare training programs designed by industry experts.',
                'icon' => 'fas fa-graduation-cap',
                'order' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'Certification Programs',
                'description' => 'Earn recognized certifications to advance your healthcare career.',
                'icon' => 'fas fa-certificate',
                'order' => 2,
                'is_active' => true,
            ],
            [
                'title' => 'Live Sessions',
                'description' => 'Interactive live sessions with real-time Q&A and expert guidance.',
                'icon' => 'fas fa-video',
                'order' => 3,
                'is_active' => true,
            ],
        ];

        foreach ($services as $serviceData) {
            Service::create($serviceData);
        }

        // Create team members
        $teamMembers = [
            [
                'name' => 'Dr. Sarah Johnson',
                'position' => 'Chief Medical Officer',
                'bio' => 'Dr. Johnson brings over 20 years of experience in emergency medicine and medical education.',
                'image' => 'team/sarah-johnson.jpg',
                'email' => '<EMAIL>',
                'linkedin' => 'https://linkedin.com/in/sarahjohnson',
                'order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Dr. Michael Chen',
                'position' => 'Director of Education',
                'bio' => 'Specializing in pediatric emergency medicine with extensive teaching experience.',
                'image' => 'team/michael-chen.jpg',
                'email' => '<EMAIL>',
                'order' => 2,
                'is_active' => true,
            ],
        ];

        foreach ($teamMembers as $memberData) {
            TeamMember::create($memberData);
        }

        // Create testimonials
        $testimonials = [
            [
                'client_name' => 'Jennifer Martinez',
                'client_position' => 'Registered Nurse',
                'client_company' => 'City General Hospital',
                'testimonial' => 'The ACLS course was incredibly comprehensive and well-structured. The instructors were knowledgeable and the hands-on practice was invaluable.',
                'rating' => 5,
                'order' => 1,
                'is_active' => true,
            ],
            [
                'client_name' => 'Dr. Robert Kim',
                'client_position' => 'Emergency Physician',
                'client_company' => 'Metro Medical Center',
                'testimonial' => 'Excellent training platform with high-quality content. The certification process was smooth and the credentials are well-recognized.',
                'rating' => 5,
                'order' => 2,
                'is_active' => true,
            ],
        ];

        foreach ($testimonials as $testimonialData) {
            Testimonial::create($testimonialData);
        }

        // Create FAQs
        $faqs = [
            [
                'question' => 'How do I enroll in a course?',
                'answer' => 'You can enroll in courses by browsing our course catalog and clicking the "Enroll Now" button. Payment is processed securely through our platform.',
                'category' => 'enrollment',
                'order' => 1,
                'is_active' => true,
            ],
            [
                'question' => 'Are the certifications recognized?',
                'answer' => 'Yes, our certifications are recognized by major healthcare organizations and meet industry standards for continuing education.',
                'category' => 'certification',
                'order' => 2,
                'is_active' => true,
            ],
            [
                'question' => 'Can I access courses on mobile devices?',
                'answer' => 'Absolutely! Our platform is fully responsive and works seamlessly on smartphones, tablets, and desktop computers.',
                'category' => 'technical',
                'order' => 3,
                'is_active' => true,
            ],
        ];

        foreach ($faqs as $faqData) {
            FAQ::create($faqData);
        }

        // Create sample support tickets
        $tickets = [
            [
                'user_id' => $users[0]->id,
                'ticket_number' => 'VCH-2024-0001',
                'subject' => 'Unable to access course materials',
                'message' => 'I enrolled in the ACLS course but cannot access the course materials. Please help.',
                'category' => 'technical',
                'priority' => 'medium',
                'status' => 'open',
            ],
            [
                'user_id' => $users[1]->id,
                'ticket_number' => 'VCH-2024-0002',
                'subject' => 'Certificate download issue',
                'message' => 'I completed my course but the certificate download link is not working.',
                'category' => 'certification',
                'priority' => 'high',
                'status' => 'in_progress',
            ],
        ];

        foreach ($tickets as $ticketData) {
            SupportTicket::updateOrCreate(
                ['ticket_number' => $ticketData['ticket_number']],
                $ticketData
            );
        }

        $this->command->info('Demo data seeded successfully!');
        $this->command->info('Admin login: <EMAIL> / password');
        $this->command->info('Instructor login: <EMAIL> / password');
        $this->command->info('User login: <EMAIL> / password (or user2-user10)');
    }
}
