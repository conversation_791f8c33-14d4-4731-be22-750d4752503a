@extends('layouts.dashboard')

@section('page-title', 'Carousel Management')

@section('content')
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Carousel Management</h2>
                    <p class="text-muted mb-0">Manage homepage carousel slides</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary" id="bulkActionsBtn" style="display: none;">
                        <i class="fas fa-tasks me-1"></i>Bulk Actions
                    </button>
                    <a href="{{ route('admin.content.carousel.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Slide
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="Search slides...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary" id="enableSortBtn">
                                    <i class="fas fa-sort me-1"></i>Reorder
                                </button>
                                <button class="btn btn-outline-secondary" onclick="location.reload()">
                                    <i class="fas fa-sync me-1"></i>Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Carousel Slides -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-images me-2 text-primary"></i>Carousel Slides
                            <span class="badge bg-secondary ms-2">{{ $slides->total() }}</span>
                        </h5>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="selectAll">
                            <label class="form-check-label" for="selectAll">Select All</label>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if($slides->count() > 0)
                        <div id="sortableSlides" class="row">
                            @foreach($slides as $slide)
                            <div class="col-lg-6 col-xl-4 mb-4" data-id="{{ $slide->id }}">
                                <div class="card border h-100 slide-card">
                                    <div class="position-relative">
                                        <img src="{{ Storage::url($slide->image) }}" 
                                             class="card-img-top" 
                                             style="height: 200px; object-fit: cover;"
                                             alt="{{ $slide->title }}">
                                        
                                        <!-- Status Badge -->
                                        <span class="position-absolute top-0 end-0 m-2">
                                            <span class="badge bg-{{ $slide->is_active ? 'success' : 'secondary' }}">
                                                {{ $slide->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </span>

                                        <!-- Order Badge -->
                                        <span class="position-absolute top-0 start-0 m-2">
                                            <span class="badge bg-primary">Order: {{ $slide->order }}</span>
                                        </span>

                                        <!-- Selection Checkbox -->
                                        <div class="position-absolute bottom-0 start-0 m-2">
                                            <div class="form-check">
                                                <input class="form-check-input slide-checkbox" 
                                                       type="checkbox" 
                                                       value="{{ $slide->id }}"
                                                       id="slide{{ $slide->id }}">
                                                <label class="form-check-label text-white" for="slide{{ $slide->id }}">
                                                    Select
                                                </label>
                                            </div>
                                        </div>

                                        <!-- Drag Handle -->
                                        <div class="position-absolute bottom-0 end-0 m-2 drag-handle" style="display: none;">
                                            <i class="fas fa-grip-vertical text-white bg-dark p-1 rounded"></i>
                                        </div>
                                    </div>
                                    
                                    <div class="card-body">
                                        <h6 class="card-title">{{ $slide->title }}</h6>
                                        @if($slide->subtitle)
                                            <p class="text-muted small mb-2">{{ $slide->subtitle }}</p>
                                        @endif
                                        <p class="card-text small">{{ Str::limit($slide->description, 100) }}</p>
                                        
                                        @if($slide->primary_button_text || $slide->secondary_button_text)
                                            <div class="mb-2">
                                                @if($slide->primary_button_text)
                                                    <span class="badge bg-primary me-1">{{ $slide->primary_button_text }}</span>
                                                @endif
                                                @if($slide->secondary_button_text)
                                                    <span class="badge bg-outline-secondary">{{ $slide->secondary_button_text }}</span>
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                    
                                    <div class="card-footer bg-white border-0">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                Updated {{ $slide->updated_at->diffForHumans() }}
                                            </small>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('admin.content.carousel.edit', $slide) }}" 
                                                   class="btn btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-outline-{{ $slide->is_active ? 'warning' : 'success' }}" 
                                                        onclick="toggleStatus({{ $slide->id }}, {{ $slide->is_active ? 'false' : 'true' }})"
                                                        title="{{ $slide->is_active ? 'Deactivate' : 'Activate' }}">
                                                    <i class="fas fa-{{ $slide->is_active ? 'eye-slash' : 'eye' }}"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" 
                                                        onclick="deleteSlide({{ $slide->id }})" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $slides->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No carousel slides found</h5>
                            <p class="text-muted">Create your first carousel slide to get started.</p>
                            <a href="{{ route('admin.content.carousel.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add First Slide
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="bulkActionsForm">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="bulkAction" class="form-label">Select Action</label>
                        <select class="form-select" id="bulkAction" name="action" required>
                            <option value="">Choose action...</option>
                            <option value="activate">Activate Selected</option>
                            <option value="deactivate">Deactivate Selected</option>
                            <option value="delete">Delete Selected</option>
                        </select>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        This action will be applied to <span id="selectedCount">0</span> selected slide(s).
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Apply Action</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const slideCheckboxes = document.querySelectorAll('.slide-checkbox');
    const bulkActionsBtn = document.getElementById('bulkActionsBtn');
    const bulkActionsModal = new bootstrap.Modal(document.getElementById('bulkActionsModal'));
    const enableSortBtn = document.getElementById('enableSortBtn');
    const dragHandles = document.querySelectorAll('.drag-handle');
    let sortable = null;

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        slideCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionsButton();
    });

    // Individual checkbox change
    slideCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionsButton);
    });

    function updateBulkActionsButton() {
        const checkedBoxes = document.querySelectorAll('.slide-checkbox:checked');
        if (checkedBoxes.length > 0) {
            bulkActionsBtn.style.display = 'block';
            bulkActionsBtn.textContent = `Bulk Actions (${checkedBoxes.length})`;
        } else {
            bulkActionsBtn.style.display = 'none';
        }
    }

    // Bulk actions button click
    bulkActionsBtn.addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.slide-checkbox:checked');
        document.getElementById('selectedCount').textContent = checkedBoxes.length;
        bulkActionsModal.show();
    });

    // Bulk actions form submission
    document.getElementById('bulkActionsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const checkedBoxes = document.querySelectorAll('.slide-checkbox:checked');
        const ids = Array.from(checkedBoxes).map(cb => cb.value);
        const action = document.getElementById('bulkAction').value;

        if (!action || ids.length === 0) return;

        fetch('{{ route("admin.content.bulk-action") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                action: action,
                type: 'carousel',
                ids: ids
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again.');
        });
    });

    // Enable/disable sorting
    enableSortBtn.addEventListener('click', function() {
        if (sortable) {
            // Disable sorting
            sortable.destroy();
            sortable = null;
            this.innerHTML = '<i class="fas fa-sort me-1"></i>Reorder';
            this.classList.remove('btn-warning');
            this.classList.add('btn-outline-primary');
            dragHandles.forEach(handle => handle.style.display = 'none');
        } else {
            // Enable sorting
            sortable = Sortable.create(document.getElementById('sortableSlides'), {
                animation: 150,
                handle: '.drag-handle',
                onEnd: function(evt) {
                    const items = Array.from(evt.to.children).map((item, index) => ({
                        id: parseInt(item.dataset.id),
                        order: index + 1
                    }));

                    fetch('{{ route("admin.content.reorder") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                        },
                        body: JSON.stringify({
                            type: 'carousel',
                            items: items
                        })
                    });
                }
            });
            this.innerHTML = '<i class="fas fa-times me-1"></i>Cancel';
            this.classList.remove('btn-outline-primary');
            this.classList.add('btn-warning');
            dragHandles.forEach(handle => handle.style.display = 'block');
        }
    });

    // Search functionality
    document.getElementById('searchInput').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const slides = document.querySelectorAll('.slide-card');
        
        slides.forEach(slide => {
            const title = slide.querySelector('.card-title').textContent.toLowerCase();
            const description = slide.querySelector('.card-text').textContent.toLowerCase();
            
            if (title.includes(searchTerm) || description.includes(searchTerm)) {
                slide.closest('.col-lg-6').style.display = 'block';
            } else {
                slide.closest('.col-lg-6').style.display = 'none';
            }
        });
    });

    // Status filter
    document.getElementById('statusFilter').addEventListener('change', function() {
        const filterValue = this.value;
        const slides = document.querySelectorAll('.slide-card');
        
        slides.forEach(slide => {
            const badge = slide.querySelector('.badge');
            const isActive = badge.textContent.trim() === 'Active';
            
            if (!filterValue || 
                (filterValue === 'active' && isActive) || 
                (filterValue === 'inactive' && !isActive)) {
                slide.closest('.col-lg-6').style.display = 'block';
            } else {
                slide.closest('.col-lg-6').style.display = 'none';
            }
        });
    });
});

function toggleStatus(slideId, newStatus) {
    if (confirm('Are you sure you want to change the status of this slide?')) {
        // In a real implementation, this would make an AJAX call to update the status
        location.reload();
    }
}

function deleteSlide(slideId) {
    if (confirm('Are you sure you want to delete this slide? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/content/carousel/${slideId}`;
        form.innerHTML = `
            @csrf
            @method('DELETE')
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection
