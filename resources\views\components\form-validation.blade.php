@props([
    'errors' => null,
    'showSummary' => true,
    'showInline' => true,
    'summaryTitle' => 'Please correct the following errors:',
    'class' => '',
])

@php
    $errorBag = $errors ?? $errors ?? session('errors');
    $hasErrors = $errorBag && $errorBag->any();
@endphp

@if($hasErrors)
    <!-- Error Summary -->
    @if($showSummary)
        <div class="alert alert-danger alert-dismissible fade show validation-summary {{ $class }}" role="alert">
            <div class="d-flex align-items-start">
                <i class="fas fa-exclamation-triangle me-3 mt-1 flex-shrink-0"></i>
                <div class="flex-grow-1">
                    <h6 class="alert-heading mb-2">{{ $summaryTitle }}</h6>
                    <ul class="mb-0 ps-3">
                        @foreach($errorBag->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif
@endif

@once
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced form validation
    const forms = document.querySelectorAll('form[data-validate="true"], form.needs-validation');
    
    forms.forEach(form => {
        // Real-time validation
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            // Validate on blur
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            // Clear validation on input (for better UX)
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    clearFieldValidation(this);
                }
            });
        });
        
        // Form submission validation
        form.addEventListener('submit', function(e) {
            let isValid = true;
            
            inputs.forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                e.stopPropagation();
                
                // Scroll to first error
                const firstError = form.querySelector('.is-invalid');
                if (firstError) {
                    firstError.scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'center' 
                    });
                    firstError.focus();
                }
                
                // Show error summary if not already visible
                showValidationSummary(form);
            }
            
            form.classList.add('was-validated');
        });
    });
    
    function validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const required = field.hasAttribute('required');
        let isValid = true;
        let errorMessage = '';
        
        // Clear previous validation
        clearFieldValidation(field);
        
        // Required validation
        if (required && !value) {
            isValid = false;
            errorMessage = `${getFieldLabel(field)} is required.`;
        }
        
        // Type-specific validation
        if (value && isValid) {
            switch (type) {
                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address.';
                    }
                    break;
                    
                case 'url':
                    try {
                        new URL(value);
                    } catch {
                        isValid = false;
                        errorMessage = 'Please enter a valid URL.';
                    }
                    break;
                    
                case 'tel':
                    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
                    if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
                        isValid = false;
                        errorMessage = 'Please enter a valid phone number.';
                    }
                    break;
                    
                case 'number':
                    const min = field.getAttribute('min');
                    const max = field.getAttribute('max');
                    const numValue = parseFloat(value);
                    
                    if (isNaN(numValue)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid number.';
                    } else {
                        if (min && numValue < parseFloat(min)) {
                            isValid = false;
                            errorMessage = `Value must be at least ${min}.`;
                        }
                        if (max && numValue > parseFloat(max)) {
                            isValid = false;
                            errorMessage = `Value must be no more than ${max}.`;
                        }
                    }
                    break;
                    
                case 'password':
                    const minLength = field.getAttribute('minlength') || 8;
                    if (value.length < minLength) {
                        isValid = false;
                        errorMessage = `Password must be at least ${minLength} characters long.`;
                    }
                    break;
            }
        }
        
        // Custom pattern validation
        const pattern = field.getAttribute('pattern');
        if (value && pattern && isValid) {
            const regex = new RegExp(pattern);
            if (!regex.test(value)) {
                isValid = false;
                errorMessage = field.getAttribute('title') || 'Please match the required format.';
            }
        }
        
        // Length validation
        const minLength = field.getAttribute('minlength');
        const maxLength = field.getAttribute('maxlength');
        
        if (value && minLength && value.length < parseInt(minLength)) {
            isValid = false;
            errorMessage = `Must be at least ${minLength} characters long.`;
        }
        
        if (value && maxLength && value.length > parseInt(maxLength)) {
            isValid = false;
            errorMessage = `Must be no more than ${maxLength} characters long.`;
        }
        
        // Apply validation state
        if (!isValid) {
            field.classList.add('is-invalid');
            field.classList.remove('is-valid');
            showFieldError(field, errorMessage);
        } else if (value) {
            field.classList.add('is-valid');
            field.classList.remove('is-invalid');
        }
        
        return isValid;
    }
    
    function clearFieldValidation(field) {
        field.classList.remove('is-invalid', 'is-valid');
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    }
    
    function showFieldError(field, message) {
        let feedback = field.parentNode.querySelector('.invalid-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            field.parentNode.appendChild(feedback);
        }
        feedback.textContent = message;
    }
    
    function getFieldLabel(field) {
        const label = document.querySelector(`label[for="${field.id}"]`);
        if (label) {
            return label.textContent.replace('*', '').trim();
        }
        return field.getAttribute('placeholder') || field.name || 'This field';
    }
    
    function showValidationSummary(form) {
        let summary = form.querySelector('.validation-summary');
        if (!summary) {
            const errors = form.querySelectorAll('.is-invalid');
            if (errors.length > 0) {
                summary = document.createElement('div');
                summary.className = 'alert alert-danger validation-summary';
                summary.innerHTML = `
                    <div class="d-flex align-items-start">
                        <i class="fas fa-exclamation-triangle me-3 mt-1"></i>
                        <div>
                            <h6 class="alert-heading mb-2">Please correct the following errors:</h6>
                            <ul class="mb-0 ps-3">
                                ${Array.from(errors).map(error => {
                                    const feedback = error.parentNode.querySelector('.invalid-feedback');
                                    return `<li>${feedback ? feedback.textContent : 'Invalid input'}</li>`;
                                }).join('')}
                            </ul>
                        </div>
                    </div>
                `;
                form.insertBefore(summary, form.firstChild);
            }
        }
    }
    
    // Password strength indicator
    const passwordFields = document.querySelectorAll('input[type="password"]');
    passwordFields.forEach(field => {
        if (field.hasAttribute('data-strength')) {
            addPasswordStrengthIndicator(field);
        }
    });
    
    function addPasswordStrengthIndicator(field) {
        const indicator = document.createElement('div');
        indicator.className = 'password-strength mt-1';
        indicator.innerHTML = `
            <div class="progress" style="height: 4px;">
                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
            <small class="text-muted strength-text">Enter a password</small>
        `;
        field.parentNode.appendChild(indicator);
        
        field.addEventListener('input', function() {
            const strength = calculatePasswordStrength(this.value);
            const progressBar = indicator.querySelector('.progress-bar');
            const strengthText = indicator.querySelector('.strength-text');
            
            progressBar.style.width = strength.percentage + '%';
            progressBar.className = `progress-bar bg-${strength.color}`;
            strengthText.textContent = strength.text;
        });
    }
    
    function calculatePasswordStrength(password) {
        let score = 0;
        
        if (password.length >= 8) score += 1;
        if (password.length >= 12) score += 1;
        if (/[a-z]/.test(password)) score += 1;
        if (/[A-Z]/.test(password)) score += 1;
        if (/[0-9]/.test(password)) score += 1;
        if (/[^A-Za-z0-9]/.test(password)) score += 1;
        
        const strength = {
            0: { text: 'Very Weak', color: 'danger', percentage: 16 },
            1: { text: 'Weak', color: 'danger', percentage: 32 },
            2: { text: 'Fair', color: 'warning', percentage: 48 },
            3: { text: 'Good', color: 'info', percentage: 64 },
            4: { text: 'Strong', color: 'success', percentage: 80 },
            5: { text: 'Very Strong', color: 'success', percentage: 96 },
            6: { text: 'Excellent', color: 'success', percentage: 100 }
        };
        
        return strength[score] || strength[0];
    }
});
</script>
@endpush

@push('styles')
<style>
.validation-summary {
    border-left: 4px solid #dc3545;
}

.validation-summary .alert-heading {
    font-size: 1rem;
    font-weight: 600;
}

.validation-summary ul {
    margin-bottom: 0;
}

.validation-summary li {
    margin-bottom: 0.25rem;
}

.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.is-valid {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.password-strength .progress {
    height: 4px;
    background-color: #e9ecef;
}

.password-strength .strength-text {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: block;
}

/* Smooth transitions */
.form-control, .form-select {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Focus states */
.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-control:focus.is-invalid, .form-select:focus.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.form-control:focus.is-valid, .form-select:focus.is-valid {
    border-color: #198754;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}
</style>
@endpush
@endonce
