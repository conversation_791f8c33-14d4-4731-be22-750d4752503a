@extends('layouts.dashboard')

@section('page-title', 'Edit Team Member')

@section('content')
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Edit Team Member</h2>
                    <p class="text-muted mb-0">Update team member information</p>
                </div>
                <div>
                    <a href="{{ route('admin.content.team.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Team
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <form action="{{ route('admin.content.team.update', $teamMember->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-user me-2 text-primary"></i>Personal Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Name -->
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $teamMember->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Position -->
                                <div class="mb-3">
                                    <label for="position" class="form-label">Position/Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('position') is-invalid @enderror" 
                                           id="position" name="position" value="{{ old('position', $teamMember->position) }}" required>
                                    @error('position')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Bio -->
                        <div class="mb-3">
                            <label for="bio" class="form-label">Biography <span class="text-danger">*</span></label>
                            <x-rich-text-editor 
                                name="bio" 
                                label="" 
                                :required="true"
                                :value="old('bio', $teamMember->bio)"
                                toolbar="simple"
                                :height="200"
                                help="Enter a brief professional biography. Include education, experience, and specialties."
                            />
                        </div>

                        <!-- Profile Image -->
                        <div class="mb-3">
                            <label for="image" class="form-label">Profile Photo</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Upload a new photo to replace the current one. Recommended size: 400x400px</div>
                        </div>

                        <!-- Current Image Preview -->
                        <div id="currentImagePreview" class="mt-3">
                            @if($teamMember->image)
                                <div class="d-flex align-items-center">
                                    <img src="{{ asset('storage/' . $teamMember->image) }}" alt="{{ $teamMember->name }}" 
                                         class="img-fluid rounded-circle" style="max-height: 150px; max-width: 150px;">
                                    <div class="ms-3">
                                        <p class="mb-1">Current image</p>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image" value="1">
                                            <label class="form-check-label" for="remove_image">Remove current image</label>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <p class="text-muted">No image currently set</p>
                            @endif
                        </div>

                        <!-- New Image Preview -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <p class="mb-1">New image preview:</p>
                            <img id="previewImg" src="" alt="Preview" class="img-fluid rounded-circle" style="max-height: 150px; max-width: 150px;">
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-address-card me-2 text-primary"></i>Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Email -->
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email', $teamMember->email) }}">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Phone -->
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone', $teamMember->phone) }}">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-share-alt me-2 text-primary"></i>Social Media</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- LinkedIn -->
                                <div class="mb-3">
                                    <label for="linkedin" class="form-label">LinkedIn Profile</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fab fa-linkedin"></i></span>
                                        <input type="url" class="form-control @error('linkedin') is-invalid @enderror" 
                                               id="linkedin" name="linkedin" value="{{ old('linkedin', $teamMember->linkedin) }}"
                                               placeholder="https://linkedin.com/in/username">
                                    </div>
                                    @error('linkedin')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Twitter -->
                                <div class="mb-3">
                                    <label for="twitter" class="form-label">Twitter Profile</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fab fa-twitter"></i></span>
                                        <input type="url" class="form-control @error('twitter') is-invalid @enderror" 
                                               id="twitter" name="twitter" value="{{ old('twitter', $teamMember->twitter) }}"
                                               placeholder="https://twitter.com/username">
                                    </div>
                                    @error('twitter')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Facebook -->
                                <div class="mb-3">
                                    <label for="facebook" class="form-label">Facebook Profile</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fab fa-facebook"></i></span>
                                        <input type="url" class="form-control @error('facebook') is-invalid @enderror" 
                                               id="facebook" name="facebook" value="{{ old('facebook', $teamMember->facebook) }}"
                                               placeholder="https://facebook.com/username">
                                    </div>
                                    @error('facebook')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-cog me-2 text-primary"></i>Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Order -->
                                <div class="mb-3">
                                    <label for="order" class="form-label">Display Order <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                           id="order" name="order" value="{{ old('order', $teamMember->order) }}" min="0" required>
                                    @error('order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Lower numbers appear first</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Status -->
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">Status</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               value="1" {{ old('is_active', $teamMember->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active (profile will be visible on the website)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.content.team.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Cancel
                    </a>
                    <div>
                        <button type="submit" name="action" value="save" class="btn btn-outline-primary me-2">
                            <i class="fas fa-save me-1"></i>Save Draft
                        </button>
                        <button type="submit" name="action" value="publish" class="btn btn-primary">
                            <i class="fas fa-check me-1"></i>Save & Publish
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Preview Panel -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm sticky-top" style="top: 2rem;">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-eye me-2 text-primary"></i>Live Preview</h5>
                </div>
                <div class="card-body">
                    <div id="teamMemberPreview" class="text-center">
                        <div class="mb-4">
                            <div id="previewImageContainer" class="mb-3">
                                @if($teamMember->image)
                                    <img src="{{ asset('storage/' . $teamMember->image) }}" class="rounded-circle mx-auto" 
                                         style="width: 120px; height: 120px; object-fit: cover;" id="currentPreviewImg">
                                @else
                                    <div class="bg-light rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                                         style="width: 120px; height: 120px;">
                                        <i class="fas fa-user fa-3x text-muted"></i>
                                    </div>
                                @endif
                            </div>
                            <h5 id="previewName" class="mb-1">{{ $teamMember->name }}</h5>
                            <p id="previewPosition" class="text-muted mb-3">{{ $teamMember->position }}</p>
                            <div id="previewSocial" class="mb-3">
                                <a href="{{ $teamMember->linkedin }}" class="text-decoration-none me-2" id="previewLinkedin" 
                                   style="{{ $teamMember->linkedin ? 'display: inline-block;' : 'display: none;' }}">
                                    <i class="fab fa-linkedin fa-lg"></i>
                                </a>
                                <a href="{{ $teamMember->twitter }}" class="text-decoration-none me-2" id="previewTwitter" 
                                   style="{{ $teamMember->twitter ? 'display: inline-block;' : 'display: none;' }}">
                                    <i class="fab fa-twitter fa-lg"></i>
                                </a>
                                <a href="{{ $teamMember->facebook }}" class="text-decoration-none" id="previewFacebook" 
                                   style="{{ $teamMember->facebook ? 'display: inline-block;' : 'display: none;' }}">
                                    <i class="fab fa-facebook fa-lg"></i>
                                </a>
                            </div>
                            <div id="previewBio" class="text-muted small">
                                {!! $teamMember->bio !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image preview
        document.getElementById('image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                    updatePreview(true);
                };
                reader.readAsDataURL(file);
            }
        });

        // Remove image checkbox
        document.getElementById('remove_image').addEventListener('change', function(e) {
            if (this.checked) {
                updatePreviewImageRemoved();
            } else {
                updatePreview(false);
            }
        });

        // Live preview update
        function updatePreview(newImage = false) {
            const name = document.getElementById('name').value || 'Team Member Name';
            const position = document.getElementById('position').value || 'Position/Title';
            const bio = window.getRichTextContent ? window.getRichTextContent('bio') : document.querySelector('[name="bio"]').value || 'Member biography will appear here';
            const linkedin = document.getElementById('linkedin').value;
            const twitter = document.getElementById('twitter').value;
            const facebook = document.getElementById('facebook').value;
            
            // Update name and position
            document.getElementById('previewName').textContent = name;
            document.getElementById('previewPosition').textContent = position;
            
            // Update bio
            document.getElementById('previewBio').innerHTML = bio;
            
            // Update image if a new one is selected
            if (newImage) {
                const previewImg = document.getElementById('previewImg');
                if (previewImg && previewImg.src && previewImg.src !== window.location.href) {
                    const imgContainer = document.getElementById('previewImageContainer');
                    imgContainer.innerHTML = `<img src="${previewImg.src}" class="rounded-circle mx-auto" style="width: 120px; height: 120px; object-fit: cover;">`;
                }
            }
            
            // Update social links
            updateSocialLink('previewLinkedin', linkedin);
            updateSocialLink('previewTwitter', twitter);
            updateSocialLink('previewFacebook', facebook);
        }
        
        function updatePreviewImageRemoved() {
            const imgContainer = document.getElementById('previewImageContainer');
            imgContainer.innerHTML = `
                <div class="bg-light rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                     style="width: 120px; height: 120px;">
                    <i class="fas fa-user fa-3x text-muted"></i>
                </div>
            `;
        }
        
        function updateSocialLink(elementId, url) {
            const element = document.getElementById(elementId);
            if (url) {
                element.href = url;
                element.style.display = 'inline-block';
            } else {
                element.style.display = 'none';
            }
        }

        // Form field listeners for live preview
        ['name', 'position', 'linkedin', 'twitter', 'facebook'].forEach(id => {
            document.getElementById(id).addEventListener('input', function() {
                updatePreview(false);
            });
        });

        // Listen for rich text editor changes
        document.addEventListener('richTextAutoSave', function(e) {
            if (e.detail.editorId.includes('bio')) {
                updatePreview(false);
            }
        });

        // Form submission handling
        document.querySelector('form').addEventListener('submit', function(e) {
            const action = e.submitter.value;
            if (action === 'save') {
                document.getElementById('is_active').checked = false;
            } else if (action === 'publish') {
                document.getElementById('is_active').checked = true;
            }
        });
    });
</script>
@endpush
@endsection