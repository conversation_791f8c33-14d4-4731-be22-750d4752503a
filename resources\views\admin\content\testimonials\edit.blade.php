@extends('layouts.dashboard')

@section('page-title', 'Edit Testimonial')

@section('content')
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Edit Testimonial</h2>
                    <p class="text-muted mb-0">Update client testimonial information</p>
                </div>
                <div>
                    <a href="{{ route('admin.content.testimonials.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Testimonials
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <form action="{{ route('admin.content.testimonials.update', $testimonial->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-user me-2 text-primary"></i>Client Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Client Name -->
                                <div class="mb-3">
                                    <label for="client_name" class="form-label">Client Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('client_name') is-invalid @enderror" 
                                           id="client_name" name="client_name" value="{{ old('client_name', $testimonial->client_name) }}" required>
                                    @error('client_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Client Position -->
                                <div class="mb-3">
                                    <label for="client_position" class="form-label">Position/Title</label>
                                    <input type="text" class="form-control @error('client_position') is-invalid @enderror" 
                                           id="client_position" name="client_position" value="{{ old('client_position', $testimonial->client_position) }}">
                                    @error('client_position')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Client Company -->
                        <div class="mb-3">
                            <label for="client_company" class="form-label">Company/Organization</label>
                            <input type="text" class="form-control @error('client_company') is-invalid @enderror" 
                                   id="client_company" name="client_company" value="{{ old('client_company', $testimonial->client_company) }}">
                            @error('client_company')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Client Image -->
                        <div class="mb-3">
                            <label for="client_image" class="form-label">Client Photo</label>
                            <input type="file" class="form-control @error('client_image') is-invalid @enderror" 
                                   id="client_image" name="client_image" accept="image/*">
                            @error('client_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Upload a new photo to replace the current one. Recommended size: 200x200px</div>
                        </div>

                        <!-- Current Image Preview -->
                        <div id="currentImagePreview" class="mt-3">
                            @if($testimonial->client_image)
                                <div class="d-flex align-items-center">
                                    <img src="{{ asset('storage/' . $testimonial->client_image) }}" alt="{{ $testimonial->client_name }}" 
                                         class="img-fluid rounded-circle" style="max-height: 100px; max-width: 100px;">
                                    <div class="ms-3">
                                        <p class="mb-1">Current image</p>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image" value="1">
                                            <label class="form-check-label" for="remove_image">Remove current image</label>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <p class="text-muted">No image currently set</p>
                            @endif
                        </div>

                        <!-- New Image Preview -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <p class="mb-1">New image preview:</p>
                            <img id="previewImg" src="" alt="Preview" class="img-fluid rounded-circle" style="max-height: 100px; max-width: 100px;">
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-comment-dots me-2 text-primary"></i>Testimonial Content</h5>
                    </div>
                    <div class="card-body">
                        <!-- Testimonial Text -->
                        <div class="mb-3">
                            <label for="testimonial" class="form-label">Testimonial <span class="text-danger">*</span></label>
                            <x-rich-text-editor 
                                name="testimonial" 
                                label="" 
                                :required="true"
                                :value="old('testimonial', $testimonial->testimonial)"
                                toolbar="simple"
                                :height="200"
                                help="Enter the client's testimonial. Keep it concise and impactful."
                            />
                        </div>

                        <!-- Rating -->
                        <div class="mb-3">
                            <label for="rating" class="form-label">Rating <span class="text-danger">*</span></label>
                            <div class="rating-input">
                                @for ($i = 5; $i >= 1; $i--)
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="rating" id="rating{{ $i }}" 
                                               value="{{ $i }}" {{ (old('rating', $testimonial->rating) == $i) ? 'checked' : '' }} required>
                                        <label class="form-check-label" for="rating{{ $i }}">
                                            {{ $i }} <i class="fas fa-star text-warning"></i>
                                        </label>
                                    </div>
                                @endfor
                            </div>
                            @error('rating')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-cog me-2 text-primary"></i>Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Order -->
                                <div class="mb-3">
                                    <label for="order" class="form-label">Display Order <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                           id="order" name="order" value="{{ old('order', $testimonial->order) }}" min="0" required>
                                    @error('order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Lower numbers appear first</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Status -->
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">Status</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               value="1" {{ old('is_active', $testimonial->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active (testimonial will be visible on the website)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.content.testimonials.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Cancel
                    </a>
                    <div>
                        <button type="submit" name="action" value="save" class="btn btn-outline-primary me-2">
                            <i class="fas fa-save me-1"></i>Save Draft
                        </button>
                        <button type="submit" name="action" value="publish" class="btn btn-primary">
                            <i class="fas fa-check me-1"></i>Save & Publish
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Preview Panel -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm sticky-top" style="top: 2rem;">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-eye me-2 text-primary"></i>Live Preview</h5>
                </div>
                <div class="card-body">
                    <div id="testimonialPreview" class="p-4 border rounded">
                        <div class="d-flex mb-3">
                            <div id="previewImageContainer" class="me-3">
                                @if($testimonial->client_image)
                                    <img src="{{ asset('storage/' . $testimonial->client_image) }}" 
                                         class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;" id="currentPreviewImg">
                                @else
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 60px; height: 60px;">
                                        <i class="fas fa-user fa-lg text-muted"></i>
                                    </div>
                                @endif
                            </div>
                            <div>
                                <h5 id="previewName" class="mb-0">{{ $testimonial->client_name }}</h5>
                                <p id="previewPosition" class="mb-0 small text-muted">
                                    <span id="previewClientPosition">{{ $testimonial->client_position }}</span>
                                    <span id="previewPositionSeparator" style="{{ $testimonial->client_position && $testimonial->client_company ? 'display:inline;' : 'display:none;' }}">, </span>
                                    <span id="previewClientCompany">{{ $testimonial->client_company }}</span>
                                </p>
                                <div id="previewRating" class="mt-1">
                                    @for ($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $testimonial->rating ? 'text-warning' : 'text-muted' }}"></i>
                                    @endfor
                                </div>
                            </div>
                        </div>
                        <div id="previewTestimonial" class="fst-italic">
                            {!! $testimonial->testimonial !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image preview
        document.getElementById('client_image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                    updatePreview(true);
                };
                reader.readAsDataURL(file);
            }
        });

        // Remove image checkbox
        if (document.getElementById('remove_image')) {
            document.getElementById('remove_image').addEventListener('change', function(e) {
                if (this.checked) {
                    updatePreviewImageRemoved();
                } else {
                    // Restore original image if available
                    const currentImg = document.getElementById('currentPreviewImg');
                    if (currentImg) {
                        document.getElementById('previewImageContainer').innerHTML = 
                            `<img src="${currentImg.src}" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;" id="currentPreviewImg">`;
                    }
                }
            });
        }

        // Live preview update
        function updatePreview(newImage = false) {
            const clientName = document.getElementById('client_name').value || 'Client Name';
            const clientPosition = document.getElementById('client_position').value || '';
            const clientCompany = document.getElementById('client_company').value || '';
            const testimonial = window.getRichTextContent ? window.getRichTextContent('testimonial') : 
                               document.querySelector('[name="testimonial"]').value || 'Testimonial text will appear here';
            
            // Update client info
            document.getElementById('previewName').textContent = clientName;
            document.getElementById('previewClientPosition').textContent = clientPosition;
            document.getElementById('previewClientCompany').textContent = clientCompany;
            
            // Show/hide position separator
            document.getElementById('previewPositionSeparator').style.display = 
                (clientPosition && clientCompany) ? 'inline' : 'none';
            
            // Update testimonial text
            document.getElementById('previewTestimonial').innerHTML = testimonial;
            
            // Update rating
            updateRating();
            
            // Update image if a new one is selected
            if (newImage) {
                const previewImg = document.getElementById('previewImg');
                if (previewImg && previewImg.src && previewImg.src !== window.location.href) {
                    const imgContainer = document.getElementById('previewImageContainer');
                    imgContainer.innerHTML = `<img src="${previewImg.src}" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;">`;
                }
            }
        }
        
        function updatePreviewImageRemoved() {
            const imgContainer = document.getElementById('previewImageContainer');
            imgContainer.innerHTML = `
                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" 
                     style="width: 60px; height: 60px;">
                    <i class="fas fa-user fa-lg text-muted"></i>
                </div>
            `;
        }
        
        function updateRating() {
            const ratingValue = document.querySelector('input[name="rating"]:checked')?.value || 5;
            const starsContainer = document.getElementById('previewRating');
            starsContainer.innerHTML = '';
            
            for (let i = 1; i <= 5; i++) {
                const starClass = i <= ratingValue ? 'text-warning' : 'text-muted';
                starsContainer.innerHTML += `<i class="fas fa-star ${starClass}"></i>`;
            }
        }

        // Form field listeners for live preview
        ['client_name', 'client_position', 'client_company'].forEach(id => {
            document.getElementById(id).addEventListener('input', function() {
                updatePreview(false);
            });
        });

        // Rating change listener
        document.querySelectorAll('input[name="rating"]').forEach(radio => {
            radio.addEventListener('change', function() {
                updateRating();
            });
        });

        // Listen for rich text editor changes
        document.addEventListener('richTextAutoSave', function(e) {
            if (e.detail.editorId.includes('testimonial')) {
                updatePreview(false);
            }
        });

        // Form submission handling
        document.querySelector('form').addEventListener('submit', function(e) {
            const action = e.submitter.value;
            if (action === 'save') {
                document.getElementById('is_active').checked = false;
            } else if (action === 'publish') {
                document.getElementById('is_active').checked = true;
            }
        });
    });
</script>
@endpush
@endsection