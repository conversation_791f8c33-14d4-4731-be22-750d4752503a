<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('live_sessions')) {
            Schema::create('live_sessions', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('slug')->unique();
                $table->text('description');
                $table->string('instructor_name');
                $table->text('instructor_bio')->nullable();
                $table->timestamp('session_date');
                $table->integer('duration_minutes');
                $table->string('timezone')->default('UTC');
                $table->integer('max_participants')->nullable();
                $table->integer('current_participants')->default(0);
                $table->decimal('price', 8, 2)->default(0);
                $table->boolean('is_free')->default(true);
                $table->string('category')->nullable();
                $table->string('level')->nullable();
                $table->text('prerequisites')->nullable();
                $table->json('learning_objectives')->nullable();
                $table->json('session_agenda')->nullable();
                $table->string('meeting_url')->nullable();
                $table->string('meeting_id')->nullable();
                $table->string('meeting_password')->nullable();
                $table->string('recording_url')->nullable();
                $table->string('materials_url')->nullable();
                $table->boolean('certificate_eligible')->default(false);
                $table->decimal('cpd_credits', 3, 1)->nullable();
                $table->string('status')->default('scheduled');
                $table->boolean('is_featured')->default(false);
                $table->timestamp('registration_deadline')->nullable();
                $table->timestamps();
                
                // Indexes
                $table->index('slug');
                $table->index('session_date');
                $table->index('status');
                $table->index('is_featured');
                $table->index('category');
                $table->index('level');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('live_sessions');
    }
};
