<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class HandleErrors
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $response = $next($request);
            
            // Log successful requests for monitoring
            if ($request->isMethod('POST') || $request->isMethod('PUT') || $request->isMethod('DELETE')) {
                Log::info('Request processed successfully', [
                    'method' => $request->method(),
                    'url' => $request->url(),
                    'user_id' => auth()->id(),
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
            }
            
            return $response;
        } catch (\Throwable $e) {
            // Log the error
            Log::error('Request failed', [
                'method' => $request->method(),
                'url' => $request->url(),
                'user_id' => auth()->id(),
                'ip' => $request->ip(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            // Handle different types of requests
            if ($request->expectsJson()) {
                return $this->handleJsonError($e, $request);
            }
            
            return $this->handleWebError($e, $request);
        }
    }
    
    /**
     * Handle JSON/AJAX errors
     */
    private function handleJsonError(\Throwable $e, Request $request)
    {
        $status = method_exists($e, 'getStatusCode') ? $e->getStatusCode() : 500;
        
        $response = [
            'success' => false,
            'message' => $this->getErrorMessage($e),
        ];
        
        // Add debug information in development
        if (config('app.debug')) {
            $response['debug'] = [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTrace(),
            ];
        }
        
        return response()->json($response, $status);
    }
    
    /**
     * Handle web request errors
     */
    private function handleWebError(\Throwable $e, Request $request)
    {
        $status = method_exists($e, 'getStatusCode') ? $e->getStatusCode() : 500;
        
        // Redirect back with error message for form submissions
        if ($request->isMethod('POST') || $request->isMethod('PUT') || $request->isMethod('DELETE')) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => $this->getErrorMessage($e)])
                ->with('error', $this->getErrorMessage($e));
        }
        
        // Show error page for GET requests
        return response()->view('errors.custom', [
            'exception' => $e,
            'status' => $status,
            'message' => $this->getErrorMessage($e),
        ], $status);
    }
    
    /**
     * Get user-friendly error message
     */
    private function getErrorMessage(\Throwable $e): string
    {
        // Return specific messages for known exceptions
        if ($e instanceof \Illuminate\Validation\ValidationException) {
            return 'Please check your input and try again.';
        }
        
        if ($e instanceof \Illuminate\Auth\AuthenticationException) {
            return 'You need to be logged in to access this resource.';
        }
        
        if ($e instanceof \Illuminate\Auth\Access\AuthorizationException) {
            return 'You do not have permission to perform this action.';
        }
        
        if ($e instanceof \Illuminate\Database\QueryException) {
            return 'A database error occurred. Please try again later.';
        }
        
        if ($e instanceof \Illuminate\Http\Exceptions\ThrottleRequestsException) {
            return 'Too many requests. Please slow down and try again later.';
        }
        
        // Return generic message for production, specific for development
        if (config('app.debug')) {
            return $e->getMessage();
        }
        
        return 'An unexpected error occurred. Please try again later.';
    }
}
