@extends('layouts.dashboard')

@section('page-title', 'Contact Support')

@section('content')
<div class="dashboard-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Contact Support</h2>
                    <p class="text-muted mb-0">Get help from our support team</p>
                </div>
                <div>
                    <a href="{{ route('dashboard.support.help') }}" class="btn btn-outline-primary">
                        <i class="fas fa-question-circle me-2"></i>Help Center
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Contact Form -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-ticket-alt me-2 text-primary"></i>Submit a Support Ticket</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('dashboard.support.submit-ticket') }}" method="POST">
                        @csrf
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('subject') is-invalid @enderror" 
                                       id="subject" name="subject" value="{{ old('subject') }}" required>
                                @error('subject')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                                <select class="form-select @error('category') is-invalid @enderror" 
                                        id="category" name="category" required>
                                    <option value="">Select a category</option>
                                    <option value="general" {{ old('category') == 'general' ? 'selected' : '' }}>
                                        General Question
                                    </option>
                                    <option value="technical" {{ old('category') == 'technical' ? 'selected' : '' }}>
                                        Technical Issue
                                    </option>
                                    <option value="billing" {{ old('category') == 'billing' ? 'selected' : '' }}>
                                        Billing & Payment
                                    </option>
                                    <option value="course" {{ old('category') == 'course' ? 'selected' : '' }}>
                                        Course Related
                                    </option>
                                    <option value="session" {{ old('category') == 'session' ? 'selected' : '' }}>
                                        Live Session
                                    </option>
                                </select>
                                @error('category')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="priority" class="form-label">Priority <span class="text-danger">*</span></label>
                            <select class="form-select @error('priority') is-invalid @enderror" 
                                    id="priority" name="priority" required>
                                <option value="">Select priority level</option>
                                <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>
                                    Low - General inquiry
                                </option>
                                <option value="normal" {{ old('priority') == 'normal' ? 'selected' : '' }}>
                                    Normal - Standard support
                                </option>
                                <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>
                                    High - Important issue
                                </option>
                                <option value="urgent" {{ old('priority') == 'urgent' ? 'selected' : '' }}>
                                    Urgent - Critical problem
                                </option>
                            </select>
                            @error('priority')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('message') is-invalid @enderror" 
                                      id="message" name="message" rows="6" required 
                                      placeholder="Please describe your issue or question in detail...">{{ old('message') }}</textarea>
                            @error('message')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                Please provide as much detail as possible to help us assist you better.
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                We typically respond within 24 hours during business days.
                            </small>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Submit Ticket
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Support Information -->
        <div class="col-lg-4">
            <!-- Contact Information -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2 text-primary"></i>Support Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-clock text-primary me-2"></i>
                            <strong>Support Hours</strong>
                        </div>
                        <small class="text-muted">
                            Monday - Friday: 9:00 AM - 6:00 PM EST<br>
                            Saturday: 10:00 AM - 4:00 PM EST<br>
                            Sunday: Closed
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-reply text-primary me-2"></i>
                            <strong>Response Time</strong>
                        </div>
                        <small class="text-muted">
                            • Low Priority: 48-72 hours<br>
                            • Normal Priority: 24-48 hours<br>
                            • High Priority: 12-24 hours<br>
                            • Urgent Priority: 2-4 hours
                        </small>
                    </div>

                    <div class="mb-0">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-envelope text-primary me-2"></i>
                            <strong>Direct Email</strong>
                        </div>
                        <small class="text-muted">
                            <a href="mailto:<EMAIL>" class="text-decoration-none">
                                <EMAIL>
                            </a>
                        </small>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h6 class="mb-0"><i class="fas fa-bolt me-2 text-primary"></i>Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('dashboard.support.tickets') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-ticket-alt me-2"></i>View My Tickets
                        </a>
                        <a href="{{ route('dashboard.support.help') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-question-circle me-2"></i>Browse FAQs
                        </a>
                        <a href="{{ route('dashboard.notifications.index') }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-bell me-2"></i>Check Notifications
                        </a>
                    </div>
                </div>
            </div>

            <!-- Common Issues -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h6 class="mb-0"><i class="fas fa-lightbulb me-2 text-primary"></i>Common Issues</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="{{ route('dashboard.support.help', ['search' => 'login']) }}" 
                           class="list-group-item list-group-item-action border-0 px-0">
                            <i class="fas fa-sign-in-alt text-muted me-2"></i>
                            <small>Login Problems</small>
                        </a>
                        <a href="{{ route('dashboard.support.help', ['search' => 'certificate']) }}" 
                           class="list-group-item list-group-item-action border-0 px-0">
                            <i class="fas fa-certificate text-muted me-2"></i>
                            <small>Certificate Download</small>
                        </a>
                        <a href="{{ route('dashboard.support.help', ['search' => 'session']) }}" 
                           class="list-group-item list-group-item-action border-0 px-0">
                            <i class="fas fa-video text-muted me-2"></i>
                            <small>Joining Live Sessions</small>
                        </a>
                        <a href="{{ route('dashboard.support.help', ['search' => 'progress']) }}" 
                           class="list-group-item list-group-item-action border-0 px-0">
                            <i class="fas fa-chart-line text-muted me-2"></i>
                            <small>Course Progress</small>
                        </a>
                        <a href="{{ route('dashboard.support.help', ['search' => 'payment']) }}" 
                           class="list-group-item list-group-item-action border-0 px-0">
                            <i class="fas fa-credit-card text-muted me-2"></i>
                            <small>Payment Issues</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textarea
    const textarea = document.getElementById('message');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }

    // Category-based priority suggestions
    const categorySelect = document.getElementById('category');
    const prioritySelect = document.getElementById('priority');
    
    if (categorySelect && prioritySelect) {
        categorySelect.addEventListener('change', function() {
            const category = this.value;
            const priorityOptions = prioritySelect.querySelectorAll('option');
            
            // Reset all options
            priorityOptions.forEach(option => {
                option.style.display = 'block';
            });
            
            // Suggest priority based on category
            if (category === 'technical' || category === 'billing') {
                prioritySelect.value = 'high';
            } else if (category === 'general') {
                prioritySelect.value = 'normal';
            }
        });
    }

    // Form validation feedback
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }
            }
        });
    }
});
</script>
@endpush
