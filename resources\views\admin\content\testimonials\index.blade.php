@extends('layouts.dashboard')

@section('page-title', 'Testimonials Management')

@section('content')
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Testimonials Management</h2>
                    <p class="text-muted mb-0">Manage client testimonials and reviews</p>
                </div>
                <div>
                    <a href="{{ route('admin.content.testimonials.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Testimonial
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters & Search -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ route('admin.content.testimonials.index') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0"><i class="fas fa-search text-muted"></i></span>
                        <input type="text" class="form-control bg-light border-0" name="search" 
                               placeholder="Search testimonials..." value="{{ request('search') }}">
                    </div>
                </div>
                <div class="col-md-2">
                    <select name="status" class="form-select bg-light border-0">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="rating" class="form-select bg-light border-0">
                        <option value="">All Ratings</option>
                        <option value="5" {{ request('rating') == '5' ? 'selected' : '' }}>5 Stars</option>
                        <option value="4" {{ request('rating') == '4' ? 'selected' : '' }}>4 Stars</option>
                        <option value="3" {{ request('rating') == '3' ? 'selected' : '' }}>3 Stars</option>
                        <option value="2" {{ request('rating') == '2' ? 'selected' : '' }}>2 Stars</option>
                        <option value="1" {{ request('rating') == '1' ? 'selected' : '' }}>1 Star</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="sort" class="form-select bg-light border-0">
                        <option value="order_asc" {{ request('sort') == 'order_asc' ? 'selected' : '' }}>Order (Low to High)</option>
                        <option value="order_desc" {{ request('sort') == 'order_desc' ? 'selected' : '' }}>Order (High to Low)</option>
                        <option value="rating_desc" {{ request('sort') == 'rating_desc' ? 'selected' : '' }}>Rating (High to Low)</option>
                        <option value="rating_asc" {{ request('sort') == 'rating_asc' ? 'selected' : '' }}>Rating (Low to High)</option>
                        <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest First</option>
                        <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>Oldest First</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Testimonials List -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input select-all" type="checkbox" id="selectAll">
                                    <label class="form-check-label" for="selectAll"></label>
                                </div>
                            </th>
                            <th width="60">Order</th>
                            <th width="80">Photo</th>
                            <th>Client</th>
                            <th>Testimonial</th>
                            <th width="100">Rating</th>
                            <th width="100">Status</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="sortable" data-url="{{ route('admin.content.reorder', ['type' => 'testimonials']) }}">
                        @forelse($testimonials ?? [] as $testimonial)
                        <tr data-id="{{ $testimonial->id }}">
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input select-item" type="checkbox" 
                                           id="testimonial{{ $testimonial->id }}" name="selected_items[]" value="{{ $testimonial->id }}">
                                    <label class="form-check-label" for="testimonial{{ $testimonial->id }}"></label>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">{{ $testimonial->order }}</span>
                                <i class="fas fa-grip-vertical text-muted ms-2 drag-handle"></i>
                            </td>
                            <td>
                                @if($testimonial->client_image)
                                    <img src="{{ asset($testimonial->client_image) }}" alt="{{ $testimonial->client_name }}" 
                                         class="rounded-circle" width="50" height="50" style="object-fit: cover;">
                                @else
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 50px; height: 50px;">
                                        <i class="fas fa-user text-muted"></i>
                                    </div>
                                @endif
                            </td>
                            <td>
                                <h6 class="mb-0">{{ $testimonial->client_name }}</h6>
                                <small class="text-muted">
                                    @if($testimonial->client_position)
                                        {{ $testimonial->client_position }}
                                        @if($testimonial->client_company), @endif
                                    @endif
                                    @if($testimonial->client_company)
                                        {{ $testimonial->client_company }}
                                    @endif
                                </small>
                            </td>
                            <td>
                                <p class="text-muted mb-0 small">{{ Str::limit(strip_tags($testimonial->testimonial), 100) }}</p>
                            </td>
                            <td>
                                <div class="text-warning">
                                    @for($i = 1; $i <= 5; $i++)
                                        @if($i <= $testimonial->rating)
                                            <i class="fas fa-star"></i>
                                        @else
                                            <i class="far fa-star"></i>
                                        @endif
                                    @endfor
                                </div>
                            </td>
                            <td>
                                @if($testimonial->is_active)
                                    <span class="badge bg-success-soft">Active</span>
                                @else
                                    <span class="badge bg-danger-soft">Inactive</span>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex">
                                    <a href="{{ route('admin.content.testimonials.edit', $testimonial->id) }}" class="btn btn-sm btn-outline-primary me-2">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.content.testimonials.destroy', $testimonial->id) }}" method="POST" class="delete-form">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-quote-right fa-3x text-muted mb-3"></i>
                                    <h5>No Testimonials Found</h5>
                                    <p class="text-muted">Get started by adding your first testimonial</p>
                                    <a href="{{ route('admin.content.testimonials.create') }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus me-2"></i>Add New Testimonial
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    @if(isset($testimonials) && $testimonials->count() > 0)
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ route('admin.content.bulk-action', ['type' => 'testimonials']) }}" method="POST" id="bulkActionForm">
                @csrf
                <div class="row g-3 align-items-center">
                    <div class="col-auto">
                        <h5 class="mb-0">Bulk Actions</h5>
                    </div>
                    <div class="col-auto">
                        <select name="action" class="form-select" id="bulkAction">
                            <option value="">Select Action</option>
                            <option value="activate">Activate</option>
                            <option value="deactivate">Deactivate</option>
                            <option value="delete">Delete</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-primary" id="applyBulkAction" disabled>
                            Apply
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-center">
        {{ $testimonials->links() }}
    </div>
    @endif
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Select all functionality
        const selectAll = document.getElementById('selectAll');
        const selectItems = document.querySelectorAll('.select-item');
        const applyBulkAction = document.getElementById('applyBulkAction');
        const bulkAction = document.getElementById('bulkAction');
        
        if (selectAll) {
            selectAll.addEventListener('change', function() {
                selectItems.forEach(item => {
                    item.checked = this.checked;
                });
                updateBulkActionButton();
            });
        }
        
        selectItems.forEach(item => {
            item.addEventListener('change', function() {
                updateBulkActionButton();
                updateSelectAllCheckbox();
            });
        });
        
        bulkAction.addEventListener('change', updateBulkActionButton);
        
        function updateBulkActionButton() {
            const hasCheckedItems = Array.from(selectItems).some(item => item.checked);
            const hasSelectedAction = bulkAction.value !== '';
            applyBulkAction.disabled = !(hasCheckedItems && hasSelectedAction);
        }
        
        function updateSelectAllCheckbox() {
            const allChecked = Array.from(selectItems).every(item => item.checked);
            const someChecked = Array.from(selectItems).some(item => item.checked);
            
            selectAll.checked = allChecked;
            selectAll.indeterminate = someChecked && !allChecked;
        }
        
        // Confirm delete
        document.querySelectorAll('.delete-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                if (confirm('Are you sure you want to delete this testimonial? This action cannot be undone.')) {
                    this.submit();
                }
            });
        });
        
        // Confirm bulk delete
        document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
            const action = document.getElementById('bulkAction').value;
            if (action === 'delete') {
                e.preventDefault();
                if (confirm('Are you sure you want to delete the selected testimonials? This action cannot be undone.')) {
                    this.submit();
                }
            }
        });
        
        // Sortable functionality for drag and drop reordering
        if (typeof Sortable !== 'undefined') {
            const sortableList = document.querySelector('.sortable');
            if (sortableList) {
                new Sortable(sortableList, {
                    handle: '.drag-handle',
                    animation: 150,
                    onEnd: function(evt) {
                        const itemIds = Array.from(sortableList.querySelectorAll('tr[data-id]'))
                            .map((row, index) => {
                                return {
                                    id: row.dataset.id,
                                    order: index
                                };
                            });
                            
                        fetch(sortableList.dataset.url, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({ items: itemIds })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Update the order numbers displayed in the UI
                                itemIds.forEach(item => {
                                    const row = sortableList.querySelector(`tr[data-id="${item.id}"]`);
                                    const orderBadge = row.querySelector('.badge');
                                    if (orderBadge) {
                                        orderBadge.textContent = item.order;
                                    }
                                });
                            }
                        })
                        .catch(error => console.error('Error updating order:', error));
                    }
                });
            }
        }
    });
</script>
@endpush
@endsection