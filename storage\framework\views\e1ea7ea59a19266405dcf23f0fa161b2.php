<?php $__env->startSection('page-title', 'Edit Service'); ?>

<?php $__env->startSection('content'); ?>
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Edit Service</h2>
                    <p class="text-muted mb-0">Update service information</p>
                </div>
                <div>
                    <a href="<?php echo e(route('admin.content.services.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Services
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <form action="<?php echo e(route('admin.content.services.update', $service->id)); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2 text-primary"></i>Service Information</h5>
                    </div>
                    <div class="card-body">
                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Service Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="title" name="title" value="<?php echo e(old('title', $service->title)); ?>" required>
                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <?php if (isset($component)) { $__componentOriginal8910ad36d6e49197b6cc58bf0f8fe11b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8910ad36d6e49197b6cc58bf0f8fe11b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.rich-text-editor','data' => ['name' => 'description','label' => '','required' => true,'value' => old('description', $service->description),'toolbar' => 'full','height' => 300,'help' => 'Describe the service in detail. Include benefits, features, and any other relevant information.']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('rich-text-editor'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'description','label' => '','required' => true,'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('description', $service->description)),'toolbar' => 'full','height' => 300,'help' => 'Describe the service in detail. Include benefits, features, and any other relevant information.']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8910ad36d6e49197b6cc58bf0f8fe11b)): ?>
<?php $attributes = $__attributesOriginal8910ad36d6e49197b6cc58bf0f8fe11b; ?>
<?php unset($__attributesOriginal8910ad36d6e49197b6cc58bf0f8fe11b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8910ad36d6e49197b6cc58bf0f8fe11b)): ?>
<?php $component = $__componentOriginal8910ad36d6e49197b6cc58bf0f8fe11b; ?>
<?php unset($__componentOriginal8910ad36d6e49197b6cc58bf0f8fe11b); ?>
<?php endif; ?>
                        </div>

                        <!-- Icon -->
                        <div class="mb-3">
                            <label for="icon" class="form-label">Font Awesome Icon <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-icons"></i></span>
                                <input type="text" class="form-control <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="icon" name="icon" value="<?php echo e(old('icon', $service->icon)); ?>" 
                                       placeholder="fa-graduation-cap" required>
                            </div>
                            <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">Enter a Font Awesome icon name (e.g., fa-graduation-cap, fa-stethoscope). <a href="https://fontawesome.com/icons" target="_blank">Browse icons</a></div>
                        </div>

                        <!-- Image -->
                        <div class="mb-3">
                            <label for="image" class="form-label">Service Image</label>
                            <input type="file" class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="image" name="image" accept="image/*">
                            <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">Upload a new image to replace the current one. Recommended size: 800x600px</div>
                        </div>

                        <!-- Current Image Preview -->
                        <div id="currentImagePreview" class="mt-3">
                            <?php if($service->image): ?>
                                <div class="d-flex align-items-center">
                                    <img src="<?php echo e(asset('storage/' . $service->image)); ?>" alt="<?php echo e($service->title); ?>" 
                                         class="img-thumbnail" style="max-height: 150px; max-width: 200px;">
                                    <div class="ms-3">
                                        <p class="mb-1">Current image</p>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image" value="1">
                                            <label class="form-check-label" for="remove_image">Remove current image</label>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">No image currently set</p>
                            <?php endif; ?>
                        </div>

                        <!-- New Image Preview -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <p class="mb-1">New image preview:</p>
                            <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-height: 150px; max-width: 200px;">
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-cog me-2 text-primary"></i>Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Order -->
                                <div class="mb-3">
                                    <label for="order" class="form-label">Display Order <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="order" name="order" value="<?php echo e(old('order', $service->order)); ?>" min="0" required>
                                    <?php $__errorArgs = ['order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <div class="form-text">Lower numbers appear first</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Status -->
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">Status</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               value="1" <?php echo e(old('is_active', $service->is_active) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_active">
                                            Active (service will be visible on the website)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <a href="<?php echo e(route('admin.content.services.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Cancel
                    </a>
                    <div>
                        <button type="submit" name="action" value="save" class="btn btn-outline-primary me-2">
                            <i class="fas fa-save me-1"></i>Save Draft
                        </button>
                        <button type="submit" name="action" value="publish" class="btn btn-primary">
                            <i class="fas fa-check me-1"></i>Save & Publish
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Preview Panel -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm sticky-top" style="top: 2rem;">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-eye me-2 text-primary"></i>Live Preview</h5>
                </div>
                <div class="card-body">
                    <div id="servicePreview" class="text-center">
                        <div class="mb-4">
                            <div id="previewIconContainer" class="mb-3">
                                <div class="bg-primary rounded-circle mx-auto d-flex align-items-center justify-content-center" 
                                     style="width: 80px; height: 80px;">
                                    <i id="previewIcon" class="<?php echo e($service->icon); ?> fa-2x text-white"></i>
                                </div>
                            </div>
                            <h5 id="previewTitle" class="mb-3"><?php echo e($service->title); ?></h5>
                            <div id="previewImageContainer" class="mb-3">
                                <?php if($service->image): ?>
                                    <img src="<?php echo e(asset('storage/' . $service->image)); ?>" class="img-fluid rounded" 
                                         style="max-height: 150px;" id="currentPreviewImg">
                                <?php endif; ?>
                            </div>
                            <div id="previewDescription" class="text-muted small text-start">
                                <?php echo $service->description; ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image preview
        document.getElementById('image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                    updatePreview(true);
                };
                reader.readAsDataURL(file);
            }
        });

        // Remove image checkbox
        if (document.getElementById('remove_image')) {
            document.getElementById('remove_image').addEventListener('change', function(e) {
                if (this.checked) {
                    document.getElementById('previewImageContainer').innerHTML = '';
                } else {
                    // Restore original image if available
                    const currentImg = document.getElementById('currentPreviewImg');
                    if (currentImg) {
                        document.getElementById('previewImageContainer').innerHTML = 
                            `<img src="${currentImg.src}" class="img-fluid rounded" style="max-height: 150px;" id="currentPreviewImg">`;
                    }
                }
            });
        }

        // Live preview update
        function updatePreview(newImage = false) {
            const title = document.getElementById('title').value || 'Service Title';
            const description = window.getRichTextContent ? window.getRichTextContent('description') : 
                               document.querySelector('[name="description"]').value || 'Service description will appear here';
            const icon = document.getElementById('icon').value || 'fa-graduation-cap';
            
            // Update title
            document.getElementById('previewTitle').textContent = title;
            
            // Update description
            document.getElementById('previewDescription').innerHTML = description;
            
            // Update icon
            const previewIcon = document.getElementById('previewIcon');
            previewIcon.className = ''; // Clear existing classes
            previewIcon.classList.add(icon, 'fa-2x', 'text-white');
            
            // Update image if a new one is selected
            if (newImage) {
                const previewImg = document.getElementById('previewImg');
                if (previewImg && previewImg.src && previewImg.src !== window.location.href) {
                    const imgContainer = document.getElementById('previewImageContainer');
                    imgContainer.innerHTML = `<img src="${previewImg.src}" class="img-fluid rounded" style="max-height: 150px;">`;
                }
            }
        }

        // Form field listeners for live preview
        ['title', 'icon'].forEach(id => {
            document.getElementById(id).addEventListener('input', function() {
                updatePreview(false);
            });
        });

        // Listen for rich text editor changes
        document.addEventListener('richTextAutoSave', function(e) {
            if (e.detail.editorId.includes('description')) {
                updatePreview(false);
            }
        });

        // Form submission handling
        document.querySelector('form').addEventListener('submit', function(e) {
            const action = e.submitter.value;
            if (action === 'save') {
                document.getElementById('is_active').checked = false;
            } else if (action === 'publish') {
                document.getElementById('is_active').checked = true;
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\VCH\resources\views/admin/content/services/edit.blade.php ENDPATH**/ ?>