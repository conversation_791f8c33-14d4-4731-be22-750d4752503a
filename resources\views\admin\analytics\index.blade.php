@extends('layouts.dashboard')

@section('title', 'Analytics & Reports')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Analytics & Reports</h1>
            <p class="mb-0 text-muted">Comprehensive insights into platform performance and usage</p>
        </div>
        <div class="dropdown">
            <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-download me-2"></i>Export Data
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{{ route('admin.analytics.export', ['type' => 'users']) }}">
                    <i class="fas fa-users me-2"></i>Export Users
                </a></li>
                <li><a class="dropdown-item" href="{{ route('admin.analytics.export', ['type' => 'courses']) }}">
                    <i class="fas fa-graduation-cap me-2"></i>Export Courses
                </a></li>
                <li><a class="dropdown-item" href="{{ route('admin.analytics.export', ['type' => 'sessions']) }}">
                    <i class="fas fa-video me-2"></i>Export Sessions
                </a></li>
                <li><a class="dropdown-item" href="{{ route('admin.analytics.export', ['type' => 'tickets']) }}">
                    <i class="fas fa-ticket-alt me-2"></i>Export Tickets
                </a></li>
            </ul>
        </div>
    </div>

    <!-- Overview Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Users</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_users']) }}</div>
                            <div class="text-xs text-success">{{ number_format($stats['active_users']) }} active</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Courses</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_courses']) }}</div>
                            <div class="text-xs text-success">{{ number_format($stats['published_courses']) }} published</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-graduation-cap fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Live Sessions</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_sessions']) }}</div>
                            <div class="text-xs text-info">{{ number_format($stats['upcoming_sessions']) }} upcoming</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-video fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Support Tickets</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['total_tickets']) }}</div>
                            <div class="text-xs text-warning">{{ number_format($stats['open_tickets']) }} open</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-ticket-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- User Registrations Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">User Registrations (Last 12 Months)</h6>
                </div>
                <div class="card-body">
                    <canvas id="userRegistrationsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Users by Role -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Users by Role</h6>
                </div>
                <div class="card-body">
                    <canvas id="usersByRoleChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Charts Row -->
    <div class="row mb-4">
        <!-- Course Statistics -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Courses by Category</h6>
                </div>
                <div class="card-body">
                    <canvas id="coursesByCategoryChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Support Tickets -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Support Tickets by Status</h6>
                </div>
                <div class="card-body">
                    <canvas id="ticketsByStatusChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
                </div>
                <div class="card-body">
                    @if($recentActivity->count() > 0)
                        <div class="timeline">
                            @foreach($recentActivity as $activity)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-{{ $activity['color'] }}">
                                    <i class="{{ $activity['icon'] }}"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <span class="timeline-title">{{ $activity['description'] }}</span>
                                        <span class="timeline-date">{{ $activity['timestamp']->diffForHumans() }}</span>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-chart-line fa-3x mb-3"></i>
                                <p>No recent activity found.</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// User Registrations Chart
const userRegistrationsCtx = document.getElementById('userRegistrationsChart').getContext('2d');
new Chart(userRegistrationsCtx, {
    type: 'line',
    data: {
        labels: @json($monthlyUsers['months']),
        datasets: [{
            label: 'New Users',
            data: @json($monthlyUsers['counts']),
            borderColor: 'rgb(78, 115, 223)',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            tension: 0.3,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Users by Role Chart
const usersByRoleCtx = document.getElementById('usersByRoleChart').getContext('2d');
new Chart(usersByRoleCtx, {
    type: 'doughnut',
    data: {
        labels: @json(array_keys($usersByRole)),
        datasets: [{
            data: @json(array_values($usersByRole)),
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e',
                '#e74a3b'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Courses by Category Chart
const coursesByCategoryCtx = document.getElementById('coursesByCategoryChart').getContext('2d');
new Chart(coursesByCategoryCtx, {
    type: 'bar',
    data: {
        labels: @json(array_keys($courseStats['by_category'])),
        datasets: [{
            label: 'Courses',
            data: @json(array_values($courseStats['by_category'])),
            backgroundColor: 'rgba(28, 200, 138, 0.8)',
            borderColor: 'rgba(28, 200, 138, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Tickets by Status Chart
const ticketsByStatusCtx = document.getElementById('ticketsByStatusChart').getContext('2d');
new Chart(ticketsByStatusCtx, {
    type: 'pie',
    data: {
        labels: @json(array_keys($ticketStats['by_status'])),
        datasets: [{
            data: @json(array_values($ticketStats['by_status'])),
            backgroundColor: [
                '#f6c23e',
                '#36b9cc',
                '#1cc88a',
                '#e74a3b'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
</script>
@endpush

@push('styles')
<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.text-gray-800 { color: #5a5c69 !important; }
.text-gray-300 { color: #dddfeb !important; }

.timeline {
    position: relative;
    padding-left: 3rem;
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-marker {
    position: absolute;
    left: -3rem;
    top: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.timeline-content {
    background: #f8f9fc;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 3px solid #e3e6f0;
}

.timeline-header {
    display: flex;
    justify-content: between;
    align-items: center;
}

.timeline-title {
    font-weight: 600;
    color: #5a5c69;
}

.timeline-date {
    font-size: 0.8rem;
    color: #858796;
    margin-left: auto;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -2rem;
    top: 1rem;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}
</style>
@endpush
