<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="utf-8">
    <title><?php echo e(config('app.name', 'Laravel')); ?> - <?php echo $__env->yieldContent('title', 'Authentication'); ?></title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="<?php echo e(asset('img/favicon.ico')); ?>" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&family=Roboto:wght@500;700&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="<?php echo e(asset('lib/animate/animate.min.css')); ?>" rel="stylesheet">

    <!-- Customized Bootstrap Stylesheet -->
    <link href="<?php echo e(asset('css/bootstrap.min.css')); ?>" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="<?php echo e(asset('css/style.css')); ?>" rel="stylesheet">

    <style>
        :root {
            --primary: #2E8B57;
            --secondary: #FF6B35;
            --light: #F8F9FA;
            --dark: #2C3E50;
            --medical-blue: #4A90E2;
            --medical-green: #2E8B57;
        }

        .auth-container {
            background: linear-gradient(135deg, rgba(46, 139, 87, 0.9), rgba(74, 144, 226, 0.8)), url('<?php echo e(asset('img/bg.jpg')); ?>') center center no-repeat;
            background-size: cover;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            padding: 2.5rem;
            width: 100%;
            max-width: 450px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .auth-logo {
            text-align: center;
            margin-bottom: 2rem;
        }

        .auth-logo .vch-logo {
            display: inline-block;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--medical-green), var(--medical-blue));
            border-radius: 12px;
            color: white;
            font-size: 24px;
            font-weight: 700;
            line-height: 60px;
            margin-bottom: 1rem;
            box-shadow: 0 8px 20px rgba(46, 139, 87, 0.3);
        }

        .auth-logo h2 {
            color: var(--medical-green);
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-size: 1.8rem;
        }

        .auth-logo p {
            color: var(--dark);
            margin: 0;
            font-size: 0.9rem;
        }

        .form-control {
            height: 55px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 0 20px;
            font-size: 16px;
        }

        .form-control:focus {
            border-color: var(--medical-green);
            box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
        }

        .btn-auth {
            height: 55px;
            background: linear-gradient(135deg, var(--medical-green), var(--medical-blue));
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(46, 139, 87, 0.3);
        }

        .btn-auth:hover {
            background: linear-gradient(135deg, var(--medical-blue), var(--medical-green));
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(46, 139, 87, 0.4);
        }

        .auth-link {
            color: var(--medical-green);
            text-decoration: none;
            font-weight: 500;
        }

        .auth-link:hover {
            color: var(--medical-blue);
            text-decoration: underline;
        }

        .form-check-input:checked {
            background-color: var(--medical-green);
            border-color: var(--medical-green);
        }

        .alert {
            border-radius: 5px;
            border: none;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .invalid-feedback {
            display: block;
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }

        .form-control.is-invalid {
            border-color: #dc3545;
        }
    </style>
</head>

<body>
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-7 col-sm-9">
                    <div class="auth-card wow fadeInUp" data-wow-delay="0.1s">
                        <div class="auth-logo">
                            <div class="vch-logo">VCH</div>
                            <h2>Virtual CME Hub</h2>
                            <p class="text-muted">Continuing Professional Development</p>
                        </div>

                        <?php echo $__env->yieldContent('content'); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo e(asset('lib/wow/wow.min.js')); ?>"></script>
    <script src="<?php echo e(asset('lib/easing/easing.min.js')); ?>"></script>

    <!-- Initialize WOW.js -->
    <script>
        new WOW().init();
    </script>
</body>

</html>
<?php /**PATH C:\Users\<USER>\Desktop\VCH\resources\views/layouts/auth.blade.php ENDPATH**/ ?>