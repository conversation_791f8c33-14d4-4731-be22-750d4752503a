<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LiveSession;
use App\Services\ZoomService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Carbon\Carbon;

class LiveSessionManagementController extends Controller
{
    protected $zoomService;

    public function __construct(ZoomService $zoomService)
    {
        $this->zoomService = $zoomService;
    }

    /**
     * Display a listing of live sessions.
     */
    public function index(Request $request)
    {
        $query = LiveSession::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('instructor', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('session_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('session_date', '<=', $request->date_to);
        }

        $sessions = $query->orderBy('session_date', 'desc')->paginate(20);

        $stats = [
            'total' => LiveSession::count(),
            'scheduled' => LiveSession::where('status', 'scheduled')->count(),
            'live' => LiveSession::where('status', 'live')->count(),
            'completed' => LiveSession::where('status', 'completed')->count(),
            'upcoming' => LiveSession::where('session_date', '>', now())->where('status', 'scheduled')->count(),
        ];

        return view('admin.sessions.index', compact('sessions', 'stats'));
    }

    /**
     * Show the form for creating a new live session.
     */
    public function create()
    {
        return view('admin.sessions.create');
    }

    /**
     * Store a newly created live session in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'instructor' => 'required|string|max:255',
            'instructor_title' => 'nullable|string|max:255',
            'session_date' => 'required|date|after:now',
            'duration_minutes' => 'required|integer|min:15|max:480',
            'max_participants' => 'nullable|integer|min:1|max:1000',
            'category' => 'nullable|string|max:100',
            'level' => 'nullable|in:Beginner,Intermediate,Advanced',
            'cpd_credits' => 'nullable|numeric|min:0|max:10',
            'is_free' => 'boolean',
            'price' => 'nullable|numeric|min:0|required_if:is_free,false',
            'registration_required' => 'boolean',
            'auto_record' => 'boolean',
            'waiting_room' => 'boolean',
            'join_before_host' => 'boolean',
            'mute_participants' => 'boolean',
        ]);

        try {
            // Create Zoom webinar with East Africa Time
            $sessionDateTime = Carbon::parse($validated['session_date'], 'Africa/Nairobi');

            $zoomWebinar = $this->zoomService->createWebinar([
                'topic' => $validated['title'],
                'type' => 5, // Scheduled webinar
                'start_time' => $sessionDateTime->toISOString(),
                'duration' => $validated['duration_minutes'],
                'timezone' => 'Africa/Nairobi',
                'agenda' => $validated['description'],
                'settings' => [
                    'host_video' => true,
                    'panelists_video' => false,
                    'practice_session' => false,
                    'hd_video' => true,
                    'approval_type' => 2, // 2=no registration required (attendees can join directly)
                    'registration_type' => 1,
                    'audio' => 'both',
                    'auto_recording' => $validated['auto_record'] ? 'cloud' : 'none',
                    'enforce_login' => false,
                    'registrants_email_notification' => false,
                    'close_registration' => false,
                    'show_share_button' => true,
                    'allow_multiple_devices' => true,
                    'on_demand' => false,
                    'global_dial_in_countries' => ['US', 'KE'], // US and Kenya
                    'contact_name' => $validated['instructor'],
                    'contact_email' => auth()->user()->email,
                ]
            ]);

            if (!$zoomWebinar) {
                throw new \Exception('Failed to create Zoom webinar');
            }

            // Create live session record
            $validated['slug'] = Str::slug($validated['title'] . '-' . now()->format('Y-m-d'));
            $validated['zoom_webinar_id'] = $zoomWebinar['id'];
            $validated['zoom_join_url'] = $zoomWebinar['join_url'];
            $validated['zoom_start_url'] = $zoomWebinar['start_url'];
            $validated['zoom_password'] = $zoomWebinar['password'] ?? null;
            $validated['status'] = 'scheduled';

            LiveSession::create($validated);

            return redirect()->route('admin.sessions.index')
                ->with('success', 'Live webinar created successfully! Zoom webinar has been scheduled.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to create live session: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified live session.
     */
    public function show(LiveSession $session)
    {
        return view('admin.sessions.show', compact('session'));
    }

    /**
     * Show the form for editing the specified live session.
     */
    public function edit(LiveSession $session)
    {
        return view('admin.sessions.edit', compact('session'));
    }

    /**
     * Update the specified live session in storage.
     */
    public function update(Request $request, LiveSession $session)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'instructor' => 'required|string|max:255',
            'instructor_title' => 'nullable|string|max:255',
            'session_date' => 'required|date',
            'duration_minutes' => 'required|integer|min:15|max:480',
            'max_participants' => 'nullable|integer|min:1|max:1000',
            'category' => 'nullable|string|max:100',
            'level' => 'nullable|in:Beginner,Intermediate,Advanced',
            'cpd_credits' => 'nullable|numeric|min:0|max:10',
            'is_free' => 'boolean',
            'price' => 'nullable|numeric|min:0|required_if:is_free,false',
            'status' => 'required|in:scheduled,live,completed,cancelled',
        ]);

        try {
            // Update Zoom meeting if session is not completed
            if ($session->status !== 'completed' && $session->zoom_meeting_id) {
                $sessionDateTime = Carbon::parse($validated['session_date'], 'Africa/Nairobi');

                $zoomMeeting = $this->zoomService->updateMeeting($session->zoom_meeting_id, [
                    'topic' => $validated['title'],
                    'start_time' => $sessionDateTime->toISOString(),
                    'duration' => $validated['duration_minutes'],
                    'agenda' => $validated['description'],
                ]);

                if (!$zoomMeeting) {
                    throw new \Exception('Failed to update Zoom meeting');
                }
            }

            $session->update($validated);

            return redirect()->route('admin.sessions.index')
                ->with('success', 'Live session updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update live session: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified live session from storage.
     */
    public function destroy(LiveSession $session)
    {
        try {
            // Delete Zoom meeting if it exists
            if ($session->zoom_meeting_id) {
                $this->zoomService->deleteMeeting($session->zoom_meeting_id);
            }

            $session->delete();

            return redirect()->route('admin.sessions.index')
                ->with('success', 'Live session deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->route('admin.sessions.index')
                ->with('error', 'Failed to delete live session: ' . $e->getMessage());
        }
    }

    /**
     * Start a live session.
     */
    public function start(LiveSession $session)
    {
        if ($session->status !== 'scheduled') {
            return redirect()->back()->with('error', 'Session cannot be started.');
        }

        $session->update(['status' => 'live']);

        return redirect($session->zoom_start_url);
    }

    /**
     * End a live session.
     */
    public function end(LiveSession $session)
    {
        if ($session->status !== 'live') {
            return redirect()->back()->with('error', 'Session is not currently live.');
        }

        try {
            // End Zoom meeting
            if ($session->zoom_meeting_id) {
                $this->zoomService->endMeeting($session->zoom_meeting_id);
            }

            $session->update(['status' => 'completed']);

            return redirect()->route('admin.sessions.index')
                ->with('success', 'Live session ended successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to end session: ' . $e->getMessage());
        }
    }

    /**
     * Get session analytics.
     */
    public function analytics(LiveSession $session)
    {
        try {
            $analytics = [];

            if ($session->zoom_meeting_id) {
                // Get meeting participants
                $participants = $this->zoomService->getMeetingParticipants($session->zoom_meeting_id);
                $analytics['participants'] = $participants;

                // Get meeting recordings if available
                $recordings = $this->zoomService->getMeetingRecordings($session->zoom_meeting_id);
                $analytics['recordings'] = $recordings;
            }

            return view('admin.sessions.analytics', compact('session', 'analytics'));

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to load analytics: ' . $e->getMessage());
        }
    }
}
