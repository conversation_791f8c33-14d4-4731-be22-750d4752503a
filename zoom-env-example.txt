# Zoom Server-to-Server OAuth Configuration
# Add these lines to your .env file

# Zoom Server-to-Server OAuth App Credentials
# Get these from your Zoom Marketplace App: https://marketplace.zoom.us/
ZOOM_ACCOUNT_ID=your_zoom_account_id_here
ZOOM_CLIENT_ID=your_zoom_client_id_here
ZOOM_CLIENT_SECRET=your_zoom_client_secret_here

# Instructions to get Zoom credentials:
# 1. Go to https://marketplace.zoom.us/
# 2. Sign in with your Zoom account
# 3. Click "Develop" > "Build App"
# 4. Choose "Server-to-Server OAuth"
# 5. Fill in the app information:
#    - App Name: VCH Learning Platform
#    - Company Name: Your Organization
#    - Developer Contact: Your email
# 6. Add required scopes:
#    - meeting:write:admin (Create meetings)
#    - meeting:read:admin (Read meeting details)
#    - meeting:update:admin (Update meetings)
#    - meeting:delete:admin (Delete meetings)
#    - webinar:write:admin (Create webinars)
#    - webinar:read:admin (Read webinar details)
#    - webinar:update:admin (Update webinars)
#    - webinar:delete:admin (Delete webinars)
#    - user:read:admin (Read user information)
#    - recording:read:admin (Access recordings)
# 7. Copy the Account ID, Client ID, and Client Secret to your .env file
# 8. Activate the app

# Note: Server-to-Server OAuth apps don't require user authorization
# and can create meetings on behalf of the account owner automatically.
