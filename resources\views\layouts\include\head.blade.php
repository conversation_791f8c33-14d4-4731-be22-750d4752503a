<head>
    <meta charset="utf-8">
    <title>Virtual CME Hub - Continuing Professional Development</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="medical education, CME, CPD, virtual learning, healthcare professionals" name="keywords">
    <meta content="Virtual CME Hub - Your premier destination for continuing professional development in healthcare" name="description">

    <!-- Favicon -->
    <link href="img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&family=Roboto:wght@500;700&display=swap" rel="stylesheet">

    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="lib/animate/animate.min.css" rel="stylesheet">
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
    <link href="lib/tempusdominus/css/tempusdominus-bootstrap-4.min.css" rel="stylesheet" />

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/bootstrap.min.css" rel="stylesheet">

    <!-- Template Stylesheet -->
    <link href="css/style.css" rel="stylesheet">

    <!-- Custom VCH Styles -->
    <style>
        :root {
            --primary: #2E8B57;
            --secondary: #FF6B35;
            --light: #F8F9FA;
            --dark: #2C3E50;
            --medical-blue: #4A90E2;
            --medical-green: #2E8B57;
        }

        .vch-logo {
            display: inline-block;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--medical-green), var(--medical-blue));
            border-radius: 10px;
            color: white;
            font-size: 18px;
            font-weight: 700;
            line-height: 50px;
            text-align: center;
            margin-right: 10px;
            box-shadow: 0 4px 15px rgba(46, 139, 87, 0.3);
            transition: all 0.3s ease;
        }

        .vch-logo:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(46, 139, 87, 0.4);
        }

        /* Enhanced Navigation Styles */
        .navbar-nav .nav-link {
            position: relative;
            transition: all 0.3s ease;
            border-radius: 6px;
            margin: 0 2px;
        }

        .navbar-nav .nav-link:hover {
            background-color: rgba(46, 139, 87, 0.1);
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            background-color: rgba(46, 139, 87, 0.15);
            color: var(--medical-green) !important;
        }

        .navbar-nav .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: linear-gradient(135deg, var(--medical-green), var(--medical-blue));
            border-radius: 2px;
        }

        /* Dropdown Enhancements */
        .dropdown-menu {
            border-radius: 12px;
            padding: 0.5rem 0;
            min-width: 280px;
            animation: fadeInUp 0.3s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-item {
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 2px 8px;
        }

        .dropdown-item:hover {
            background-color: rgba(46, 139, 87, 0.1);
            transform: translateX(5px);
        }

        .dropdown-header {
            font-weight: 600;
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }

        /* Search Box Styling */
        .input-group .form-control {
            border-radius: 25px 0 0 25px;
            transition: all 0.3s ease;
        }

        .input-group .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
            border-color: var(--medical-green);
        }

        .input-group .btn {
            border-radius: 0 25px 25px 0;
            transition: all 0.3s ease;
        }

        .input-group .btn:hover {
            background-color: var(--medical-green);
            border-color: var(--medical-green);
            color: white;
        }

        /* Button Enhancements */
        .btn-outline-primary {
            border-color: var(--medical-green);
            color: var(--medical-green);
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background-color: var(--medical-green);
            border-color: var(--medical-green);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(46, 139, 87, 0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--medical-green), var(--medical-blue));
            border: none;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--medical-blue), var(--medical-green));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(46, 139, 87, 0.3);
        }

        /* Live Session CTA Animation */
        .bg-gradient-primary {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(46, 139, 87, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(46, 139, 87, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(46, 139, 87, 0);
            }
        }

        /* Mobile Responsive */
        @media (max-width: 991.98px) {
            .navbar-nav .nav-link {
                padding: 0.75rem 1rem;
                margin: 2px 0;
            }

            .dropdown-menu {
                min-width: 100%;
            }
        }

        /* Topbar Stats Animation */
        .top-bar .d-inline-flex {
            transition: all 0.3s ease;
        }

        .top-bar .d-inline-flex:hover {
            transform: translateY(-2px);
        }

        .bg-white.bg-opacity-20 {
            transition: all 0.3s ease;
        }

        .d-inline-flex:hover .bg-white.bg-opacity-20 {
            background-color: rgba(255, 255, 255, 0.3) !important;
            transform: scale(1.1);
        }
    </style>
</head>
