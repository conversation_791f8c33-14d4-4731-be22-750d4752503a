<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LiveSession>
 */
class LiveSessionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $sessionTitles = [
            'COVID-19 Update: Latest Protocols',
            'Mental Health in Healthcare Workers',
            'Advanced Wound Care Techniques',
            'Medication Safety and Administration',
            'Emergency Triage Protocols',
            'Patient Communication Skills',
            'Healthcare Technology Updates',
            'Infection Prevention Strategies',
            'Pain Management Best Practices',
            'Healthcare Quality Improvement',
            'Clinical Decision Making',
            'Team-Based Care Coordination',
            'Healthcare Ethics Discussion',
            'Medical Device Safety',
            'Patient Safety Initiatives'
        ];

        $instructors = [
            'Dr. <PERSON>',
            'Dr. <PERSON>',
            'Dr. <PERSON>',
            'Dr. <PERSON>',
            'Dr. <PERSON>',
            'Dr. <PERSON>',
            'Dr. <PERSON>',
            'Dr. <PERSON>'
        ];

        $statuses = ['scheduled', 'live', 'completed', 'cancelled'];
        $categories = [
            'Emergency Medicine',
            'Patient Care',
            'Healthcare Management',
            'Clinical Skills',
            'Medical Technology',
            'Healthcare Ethics',
            'Public Health'
        ];

        $title = $this->faker->randomElement($sessionTitles);
        $sessionDate = $this->faker->dateTimeBetween('-1 month', '+3 months');
        $duration = $this->faker->randomElement([60, 90, 120, 180]); // 1-3 hours
        $price = $this->faker->randomElement([0, 19.99, 29.99, 39.99, 49.99]);

        return [
            'title' => $title,
            'slug' => \Illuminate\Support\Str::slug($title),
            'description' => $this->faker->paragraphs(2, true),
            'instructor_name' => $this->faker->randomElement($instructors),
            'instructor_bio' => $this->faker->paragraph(),
            'session_date' => $sessionDate,
            'duration_minutes' => $duration,
            'timezone' => $this->faker->randomElement(['UTC', 'America/New_York', 'America/Los_Angeles', 'Europe/London']),
            'max_participants' => $this->faker->randomElement([25, 50, 100, 200, 500]),
            'current_participants' => $this->faker->numberBetween(0, 50),
            'price' => $price,
            'is_free' => $price == 0,
            'category' => $this->faker->randomElement($categories),
            'level' => $this->faker->randomElement(['Beginner', 'Intermediate', 'Advanced']),
            'prerequisites' => $this->faker->optional(0.3)->sentence(),
            'learning_objectives' => json_encode([
                $this->faker->sentence(),
                $this->faker->sentence(),
                $this->faker->sentence(),
            ]),
            'session_agenda' => json_encode([
                '00:00 - Introduction and Welcome',
                '10:00 - ' . $this->faker->sentence(4),
                '30:00 - ' . $this->faker->sentence(4),
                '50:00 - Q&A Session',
                '60:00 - Wrap-up and Next Steps'
            ]),
            'meeting_url' => $this->faker->optional(0.7)->url(),
            'meeting_id' => $this->faker->optional(0.7)->numerify('###-###-###'),
            'meeting_password' => $this->faker->optional(0.5)->lexify('??????'),
            'recording_url' => $this->faker->optional(0.3)->url(),
            'materials_url' => $this->faker->optional(0.4)->url(),
            'certificate_eligible' => $this->faker->boolean(70),
            'cpd_credits' => $this->faker->optional(0.8)->randomFloat(1, 0.5, 3.0),
            'status' => $this->faker->randomElement($statuses),
            'is_featured' => $this->faker->boolean(15),
            'registration_deadline' => $this->faker->optional(0.6)->dateTimeBetween('now', $sessionDate),
            'created_at' => $this->faker->dateTimeBetween('-2 months', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ];
    }

    /**
     * Indicate that the session is scheduled.
     */
    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'scheduled',
            'session_date' => $this->faker->dateTimeBetween('+1 day', '+2 months'),
        ]);
    }

    /**
     * Indicate that the session is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'session_date' => $this->faker->dateTimeBetween('-2 months', '-1 day'),
            'recording_url' => $this->faker->url(),
        ]);
    }

    /**
     * Indicate that the session is free.
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => 0,
            'is_free' => true,
        ]);
    }

    /**
     * Indicate that the session is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the session offers CPD credits.
     */
    public function withCpdCredits(): static
    {
        return $this->state(fn (array $attributes) => [
            'certificate_eligible' => true,
            'cpd_credits' => $this->faker->randomFloat(1, 1.0, 3.0),
        ]);
    }
}
