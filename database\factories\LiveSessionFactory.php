<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\LiveSession>
 */
class LiveSessionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $sessionTitles = [
            'COVID-19 Update: Latest Protocols',
            'Mental Health in Healthcare Workers',
            'Advanced Wound Care Techniques',
            'Medication Safety and Administration',
            'Emergency Triage Protocols',
            'Patient Communication Skills',
            'Healthcare Technology Updates',
            'Infection Prevention Strategies',
            'Pain Management Best Practices',
            'Healthcare Quality Improvement',
            'Clinical Decision Making',
            'Team-Based Care Coordination',
            'Healthcare Ethics Discussion',
            'Medical Device Safety',
            'Patient Safety Initiatives'
        ];

        $instructors = [
            'Dr. <PERSON>',
            'Dr. <PERSON>',
            'Dr. <PERSON>',
            'Dr. <PERSON>',
            'Dr. <PERSON>',
            'Dr. <PERSON>',
            'Dr. <PERSON>',
            'Dr. <PERSON>'
        ];

        $instructorTitles = [
            'Chief Medical Officer',
            'Director of Education',
            'Quality Assurance Director',
            'Technology Integration Specialist',
            'Emergency Medicine Specialist',
            'Healthcare Ethics Consultant',
            'Clinical Skills Instructor',
            'Medical Research Director'
        ];

        $statuses = ['scheduled', 'live', 'completed', 'cancelled'];
        $categories = [
            'Emergency Medicine',
            'Patient Care',
            'Healthcare Management',
            'Clinical Skills',
            'Medical Technology',
            'Healthcare Ethics',
            'Public Health'
        ];

        $title = $this->faker->randomElement($sessionTitles);
        $sessionDate = $this->faker->dateTimeBetween('-1 month', '+3 months');
        $duration = $this->faker->randomElement([60, 90, 120, 180]); // 1-3 hours
        $price = $this->faker->randomElement([0, 19.99, 29.99, 39.99, 49.99]);

        return [
            'title' => $title,
            'slug' => \Illuminate\Support\Str::slug($title),
            'description' => $this->faker->paragraphs(2, true),
            'instructor' => $this->faker->randomElement($instructors),
            'instructor_title' => $this->faker->randomElement($instructorTitles),
            'instructor_image' => $this->faker->optional(0.7)->passthrough('instructors/instructor-' . $this->faker->numberBetween(1, 8) . '.jpg'),
            'session_date' => $sessionDate,
            'duration_minutes' => $duration,
            'max_participants' => $this->faker->randomElement([25, 50, 100, 200, 500]),
            'price' => $price,
            'is_free' => $price == 0,
            'category' => $this->faker->randomElement($categories),
            'level' => $this->faker->randomElement(['Beginner', 'Intermediate', 'Advanced']),
            'prerequisites' => $this->faker->optional(0.3)->sentence(),
            'materials' => json_encode([
                'slides' => $this->faker->optional(0.7)->url(),
                'handouts' => $this->faker->optional(0.5)->url(),
                'resources' => $this->faker->optional(0.6)->url(),
            ]),
            'meeting_url' => $this->faker->optional(0.7)->url(),
            'meeting_id' => $this->faker->optional(0.7)->numerify('###-###-###'),
            'meeting_password' => $this->faker->optional(0.5)->lexify('??????'),
            'cpd_credits' => $this->faker->optional(0.8)->randomFloat(1, 0.5, 3.0),
            'status' => $this->faker->randomElement($statuses),
            'registration_required' => $this->faker->boolean(80),
            'auto_record' => $this->faker->boolean(70),
            'waiting_room' => $this->faker->boolean(60),
            'join_before_host' => $this->faker->boolean(40),
            'mute_participants' => $this->faker->boolean(50),
            'registration_deadline' => $this->faker->optional(0.6)->dateTimeBetween('now', $sessionDate),
            'created_at' => $this->faker->dateTimeBetween('-2 months', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ];
    }

    /**
     * Indicate that the session is scheduled.
     */
    public function scheduled(): static
    {
        return $this->state(function (array $attributes) {
            // Ensure title exists to generate slug
            $title = $attributes['title'] ?? $this->faker->sentence(4);
            return [
                'title' => $title,
                'slug' => \Illuminate\Support\Str::slug($title),
                'status' => 'scheduled',
                'session_date' => $this->faker->dateTimeBetween('+1 day', '+2 months'),
            ];
        });
    }

    /**
     * Indicate that the session is completed.
     */
    public function completed(): static
    {
        return $this->state(function (array $attributes) {
            // Ensure title exists to generate slug
            $title = $attributes['title'] ?? $this->faker->sentence(4);
            return [
                'title' => $title,
                'slug' => \Illuminate\Support\Str::slug($title),
                'status' => 'completed',
                'session_date' => $this->faker->dateTimeBetween('-2 months', '-1 day'),
                'recording_url' => $this->faker->url(),
            ];
        });
    }

    /**
     * Indicate that the session is free.
     */
    public function free(): static
    {
        return $this->state(function (array $attributes) {
            // Ensure title exists to generate slug
            $title = $attributes['title'] ?? $this->faker->sentence(4);
            return [
                'title' => $title,
                'slug' => \Illuminate\Support\Str::slug($title),
                'price' => 0,
                'is_free' => true,
            ];
        });
    }

    /**
     * Indicate that the session is featured.
     */
    public function featured(): static
    {
        return $this->state(function (array $attributes) {
            // Ensure title exists to generate slug
            $title = $attributes['title'] ?? $this->faker->sentence(4);
            return [
                'title' => $title,
                'slug' => \Illuminate\Support\Str::slug($title),
                'is_featured' => true,
            ];
        });
    }

    /**
     * Indicate that the session offers CPD credits.
     */
    public function withCpdCredits(): static
    {
        return $this->state(function (array $attributes) {
            // Ensure title exists to generate slug
            $title = $attributes['title'] ?? $this->faker->sentence(4);
            return [
                'title' => $title,
                'slug' => \Illuminate\Support\Str::slug($title),
                'certificate_eligible' => true,
                'cpd_credits' => $this->faker->randomFloat(1, 1.0, 3.0),
            ];
        });
    }
}
