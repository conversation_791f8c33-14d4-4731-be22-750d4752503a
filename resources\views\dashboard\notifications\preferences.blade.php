@extends('layouts.dashboard')

@section('page-title', 'Notification Preferences')

@section('content')
<div class="dashboard-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Notification Preferences</h2>
                    <p class="text-muted mb-0">Customize how and when you receive notifications</p>
                </div>
                <div>
                    <a href="{{ route('dashboard.notifications.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Notifications
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ route('dashboard.notifications.preferences.update') }}">
        @csrf
        
        <div class="row">
            <!-- Notification Types -->
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-bell me-2 text-primary"></i>Notification Types</h5>
                        <small class="text-muted">Choose which types of notifications you want to receive</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Session Reminders -->
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="session_reminders" 
                                           name="session_reminders" value="1" 
                                           {{ $preferences['session_reminders'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="session_reminders">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-video text-primary me-2"></i>
                                            <div>
                                                <strong>Session Reminders</strong>
                                                <br><small class="text-muted">Reminders for upcoming live sessions</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>

                                <!-- Course Updates -->
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="course_updates" 
                                           name="course_updates" value="1" 
                                           {{ $preferences['course_updates'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="course_updates">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-graduation-cap text-success me-2"></i>
                                            <div>
                                                <strong>Course Updates</strong>
                                                <br><small class="text-muted">Updates about your enrolled courses</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>

                                <!-- Certificate Notifications -->
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="certificate_notifications" 
                                           name="certificate_notifications" value="1" 
                                           {{ $preferences['certificate_notifications'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="certificate_notifications">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-certificate text-warning me-2"></i>
                                            <div>
                                                <strong>Certificate Notifications</strong>
                                                <br><small class="text-muted">When certificates are ready for download</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>

                                <!-- Payment Reminders -->
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="payment_reminders" 
                                           name="payment_reminders" value="1" 
                                           {{ $preferences['payment_reminders'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="payment_reminders">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-credit-card text-danger me-2"></i>
                                            <div>
                                                <strong>Payment Reminders</strong>
                                                <br><small class="text-muted">Billing and subscription reminders</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <!-- System Updates -->
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="system_updates" 
                                           name="system_updates" value="1" 
                                           {{ $preferences['system_updates'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="system_updates">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-cog text-info me-2"></i>
                                            <div>
                                                <strong>System Updates</strong>
                                                <br><small class="text-muted">Platform updates and maintenance notices</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>

                                <!-- Marketing Emails -->
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="marketing_emails" 
                                           name="marketing_emails" value="1" 
                                           {{ $preferences['marketing_emails'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="marketing_emails">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-envelope text-secondary me-2"></i>
                                            <div>
                                                <strong>Marketing Emails</strong>
                                                <br><small class="text-muted">Promotional content and new course announcements</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delivery Methods -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-paper-plane me-2 text-primary"></i>Delivery Methods</h5>
                        <small class="text-muted">Choose how you want to receive notifications</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Email Notifications -->
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="email_notifications" 
                                           name="email_notifications" value="1" 
                                           {{ $preferences['email_notifications'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="email_notifications">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-envelope text-primary me-2"></i>
                                            <div>
                                                <strong>Email Notifications</strong>
                                                <br><small class="text-muted">Receive notifications via email</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <!-- Push Notifications -->
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="push_notifications" 
                                           name="push_notifications" value="1" 
                                           {{ $preferences['push_notifications'] ? 'checked' : '' }}>
                                    <label class="form-check-label" for="push_notifications">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-mobile-alt text-success me-2"></i>
                                            <div>
                                                <strong>Push Notifications</strong>
                                                <br><small class="text-muted">Receive notifications in your browser</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h6 class="mb-0"><i class="fas fa-bolt me-2 text-primary"></i>Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success" onclick="enableAll()">
                                <i class="fas fa-check-circle me-2"></i>Enable All
                            </button>
                            <button type="button" class="btn btn-warning" onclick="enableEssential()">
                                <i class="fas fa-star me-2"></i>Essential Only
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="disableAll()">
                                <i class="fas fa-times-circle me-2"></i>Disable All
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2 text-primary"></i>Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <small>
                                <strong>Note:</strong> Some notifications are required for account security and cannot be disabled.
                            </small>
                        </div>
                        
                        <div class="mb-3">
                            <small class="text-muted d-block mb-1">Current Email:</small>
                            <strong>{{ Auth::user()->email }}</strong>
                        </div>

                        <a href="{{ route('profile.edit') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>Update Email
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <a href="{{ route('dashboard.notifications.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Save Preferences
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
function enableAll() {
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = true;
    });
}

function enableEssential() {
    // Disable all first
    disableAll();
    
    // Enable essential notifications
    const essential = ['session_reminders', 'course_updates', 'certificate_notifications', 'payment_reminders', 'email_notifications'];
    essential.forEach(id => {
        const checkbox = document.getElementById(id);
        if (checkbox) {
            checkbox.checked = true;
        }
    });
}

function disableAll() {
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
}
</script>
@endpush
@endsection
