<?php

namespace App\Http\Controllers;

use App\Models\SupportTicket;
use App\Models\FAQ;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SupportController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        // Get recent tickets
        $recentTickets = SupportTicket::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get popular FAQs
        $popularFaqs = FAQ::active()
            ->orderBy('order')
            ->take(6)
            ->get();

        // Get ticket statistics
        $stats = [
            'total_tickets' => SupportTicket::where('user_id', $user->id)->count(),
            'open_tickets' => SupportTicket::where('user_id', $user->id)->where('status', 'open')->count(),
            'resolved_tickets' => SupportTicket::where('user_id', $user->id)->where('status', 'resolved')->count(),
        ];

        return view('dashboard.support.index', compact('recentTickets', 'popularFaqs', 'stats'));
    }

    public function help(Request $request)
    {
        $category = $request->get('category', 'all');
        $search = $request->get('search');

        $query = FAQ::active();

        if ($category !== 'all') {
            $query->where('category', $category);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('question', 'like', "%{$search}%")
                  ->orWhere('answer', 'like', "%{$search}%");
            });
        }

        $faqs = $query->orderBy('order')->paginate(10);

        // Get categories
        $categories = FAQ::active()->distinct()->pluck('category');

        return view('dashboard.support.help', compact('faqs', 'categories', 'category', 'search'));
    }

    public function contact()
    {
        return view('dashboard.support.contact');
    }

    public function submitTicket(Request $request)
    {
        $request->validate([
            'subject' => 'required|string|max:255',
            'category' => 'required|string|in:general,technical,billing,course,session',
            'priority' => 'required|string|in:low,normal,high,urgent',
            'message' => 'required|string|min:10',
        ]);

        $user = Auth::user();

        $ticket = SupportTicket::create([
            'user_id' => $user->id,
            'ticket_number' => $this->generateTicketNumber(),
            'subject' => $request->subject,
            'category' => $request->category,
            'priority' => $request->priority,
            'message' => $request->message,
            'status' => 'open',
        ]);

        // Create notification for user
        \App\Models\Notification::createForUser(
            $user->id,
            'support_ticket',
            'Support Ticket Created',
            "Your support ticket #{$ticket->ticket_number} has been created and will be reviewed shortly.",
            ['ticket_id' => $ticket->id]
        );

        return redirect()->route('dashboard.support.tickets.show', $ticket)
            ->with('success', 'Support ticket created successfully! Ticket #' . $ticket->ticket_number);
    }

    public function tickets(Request $request)
    {
        $user = Auth::user();
        $status = $request->get('status', 'all');

        $query = SupportTicket::where('user_id', $user->id);

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $tickets = $query->orderBy('created_at', 'desc')->paginate(10);

        // Get statistics
        $stats = [
            'total_count' => SupportTicket::where('user_id', $user->id)->count(),
            'open_count' => SupportTicket::where('user_id', $user->id)->where('status', 'open')->count(),
            'in_progress_count' => SupportTicket::where('user_id', $user->id)->where('status', 'in_progress')->count(),
            'resolved_count' => SupportTicket::where('user_id', $user->id)->where('status', 'resolved')->count(),
            'closed_count' => SupportTicket::where('user_id', $user->id)->where('status', 'closed')->count(),
        ];

        return view('dashboard.support.tickets', compact('tickets', 'stats', 'status'));
    }

    public function showTicket(SupportTicket $ticket)
    {
        // Check if user owns this ticket
        if ($ticket->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to support ticket.');
        }

        return view('dashboard.support.show-ticket', compact('ticket'));
    }

    public function addReply(Request $request, SupportTicket $ticket)
    {
        // Check if user owns this ticket
        if ($ticket->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to ticket.');
        }

        $request->validate([
            'message' => 'required|string|min:10',
        ]);

        $reply = $ticket->replies()->create([
            'user_id' => Auth::id(),
            'message' => $request->message,
            'is_staff_reply' => false,
        ]);

        // Update ticket status if it was resolved
        if ($ticket->status === 'resolved') {
            $ticket->update(['status' => 'open']);
        }

        // Create notification for staff (in real implementation)
        // This would notify support staff about the new reply

        return redirect()->back()->with('success', 'Reply added successfully.');
    }

    public function closeTicket(SupportTicket $ticket)
    {
        // Check if user owns this ticket
        if ($ticket->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to ticket.');
        }

        $ticket->markAsClosed();

        return redirect()->route('dashboard.support.tickets')
            ->with('success', 'Ticket closed successfully.');
    }

    public function searchKnowledgeBase(Request $request)
    {
        $search = $request->get('q');

        if (!$search) {
            return response()->json(['results' => []]);
        }

        $faqs = FAQ::active()
            ->where(function ($query) use ($search) {
                $query->where('question', 'like', "%{$search}%")
                      ->orWhere('answer', 'like', "%{$search}%");
            })
            ->orderBy('order')
            ->take(5)
            ->get(['id', 'question', 'category']);

        return response()->json(['results' => $faqs]);
    }

    public function rateFaq(Request $request, FAQ $faq)
    {
        $request->validate([
            'helpful' => 'required|boolean',
        ]);

        // In a real implementation, you would store FAQ ratings
        // For now, we'll just return success

        return response()->json([
            'success' => true,
            'message' => 'Thank you for your feedback!'
        ]);
    }

    private function generateTicketNumber()
    {
        $prefix = 'VCH';
        $year = now()->year;
        $random = str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);

        return $prefix . '-' . $year . '-' . $random;
    }
}
