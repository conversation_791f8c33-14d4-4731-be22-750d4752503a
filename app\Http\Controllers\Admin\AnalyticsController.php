<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Course;
use App\Models\LiveSession;
use App\Models\SupportTicket;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    /**
     * Display analytics dashboard.
     */
    public function index()
    {
        // Get overview statistics
        $stats = [
            'total_users' => User::count(),
            'total_courses' => Course::count(),
            'total_sessions' => LiveSession::count(),
            'total_tickets' => SupportTicket::count(),
            'active_users' => User::where('is_active', true)->count(),
            'published_courses' => Course::where('status', 'published')->count(),
            'upcoming_sessions' => LiveSession::where('session_date', '>', now())->count(),
            'open_tickets' => SupportTicket::whereIn('status', ['open', 'in_progress'])->count(),
        ];

        // Get monthly user registrations for the last 12 months
        $monthlyUsers = $this->getMonthlyUserRegistrations();
        
        // Get course enrollment statistics
        $courseStats = $this->getCourseStatistics();
        
        // Get user activity by role
        $usersByRole = $this->getUsersByRole();
        
        // Get session attendance statistics
        $sessionStats = $this->getSessionStatistics();
        
        // Get support ticket statistics
        $ticketStats = $this->getTicketStatistics();
        
        // Get recent activity
        $recentActivity = $this->getRecentActivity();

        return view('admin.analytics.index', compact(
            'stats',
            'monthlyUsers',
            'courseStats',
            'usersByRole',
            'sessionStats',
            'ticketStats',
            'recentActivity'
        ));
    }

    /**
     * Get monthly user registrations for the last 12 months
     */
    private function getMonthlyUserRegistrations()
    {
        $months = [];
        $counts = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = $date->format('M Y');
            
            $count = User::whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();
            $counts[] = $count;
        }

        return [
            'months' => $months,
            'counts' => $counts,
        ];
    }

    /**
     * Get course statistics
     */
    private function getCourseStatistics()
    {
        $statusCounts = Course::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $categoryCounts = Course::select('category', DB::raw('count(*) as count'))
            ->whereNotNull('category')
            ->groupBy('category')
            ->orderBy('count', 'desc')
            ->take(10)
            ->pluck('count', 'category')
            ->toArray();

        return [
            'by_status' => $statusCounts,
            'by_category' => $categoryCounts,
        ];
    }

    /**
     * Get users by role
     */
    private function getUsersByRole()
    {
        return User::select('role', DB::raw('count(*) as count'))
            ->groupBy('role')
            ->pluck('count', 'role')
            ->toArray();
    }

    /**
     * Get session statistics
     */
    private function getSessionStatistics()
    {
        $statusCounts = LiveSession::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $monthlySessions = [];
        $monthlySessionCounts = [];

        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthlySessions[] = $date->format('M Y');
            
            $count = LiveSession::whereYear('session_date', $date->year)
                ->whereMonth('session_date', $date->month)
                ->count();
            $monthlySessionCounts[] = $count;
        }

        return [
            'by_status' => $statusCounts,
            'monthly' => [
                'months' => $monthlySessions,
                'counts' => $monthlySessionCounts,
            ],
        ];
    }

    /**
     * Get support ticket statistics
     */
    private function getTicketStatistics()
    {
        $statusCounts = SupportTicket::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $priorityCounts = SupportTicket::select('priority', DB::raw('count(*) as count'))
            ->groupBy('priority')
            ->pluck('count', 'priority')
            ->toArray();

        $categoryCounts = SupportTicket::select('category', DB::raw('count(*) as count'))
            ->groupBy('category')
            ->pluck('count', 'category')
            ->toArray();

        return [
            'by_status' => $statusCounts,
            'by_priority' => $priorityCounts,
            'by_category' => $categoryCounts,
        ];
    }

    /**
     * Get recent activity
     */
    private function getRecentActivity()
    {
        $activities = collect();

        // Recent user registrations
        $recentUsers = User::latest()->take(5)->get();
        foreach ($recentUsers as $user) {
            $activities->push([
                'type' => 'user_registered',
                'description' => "New user registered: {$user->name}",
                'timestamp' => $user->created_at,
                'icon' => 'fas fa-user-plus',
                'color' => 'success',
            ]);
        }

        // Recent courses
        $recentCourses = Course::latest()->take(3)->get();
        foreach ($recentCourses as $course) {
            $activities->push([
                'type' => 'course_created',
                'description' => "New course created: {$course->title}",
                'timestamp' => $course->created_at,
                'icon' => 'fas fa-graduation-cap',
                'color' => 'primary',
            ]);
        }

        // Recent support tickets
        $recentTickets = SupportTicket::latest()->take(3)->get();
        foreach ($recentTickets as $ticket) {
            $activities->push([
                'type' => 'ticket_created',
                'description' => "New support ticket: {$ticket->subject}",
                'timestamp' => $ticket->created_at,
                'icon' => 'fas fa-ticket-alt',
                'color' => 'warning',
            ]);
        }

        return $activities->sortByDesc('timestamp')->take(10);
    }

    /**
     * Export analytics data
     */
    public function export(Request $request)
    {
        $type = $request->get('type', 'users');
        
        switch ($type) {
            case 'users':
                return $this->exportUsers();
            case 'courses':
                return $this->exportCourses();
            case 'sessions':
                return $this->exportSessions();
            case 'tickets':
                return $this->exportTickets();
            default:
                return redirect()->back()->with('error', 'Invalid export type.');
        }
    }

    private function exportUsers()
    {
        $users = User::select('name', 'email', 'role', 'organization', 'is_active', 'created_at')->get();
        
        $filename = 'users_export_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($users) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Name', 'Email', 'Role', 'Organization', 'Status', 'Registered']);
            
            foreach ($users as $user) {
                fputcsv($file, [
                    $user->name,
                    $user->email,
                    $user->role,
                    $user->organization,
                    $user->is_active ? 'Active' : 'Inactive',
                    $user->created_at->format('Y-m-d H:i:s'),
                ]);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function exportCourses()
    {
        $courses = Course::select('title', 'category', 'level', 'status', 'price', 'created_at')->get();
        
        $filename = 'courses_export_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($courses) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Title', 'Category', 'Level', 'Status', 'Price', 'Created']);
            
            foreach ($courses as $course) {
                fputcsv($file, [
                    $course->title,
                    $course->category,
                    $course->level,
                    $course->status,
                    $course->price,
                    $course->created_at->format('Y-m-d H:i:s'),
                ]);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function exportSessions()
    {
        $sessions = LiveSession::select('title', 'instructor', 'session_date', 'status', 'max_participants', 'created_at')->get();
        
        $filename = 'sessions_export_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($sessions) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Title', 'Instructor', 'Session Date', 'Status', 'Max Participants', 'Created']);
            
            foreach ($sessions as $session) {
                fputcsv($file, [
                    $session->title,
                    $session->instructor,
                    $session->session_date,
                    $session->status,
                    $session->max_participants,
                    $session->created_at->format('Y-m-d H:i:s'),
                ]);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function exportTickets()
    {
        $tickets = SupportTicket::with('user')
            ->select('ticket_number', 'subject', 'category', 'priority', 'status', 'user_id', 'created_at')
            ->get();
        
        $filename = 'tickets_export_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($tickets) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Ticket Number', 'Subject', 'User', 'Category', 'Priority', 'Status', 'Created']);
            
            foreach ($tickets as $ticket) {
                fputcsv($file, [
                    $ticket->ticket_number,
                    $ticket->subject,
                    $ticket->user->name ?? 'N/A',
                    $ticket->category,
                    $ticket->priority,
                    $ticket->status,
                    $ticket->created_at->format('Y-m-d H:i:s'),
                ]);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
