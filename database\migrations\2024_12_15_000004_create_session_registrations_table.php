<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('session_registrations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('live_session_id')->constrained()->onDelete('cascade');
            $table->timestamp('registered_at');
            $table->timestamp('attended_at')->nullable();
            $table->integer('attendance_duration')->nullable(); // minutes attended
            $table->string('status')->default('registered'); // registered, attended, missed, cancelled
            $table->json('feedback')->nullable(); // session feedback
            $table->timestamps();

            $table->unique(['user_id', 'live_session_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('session_registrations');
    }
};
