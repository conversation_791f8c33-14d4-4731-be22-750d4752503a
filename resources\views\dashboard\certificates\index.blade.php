@extends('layouts.dashboard')

@section('page-title', 'Certificates')

@section('content')
<div class="dashboard-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">My Certificates</h2>
                    <p class="text-muted mb-0">View and manage your earned certificates</p>
                </div>
                <div>
                    <a href="{{ route('dashboard.certificates.cpd') }}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line me-2"></i>CPD Tracking
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-certificate text-primary fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-primary mb-1">{{ $stats['total_certificates'] }}</h3>
                    <p class="text-muted mb-0">Total Certificates</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-check-circle text-success fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-success mb-1">{{ $stats['active_certificates'] }}</h3>
                    <p class="text-muted mb-0">Active Certificates</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-clock text-warning fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-warning mb-1">{{ $stats['expired_certificates'] }}</h3>
                    <p class="text-muted mb-0">Expired Certificates</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-award text-info fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-info mb-1">{{ $stats['total_cpd_credits'] }}</h3>
                    <p class="text-muted mb-0">Total CPD Credits</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="btn-group" role="group">
                                <a href="{{ route('dashboard.certificates.index', ['filter' => 'all']) }}"
                                   class="btn {{ $filter === 'all' ? 'btn-primary' : 'btn-outline-primary' }}">
                                    <i class="fas fa-list me-1"></i>All Certificates
                                </a>
                                <a href="{{ route('dashboard.certificates.index', ['filter' => 'active']) }}"
                                   class="btn {{ $filter === 'active' ? 'btn-primary' : 'btn-outline-primary' }}">
                                    <i class="fas fa-check-circle me-1"></i>Active
                                </a>
                                <a href="{{ route('dashboard.certificates.index', ['filter' => 'expired']) }}"
                                   class="btn {{ $filter === 'expired' ? 'btn-primary' : 'btn-outline-primary' }}">
                                    <i class="fas fa-clock me-1"></i>Expired
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <form method="GET" class="d-flex">
                                <input type="hidden" name="filter" value="{{ $filter }}">
                                <select name="type" class="form-select me-2" onchange="this.form.submit()">
                                    <option value="">All Types</option>
                                    @foreach($types as $typeOption)
                                        <option value="{{ $typeOption }}" {{ $type == $typeOption ? 'selected' : '' }}>
                                            {{ ucfirst($typeOption) }}
                                        </option>
                                    @endforeach
                                </select>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Certificates Grid -->
    <div class="row">
        @forelse($certificates as $certificate)
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h5 class="card-title mb-2">{{ $certificate->title }}</h5>
                            <p class="text-muted mb-2">{{ Str::limit($certificate->description, 100) }}</p>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-{{ $certificate->isActive() ? 'success' : 'warning' }}">
                                {{ $certificate->isActive() ? 'Active' : 'Expired' }}
                            </span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted d-block">Certificate Number</small>
                            <strong>{{ $certificate->certificate_number }}</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">Type</small>
                            <strong>{{ ucfirst($certificate->certificate_type) }}</strong>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted d-block">CPD Credits</small>
                            <strong>{{ $certificate->cpd_credits }} credits</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">Issued Date</small>
                            <strong>{{ $certificate->issued_at->format('M j, Y') }}</strong>
                        </div>
                    </div>

                    @if($certificate->expires_at)
                    <div class="row mb-3">
                        <div class="col-12">
                            <small class="text-muted d-block">Expires</small>
                            <strong class="{{ $certificate->isExpired() ? 'text-danger' : 'text-success' }}">
                                {{ $certificate->expires_at->format('M j, Y') }}
                            </strong>
                        </div>
                    </div>
                    @endif

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            @if($certificate->course)
                                <small class="text-muted">
                                    <i class="fas fa-graduation-cap me-1"></i>{{ $certificate->course->title }}
                                </small>
                            @elseif($certificate->liveSession)
                                <small class="text-muted">
                                    <i class="fas fa-video me-1"></i>{{ $certificate->liveSession->title }}
                                </small>
                            @endif
                        </div>

                        <div class="btn-group">
                            <a href="{{ route('dashboard.certificates.show', $certificate) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View
                            </a>

                            @if($certificate->isActive())
                                <a href="{{ route('dashboard.certificates.download', $certificate) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-download me-1"></i>Download
                                </a>
                                <a href="{{ route('dashboard.certificates.share', $certificate) }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-share me-1"></i>Share
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @empty
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-certificate fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No certificates found</h4>
                    <p class="text-muted mb-4">
                        @if($filter === 'active')
                            You don't have any active certificates at the moment.
                        @elseif($filter === 'expired')
                            You don't have any expired certificates.
                        @else
                            You haven't earned any certificates yet. Complete courses or attend live sessions to earn certificates.
                        @endif
                    </p>
                    <a href="{{ route('dashboard.courses.index') }}" class="btn btn-primary me-2">
                        <i class="fas fa-graduation-cap me-2"></i>Browse Courses
                    </a>
                    <a href="{{ route('dashboard.live-sessions.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-video me-2"></i>View Live Sessions
                    </a>
                </div>
            </div>
        </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($certificates->hasPages())
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-center">
                {{ $certificates->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any certificate-specific JavaScript here
    console.log('Certificates page loaded');
});
</script>
@endpush
