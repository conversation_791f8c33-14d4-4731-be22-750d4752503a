<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plan_id',
        'status',
        'current_period_start',
        'current_period_end',
        'trial_start',
        'trial_end',
        'canceled_at',
        'ends_at',
        'stripe_subscription_id',
        'stripe_customer_id',
    ];

    protected $casts = [
        'current_period_start' => 'datetime',
        'current_period_end' => 'datetime',
        'trial_start' => 'datetime',
        'trial_end' => 'datetime',
        'canceled_at' => 'datetime',
        'ends_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function plan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id');
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeTrialing($query)
    {
        return $query->where('status', 'trialing');
    }

    public function scopeCanceled($query)
    {
        return $query->where('status', 'canceled');
    }

    public function isActive()
    {
        return $this->status === 'active';
    }

    public function isTrialing()
    {
        return $this->status === 'trialing';
    }

    public function isCanceled()
    {
        return $this->status === 'canceled';
    }

    public function isExpired()
    {
        return $this->ends_at && $this->ends_at->isPast();
    }

    public function onTrial()
    {
        return $this->trial_end && $this->trial_end->isFuture();
    }

    public function daysUntilExpiry()
    {
        if (!$this->current_period_end) {
            return null;
        }

        return $this->current_period_end->diffInDays(now());
    }

    public function cancel()
    {
        $this->update([
            'status' => 'canceled',
            'canceled_at' => now(),
            'ends_at' => $this->current_period_end,
        ]);
    }

    public function resume()
    {
        $this->update([
            'status' => 'active',
            'canceled_at' => null,
            'ends_at' => null,
        ]);
    }

    public function changePlan(SubscriptionPlan $newPlan)
    {
        $this->update(['plan_id' => $newPlan->id]);
    }

    public function getTotalSpent()
    {
        return $this->invoices()->where('status', 'paid')->sum('amount');
    }
}
