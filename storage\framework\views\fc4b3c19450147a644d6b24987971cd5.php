<?php $__env->startSection('title', 'Register'); ?>

<?php $__env->startSection('content'); ?>
    <div class="text-center mb-4">
        <h3 class="text-dark mb-2">Create Account</h3>
        <p class="text-muted">Join Virtual CME Hub for professional development</p>
    </div>

    <form method="POST" action="<?php echo e(route('register')); ?>">
        <?php echo csrf_field(); ?>

        <div class="row">
            <!-- Full Name -->
            <div class="col-12 mb-3">
                <label for="name" class="form-label text-dark fw-medium"><?php echo e(__('Full Name')); ?> <span class="text-danger">*</span></label>
                <input id="name" type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       name="name" value="<?php echo e(old('name')); ?>" required autofocus autocomplete="name"
                       placeholder="Enter your full name">
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Email Address -->
            <div class="col-12 mb-3">
                <label for="email" class="form-label text-dark fw-medium"><?php echo e(__('Professional Email')); ?> <span class="text-danger">*</span></label>
                <input id="email" type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       name="email" value="<?php echo e(old('email')); ?>" required autocomplete="username"
                       placeholder="Enter your professional email address">
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- KMLTTB No and National ID in same row -->
            <div class="col-md-6 mb-3">
                <label for="kmlttb_no" class="form-label text-dark fw-medium"><?php echo e(__('KMLTTB No')); ?></label>
                <input id="kmlttb_no" type="text" class="form-control <?php $__errorArgs = ['kmlttb_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       name="kmlttb_no" value="<?php echo e(old('kmlttb_no')); ?>" autocomplete="kmlttb_no"
                       placeholder="Enter KMLTTB number">
                <?php $__errorArgs = ['kmlttb_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="col-md-6 mb-3">
                <label for="national_id" class="form-label text-dark fw-medium"><?php echo e(__('National ID')); ?></label>
                <input id="national_id" type="text" class="form-control <?php $__errorArgs = ['national_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       name="national_id" value="<?php echo e(old('national_id')); ?>" autocomplete="national_id"
                       placeholder="Enter national ID number">
                <?php $__errorArgs = ['national_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Password -->
            <div class="col-md-6 mb-3">
                <label for="password" class="form-label text-dark fw-medium"><?php echo e(__('Password')); ?> <span class="text-danger">*</span></label>
                <input id="password" type="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       name="password" required autocomplete="new-password"
                       placeholder="Create a strong password">
                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Confirm Password -->
            <div class="col-md-6 mb-3">
                <label for="password_confirmation" class="form-label text-dark fw-medium"><?php echo e(__('Confirm Password')); ?> <span class="text-danger">*</span></label>
                <input id="password_confirmation" type="password" class="form-control <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       name="password_confirmation" required autocomplete="new-password"
                       placeholder="Confirm your password">
                <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Data Collection Consent -->
        <div class="mb-4">
            <div class="card border-0 bg-light">
                <div class="card-body p-3">
                    <h6 class="card-title text-primary mb-3">
                        <i class="fas fa-shield-alt me-2"></i>Data Collection & Privacy Consent
                    </h6>

                    <div class="form-check mb-3">
                        <input class="form-check-input <?php $__errorArgs = ['data_consent'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               type="checkbox" id="data_consent" name="data_consent"
                               value="1" <?php echo e(old('data_consent') ? 'checked' : ''); ?> required>
                        <label class="form-check-label text-sm" for="data_consent">
                            <strong>I consent to data collection and processing</strong> <span class="text-danger">*</span>
                            <br>
                            <small class="text-muted">
                                I agree to allow Virtual CME Hub to collect, store, and process my personal and professional data
                                for the purpose of providing medical education services, tracking CPD credits, and improving
                                the learning experience.
                            </small>
                        </label>
                        <?php $__errorArgs = ['data_consent'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback d-block"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            By registering, you acknowledge that you have read and agree to our
                            <a href="#" class="auth-link">Privacy Policy</a> and
                            <a href="#" class="auth-link">Terms of Service</a>.
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="d-grid mb-4">
            <button type="submit" class="btn btn-auth">
                <i class="fas fa-user-plus me-2"></i><?php echo e(__('Create Account')); ?>

            </button>
        </div>

        <!-- Links -->
        <div class="text-center">
            <p class="mb-0">
                <?php echo e(__('Already have an account?')); ?>

                <a href="<?php echo e(route('login')); ?>" class="auth-link fw-medium">
                    <?php echo e(__('Sign In')); ?>

                </a>
            </p>
        </div>
    </form>

    <style>
        .auth-card {
            max-width: 550px !important;
        }

        .form-check-label.text-sm {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .card {
            border-radius: 10px;
        }

        .form-check-input:checked {
            background-color: var(--medical-green);
            border-color: var(--medical-green);
        }

        .form-check-input:focus {
            border-color: var(--medical-green);
            box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\VCH\resources\views/auth/register.blade.php ENDPATH**/ ?>