<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CarouselSlide;
use App\Models\Service;
use App\Models\TeamMember;
use App\Models\Testimonial;
use App\Models\Course;
use App\Models\LiveSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class ContentController extends Controller
{
    public function index()
    {
        $stats = [
            'carousel_slides' => CarouselSlide::count(),
            'services' => Service::count(),
            'team_members' => TeamMember::count(),
            'testimonials' => Testimonial::count(),
            'courses' => Course::count(),
            'live_sessions' => LiveSession::count(),
        ];

        return view('admin.content.index', compact('stats'));
    }

    // Carousel Slides Management
    public function carouselIndex()
    {
        $slides = CarouselSlide::orderBy('order')->paginate(10);
        return view('admin.content.carousel.index', compact('slides'));
    }

    public function carouselCreate()
    {
        return view('admin.content.carousel.create');
    }

    public function carouselStore(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'required|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'primary_button_text' => 'nullable|string|max:50',
            'primary_button_link' => 'nullable|string|max:255',
            'secondary_button_text' => 'nullable|string|max:50',
            'secondary_button_link' => 'nullable|string|max:255',
            'order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('carousel', 'public');
        }

        CarouselSlide::create($data);

        return redirect()->route('admin.content.carousel.index')
            ->with('success', 'Carousel slide created successfully.');
    }

    public function carouselEdit(CarouselSlide $slide)
    {
        return view('admin.content.carousel.edit', compact('slide'));
    }

    public function carouselUpdate(Request $request, CarouselSlide $slide)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'primary_button_text' => 'nullable|string|max:50',
            'primary_button_link' => 'nullable|string|max:255',
            'secondary_button_text' => 'nullable|string|max:50',
            'secondary_button_link' => 'nullable|string|max:255',
            'order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            // Delete old image
            if ($slide->image) {
                Storage::disk('public')->delete($slide->image);
            }
            $data['image'] = $request->file('image')->store('carousel', 'public');
        }

        $slide->update($data);

        return redirect()->route('admin.content.carousel.index')
            ->with('success', 'Carousel slide updated successfully.');
    }

    public function carouselDestroy(CarouselSlide $slide)
    {
        if ($slide->image) {
            Storage::disk('public')->delete($slide->image);
        }

        $slide->delete();

        return redirect()->route('admin.content.carousel.index')
            ->with('success', 'Carousel slide deleted successfully.');
    }

    // Services Management
    public function servicesIndex()
    {
        $services = Service::orderBy('order')->paginate(10);
        return view('admin.content.services.index', compact('services'));
    }

    public function servicesCreate()
    {
        return view('admin.content.services.create');
    }

    public function servicesStore(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'icon' => 'nullable|string|max:100',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('services', 'public');
        }

        Service::create($data);

        return redirect()->route('admin.content.services.index')
            ->with('success', 'Service created successfully.');
    }

    public function servicesEdit(Service $service)
    {
        return view('admin.content.services.edit', compact('service'));
    }

    public function servicesUpdate(Request $request, Service $service)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'icon' => 'nullable|string|max:100',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            if ($service->image) {
                Storage::disk('public')->delete($service->image);
            }
            $data['image'] = $request->file('image')->store('services', 'public');
        }

        $service->update($data);

        return redirect()->route('admin.content.services.index')
            ->with('success', 'Service updated successfully.');
    }

    public function servicesDestroy(Service $service)
    {
        if ($service->image) {
            Storage::disk('public')->delete($service->image);
        }

        $service->delete();

        return redirect()->route('admin.content.services.index')
            ->with('success', 'Service deleted successfully.');
    }

    // Team Members Management
    public function teamIndex()
    {
        $members = TeamMember::orderBy('order')->paginate(10);
        return view('admin.content.team.index', compact('members'));
    }

    public function teamCreate()
    {
        return view('admin.content.team.create');
    }

    public function teamStore(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'bio' => 'nullable|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'social_links' => 'nullable|array',
            'order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('team', 'public');
        }

        TeamMember::create($data);

        return redirect()->route('admin.content.team.index')
            ->with('success', 'Team member created successfully.');
    }

    public function teamEdit(TeamMember $member)
    {
        return view('admin.content.team.edit', compact('member'));
    }

    public function teamUpdate(Request $request, TeamMember $member)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'bio' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'social_links' => 'nullable|array',
            'order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            if ($member->image) {
                Storage::disk('public')->delete($member->image);
            }
            $data['image'] = $request->file('image')->store('team', 'public');
        }

        $member->update($data);

        return redirect()->route('admin.content.team.index')
            ->with('success', 'Team member updated successfully.');
    }

    public function teamDestroy(TeamMember $member)
    {
        if ($member->image) {
            Storage::disk('public')->delete($member->image);
        }

        $member->delete();

        return redirect()->route('admin.content.team.index')
            ->with('success', 'Team member deleted successfully.');
    }

    // Testimonials Management
    public function testimonialsIndex()
    {
        $testimonials = Testimonial::orderBy('order')->paginate(10);
        return view('admin.content.testimonials.index', compact('testimonials'));
    }

    public function testimonialsCreate()
    {
        return view('admin.content.testimonials.create');
    }

    public function testimonialsStore(Request $request)
    {
        $request->validate([
            'client_name' => 'required|string|max:255',
            'client_position' => 'nullable|string|max:255',
            'client_company' => 'nullable|string|max:255',
            'testimonial' => 'required|string',
            'client_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'rating' => 'required|integer|min:1|max:5',
            'order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        if ($request->hasFile('client_image')) {
            $data['client_image'] = $request->file('client_image')->store('testimonials', 'public');
        }

        Testimonial::create($data);

        return redirect()->route('admin.content.testimonials.index')
            ->with('success', 'Testimonial created successfully.');
    }

    public function testimonialsEdit(Testimonial $testimonial)
    {
        return view('admin.content.testimonials.edit', compact('testimonial'));
    }

    public function testimonialsUpdate(Request $request, Testimonial $testimonial)
    {
        $request->validate([
            'client_name' => 'required|string|max:255',
            'client_position' => 'nullable|string|max:255',
            'client_company' => 'nullable|string|max:255',
            'testimonial' => 'required|string',
            'client_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'rating' => 'required|integer|min:1|max:5',
            'order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();

        if ($request->hasFile('client_image')) {
            if ($testimonial->client_image) {
                Storage::disk('public')->delete($testimonial->client_image);
            }
            $data['client_image'] = $request->file('client_image')->store('testimonials', 'public');
        }

        $testimonial->update($data);

        return redirect()->route('admin.content.testimonials.index')
            ->with('success', 'Testimonial updated successfully.');
    }

    public function testimonialsDestroy(Testimonial $testimonial)
    {
        if ($testimonial->client_image) {
            Storage::disk('public')->delete($testimonial->client_image);
        }

        $testimonial->delete();

        return redirect()->route('admin.content.testimonials.index')
            ->with('success', 'Testimonial deleted successfully.');
    }

    /**
     * Helper method to handle image upload with resizing
     */
    private function handleImageUpload($file, $directory, $maxWidth = 1200, $maxHeight = 800)
    {
        $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
        $path = $directory . '/' . $filename;

        // Create directory if it doesn't exist
        $fullPath = storage_path('app/public/' . $directory);
        if (!file_exists($fullPath)) {
            mkdir($fullPath, 0755, true);
        }

        // For now, we'll use the basic Laravel file storage
        // In a real implementation, you would use Intervention Image for resizing
        $storedPath = $file->store($directory, 'public');

        return $storedPath;
    }

    /**
     * Helper method to delete old image
     */
    private function deleteOldImage($imagePath)
    {
        if ($imagePath && Storage::disk('public')->exists($imagePath)) {
            Storage::disk('public')->delete($imagePath);
        }
    }

    /**
     * Bulk actions for content management
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'type' => 'required|in:carousel,services,team,testimonials',
            'ids' => 'required|array',
            'ids.*' => 'integer',
        ]);

        $action = $request->action;
        $type = $request->type;
        $ids = $request->ids;

        $model = $this->getModelByType($type);
        $items = $model::whereIn('id', $ids);

        switch ($action) {
            case 'activate':
                $items->update(['is_active' => true]);
                $message = ucfirst($type) . ' items activated successfully.';
                break;
            case 'deactivate':
                $items->update(['is_active' => false]);
                $message = ucfirst($type) . ' items deactivated successfully.';
                break;
            case 'delete':
                // Delete associated images before deleting records
                foreach ($items->get() as $item) {
                    $this->deleteItemImages($item, $type);
                }
                $items->delete();
                $message = ucfirst($type) . ' items deleted successfully.';
                break;
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Get model class by type
     */
    private function getModelByType($type)
    {
        $models = [
            'carousel' => CarouselSlide::class,
            'services' => Service::class,
            'team' => TeamMember::class,
            'testimonials' => Testimonial::class,
        ];

        return $models[$type] ?? null;
    }

    /**
     * Delete images associated with an item
     */
    private function deleteItemImages($item, $type)
    {
        switch ($type) {
            case 'carousel':
                $this->deleteOldImage($item->image);
                break;
            case 'services':
                $this->deleteOldImage($item->image);
                break;
            case 'team':
                $this->deleteOldImage($item->image);
                break;
            case 'testimonials':
                $this->deleteOldImage($item->client_image);
                break;
        }
    }

    /**
     * Reorder items
     */
    public function reorder(Request $request)
    {
        $request->validate([
            'type' => 'required|in:carousel,services,team,testimonials',
            'items' => 'required|array',
            'items.*.id' => 'required|integer',
            'items.*.order' => 'required|integer',
        ]);

        $type = $request->type;
        $model = $this->getModelByType($type);

        foreach ($request->items as $item) {
            $model::where('id', $item['id'])->update(['order' => $item['order']]);
        }

        return response()->json(['success' => true, 'message' => 'Order updated successfully.']);
    }
}
