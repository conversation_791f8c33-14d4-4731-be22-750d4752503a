<?php $__env->startSection('page-title', 'Overview'); ?>

<?php $__env->startSection('content'); ?>
<div class="dashboard-content-wrapper">
<!-- Dashboard Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 bg-gradient-primary text-white" style="background: linear-gradient(135deg, var(--medical-green), var(--medical-blue)) !important;">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h2 class="mb-2">Welcome back, <?php echo e(Auth::user()->name); ?>!</h2>
                        <p class="mb-0 opacity-75">Continue your medical education journey with Virtual CME Hub</p>
                    </div>
                    <div class="col-lg-4 text-end">
                        <div class="d-flex align-items-center justify-content-end">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <span><?php echo e(date('l, F j, Y')); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Statistics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-video text-primary fa-2x"></i>
                    </div>
                </div>
                <h3 class="text-primary mb-1"><?php echo e($stats['sessions_attended']); ?></h3>
                <p class="text-muted mb-0">Live Sessions Attended</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-success bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-graduation-cap text-success fa-2x"></i>
                    </div>
                </div>
                <h3 class="text-success mb-1"><?php echo e($stats['courses_completed']); ?></h3>
                <p class="text-muted mb-0">Courses Completed</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-certificate text-warning fa-2x"></i>
                    </div>
                </div>
                <h3 class="text-warning mb-1"><?php echo e($stats['cpd_credits']); ?></h3>
                <p class="text-muted mb-0">CPD Credits Earned</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-info bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-clock text-info fa-2x"></i>
                    </div>
                </div>
                <h3 class="text-info mb-1"><?php echo e($stats['learning_hours']); ?>h</h3>
                <p class="text-muted mb-0">Learning Hours</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0"><i class="fas fa-bolt me-2 text-primary"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?php echo e(route('dashboard.live-sessions.index')); ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-video fa-2x mb-2"></i>
                            <span>Live Sessions</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?php echo e(route('dashboard.courses.index')); ?>" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-book-open fa-2x mb-2"></i>
                            <span>Browse Courses</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?php echo e(route('dashboard.certificates.index')); ?>" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-certificate fa-2x mb-2"></i>
                            <span>View Certificates</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?php echo e(route('dashboard.certificates.cpd')); ?>" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <span>CPD Credits</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?php echo e(route('profile.edit')); ?>" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-user-cog fa-2x mb-2"></i>
                            <span>Profile Settings</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?php echo e(route('dashboard.support.help')); ?>" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="fas fa-headset fa-2x mb-2"></i>
                            <span>Get Support</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Dashboard Content -->
<div class="row">
    <!-- Learning Progress -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0"><i class="fas fa-graduation-cap me-2 text-primary"></i>Current Learning Path</h5>
            </div>
            <div class="card-body">
                <?php if($currentCourses->count() > 0): ?>
                    <div class="row">
                        <?php $__currentLoopData = $currentCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $enrollment = $course->enrollments->first();
                                $progress = $enrollment ? $enrollment->progress_percentage : 0;
                                $status = $enrollment ? $enrollment->status : 'enrolled';

                                $borderColor = 'border-primary';
                                $progressColor = 'bg-primary';
                                $buttonColor = 'btn-primary';
                                $buttonText = 'Continue';

                                if ($status === 'completed') {
                                    $borderColor = 'border-success';
                                    $progressColor = 'bg-success';
                                    $buttonColor = 'btn-success';
                                    $buttonText = 'Certificate';
                                } elseif ($progress === 0) {
                                    $borderColor = 'border-warning';
                                    $progressColor = 'bg-warning';
                                    $buttonColor = 'btn-warning';
                                    $buttonText = 'Start';
                                }
                            ?>
                            <div class="col-md-4 mb-3">
                                <div class="card border <?php echo e($borderColor); ?>">
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            <?php if($course->category === 'cardiology'): ?>
                                                <i class="fas fa-heart text-danger fa-3x"></i>
                                            <?php elseif($course->category === 'neurology'): ?>
                                                <i class="fas fa-brain text-info fa-3x"></i>
                                            <?php elseif($course->category === 'pediatrics'): ?>
                                                <i class="fas fa-child text-warning fa-3x"></i>
                                            <?php elseif($course->category === 'emergency'): ?>
                                                <i class="fas fa-ambulance text-danger fa-3x"></i>
                                            <?php else: ?>
                                                <i class="fas fa-graduation-cap text-primary fa-3x"></i>
                                            <?php endif; ?>
                                        </div>
                                        <h6><?php echo e($course->title); ?></h6>
                                        <p class="text-muted small mb-3"><?php echo e(Str::limit($course->description, 50)); ?></p>
                                        <div class="progress mb-3" style="height: 8px;">
                                            <div class="progress-bar <?php echo e($progressColor); ?>" style="width: <?php echo e($progress); ?>%"></div>
                                        </div>
                                        <?php if($status === 'completed'): ?>
                                            <small class="text-success">Completed</small>
                                        <?php else: ?>
                                            <small class="text-muted"><?php echo e($progress); ?>% Complete</small>
                                        <?php endif; ?>
                                        <br>
                                        <a href="<?php echo e(route('dashboard.courses.show', $course)); ?>" class="btn <?php echo e($buttonColor); ?> btn-sm mt-2"><?php echo e($buttonText); ?></a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Active Courses</h5>
                        <p class="text-muted">Start your learning journey by enrolling in a course.</p>
                        <a href="<?php echo e(route('dashboard.courses.index')); ?>" class="btn btn-primary">Browse Courses</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Upcoming Sessions -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2 text-primary"></i>Upcoming Live Sessions</h5>
            </div>
            <div class="card-body">
                <?php if($upcomingSessions->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Session</th>
                                    <th>Instructor</th>
                                    <th>Date & Time</th>
                                    <th>CPD Credits</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $upcomingSessions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $session): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $isRegistered = $session->registrations->count() > 0;
                                        $iconClass = 'fas fa-video';
                                        $iconColor = 'text-primary';

                                        if ($session->category === 'cardiology') {
                                            $iconClass = 'fas fa-heart';
                                            $iconColor = 'text-danger';
                                        } elseif ($session->category === 'neurology') {
                                            $iconClass = 'fas fa-brain';
                                            $iconColor = 'text-info';
                                        } elseif ($session->category === 'pediatrics') {
                                            $iconClass = 'fas fa-child';
                                            $iconColor = 'text-warning';
                                        } elseif ($session->category === 'emergency') {
                                            $iconClass = 'fas fa-ambulance';
                                            $iconColor = 'text-danger';
                                        }
                                    ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                                    <i class="<?php echo e($iconClass); ?> <?php echo e($iconColor); ?>"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-semibold"><?php echo e($session->title); ?></div>
                                                    <small class="text-muted"><?php echo e(Str::limit($session->description, 50)); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="fw-semibold"><?php echo e($session->instructor); ?></div>
                                                <small class="text-muted"><?php echo e($session->instructor_title); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="fw-semibold"><?php echo e($session->session_date->format('M j, Y')); ?></div>
                                                <small class="text-muted"><?php echo e($session->session_date->format('g:i A T')); ?></small>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-primary"><?php echo e($session->cpd_credits); ?> Credits</span></td>
                                        <td>
                                            <?php if($isRegistered): ?>
                                                <a href="<?php echo e($session->meeting_url); ?>" class="btn btn-sm btn-success" target="_blank">Join Session</a>
                                            <?php else: ?>
                                                <form action="<?php echo e(route('dashboard.live-sessions.register', $session)); ?>" method="POST" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-primary">Register</button>
                                                </form>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Upcoming Sessions</h5>
                        <p class="text-muted">Check back later for new live sessions.</p>
                        <a href="<?php echo e(route('dashboard.live-sessions.index')); ?>" class="btn btn-primary">View All Sessions</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Recent Activities -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0"><i class="fas fa-clock me-2 text-primary"></i>Recent Activities</h5>
            </div>
            <div class="card-body">
                <?php if($recentActivities->count() > 0): ?>
                    <?php $__currentLoopData = $recentActivities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex align-items-start <?php echo e(!$loop->last ? 'mb-3' : ''); ?>">
                            <div class="bg-<?php echo e($activity['color']); ?> bg-opacity-10 rounded-circle p-2 me-3">
                                <i class="<?php echo e($activity['icon']); ?> text-<?php echo e($activity['color']); ?>"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-semibold"><?php echo e($activity['title']); ?></div>
                                <small class="text-muted"><?php echo e($activity['description']); ?></small>
                                <br>
                                <small class="text-muted"><?php echo e($activity['time']->diffForHumans()); ?></small>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No Recent Activities</h6>
                        <p class="text-muted small">Your recent learning activities will appear here.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-0">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2 text-primary"></i>Learning Progress</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-semibold">Overall Progress</span>
                        <span class="text-primary fw-bold"><?php echo e($progressData['overall_progress']); ?>%</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-primary" style="width: <?php echo e($progressData['overall_progress']); ?>%"></div>
                    </div>
                </div>

                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-semibold">CPD Credits</span>
                        <span class="text-success fw-bold"><?php echo e($stats['cpd_credits']); ?>/60</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-success" style="width: <?php echo e($progressData['cpd_progress']); ?>%"></div>
                    </div>
                </div>

                <div class="mb-0">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-semibold">Learning Hours</span>
                        <span class="text-info fw-bold"><?php echo e($stats['learning_hours']); ?>h</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-info" style="width: <?php echo e($progressData['learning_hours_progress']); ?>%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\VCH\resources\views/dashboard.blade.php ENDPATH**/ ?>