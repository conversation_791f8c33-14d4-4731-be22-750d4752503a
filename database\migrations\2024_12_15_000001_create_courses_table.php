<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->text('content')->nullable();
            $table->string('instructor');
            $table->string('instructor_title')->nullable();
            $table->integer('duration_hours');
            $table->integer('cpd_credits');
            $table->string('category');
            $table->string('level')->default('intermediate'); // beginner, intermediate, advanced
            $table->string('status')->default('active'); // active, inactive, draft
            $table->string('image')->nullable();
            $table->decimal('price', 8, 2)->default(0);
            $table->json('learning_objectives')->nullable();
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('courses');
    }
};
