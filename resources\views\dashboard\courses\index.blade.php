@extends('layouts.dashboard')

@section('page-title', 'My Courses')

@section('content')
<div class="dashboard-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">My Courses</h2>
                    <p class="text-muted mb-0">Track your learning progress and access course materials</p>
                </div>
                <div>
                    <a href="{{ route('dashboard.support.contact') }}" class="btn btn-outline-primary">
                        <i class="fas fa-headset me-2"></i>Need Help?
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-book-open text-primary fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-primary mb-1">{{ $stats['enrolled_count'] }}</h3>
                    <p class="text-muted mb-0">Enrolled Courses</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-check-circle text-success fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-success mb-1">{{ $stats['completed_count'] }}</h3>
                    <p class="text-muted mb-0">Completed Courses</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-play text-warning fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-warning mb-1">{{ $stats['in_progress_count'] }}</h3>
                    <p class="text-muted mb-0">In Progress</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-certificate text-info fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-info mb-1">{{ $stats['total_credits'] }}</h3>
                    <p class="text-muted mb-0">CPD Credits Earned</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <!-- Filter Tabs -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="btn-group flex-wrap" role="group">
                                <a href="{{ route('dashboard.courses.index', ['filter' => 'all']) }}"
                                   class="btn {{ $filter === 'all' ? 'btn-primary' : 'btn-outline-primary' }}">
                                    <i class="fas fa-list me-1"></i>All Courses
                                </a>
                                <a href="{{ route('dashboard.courses.index', ['filter' => 'enrolled']) }}"
                                   class="btn {{ $filter === 'enrolled' ? 'btn-info' : 'btn-outline-info' }}">
                                    <i class="fas fa-user-check me-1"></i>My Courses
                                </a>
                                <a href="{{ route('dashboard.courses.index', ['filter' => 'in_progress']) }}"
                                   class="btn {{ $filter === 'in_progress' ? 'btn-warning' : 'btn-outline-warning' }}">
                                    <i class="fas fa-play me-1"></i>In Progress
                                </a>
                                <a href="{{ route('dashboard.courses.index', ['filter' => 'completed']) }}"
                                   class="btn {{ $filter === 'completed' ? 'btn-success' : 'btn-outline-success' }}">
                                    <i class="fas fa-check-circle me-1"></i>Completed
                                </a>
                                <a href="{{ route('dashboard.courses.index', ['filter' => 'available']) }}"
                                   class="btn {{ $filter === 'available' ? 'btn-secondary' : 'btn-outline-secondary' }}">
                                    <i class="fas fa-plus-circle me-1"></i>Available
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Search and Filters -->
                    <div class="row">
                        <div class="col-12">
                            <form method="GET" action="{{ route('dashboard.courses.index') }}" class="row g-3">
                                <input type="hidden" name="filter" value="{{ $filter }}">

                                <div class="col-md-4">
                                    <label for="search" class="form-label">Search Courses</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" id="search" name="search"
                                               value="{{ $search }}" placeholder="Search by title, instructor...">
                                    </div>
                                </div>

                                <div class="col-md-2">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">All Categories</option>
                                        @foreach($categories as $cat)
                                            <option value="{{ $cat }}" {{ $category === $cat ? 'selected' : '' }}>
                                                {{ ucfirst($cat) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label for="level" class="form-label">Level</label>
                                    <select class="form-select" id="level" name="level">
                                        <option value="">All Levels</option>
                                        @foreach($levels as $lvl)
                                            <option value="{{ $lvl }}" {{ $level === $lvl ? 'selected' : '' }}>
                                                {{ ucfirst($lvl) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label for="sort" class="form-label">Sort By</label>
                                    <select class="form-select" id="sort" name="sort">
                                        <option value="title" {{ $sort === 'title' ? 'selected' : '' }}>Title</option>
                                        <option value="newest" {{ $sort === 'newest' ? 'selected' : '' }}>Newest</option>
                                        <option value="duration" {{ $sort === 'duration' ? 'selected' : '' }}>Duration</option>
                                        <option value="credits" {{ $sort === 'credits' ? 'selected' : '' }}>CPD Credits</option>
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-1"></i>Filter
                                        </button>
                                        <a href="{{ route('dashboard.courses.index', ['filter' => $filter]) }}"
                                           class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <span class="text-muted">
                                <i class="fas fa-filter me-1"></i>
                                Showing {{ $courses->count() }} of {{ $courses->total() }} courses
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Courses Grid -->
    <div class="row">
        @forelse($courses as $course)
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h5 class="card-title mb-2">{{ $course->title }}</h5>
                            <p class="text-muted mb-2">{{ Str::limit($course->description, 100) }}</p>
                        </div>
                        <div class="text-end">
                            @if($course->pivot ?? null)
                                <span class="badge bg-{{ $course->pivot->status === 'completed' ? 'success' : ($course->pivot->status === 'in_progress' ? 'warning' : 'primary') }}">
                                    {{ ucfirst(str_replace('_', ' ', $course->pivot->status)) }}
                                </span>
                            @else
                                <span class="badge bg-secondary">Available</span>
                            @endif
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted d-block">Instructor</small>
                            <strong>{{ $course->instructor }}</strong>
                            @if($course->instructor_title)
                                <br><small class="text-muted">{{ $course->instructor_title }}</small>
                            @endif
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">Duration & Credits</small>
                            <strong>{{ $course->duration_hours }}h • {{ $course->cpd_credits }} CPD</strong>
                        </div>
                    </div>

                    @if($course->pivot ?? null)
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted">Progress</small>
                            <small class="text-muted">{{ $course->pivot->progress_percentage ?? 0 }}%</small>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-primary" style="width: {{ $course->pivot->progress_percentage ?? 0 }}%"></div>
                        </div>
                    </div>
                    @endif

                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted d-block">Category</small>
                            <strong>{{ $course->category }}</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted d-block">Level</small>
                            <strong>{{ ucfirst($course->level) }}</strong>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            @if($course->pivot ?? null)
                                @if($course->pivot->enrolled_at)
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>Enrolled: {{ \Carbon\Carbon::parse($course->pivot->enrolled_at)->format('M j, Y') }}
                                    </small>
                                @endif
                            @else
                                <small class="text-success">
                                    <i class="fas fa-dollar-sign me-1"></i>${{ number_format($course->price, 2) }}
                                </small>
                            @endif
                        </div>
                        
                        <div class="btn-group">
                            <a href="{{ route('dashboard.courses.show', $course) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                            
                            @if($course->pivot ?? null)
                                @if($course->pivot->status === 'completed')
                                    <a href="{{ route('dashboard.certificates.index', ['course' => $course->id]) }}" class="btn btn-success btn-sm">
                                        <i class="fas fa-certificate me-1"></i>Certificate
                                    </a>
                                @else
                                    <a href="{{ route('dashboard.courses.continue', $course) }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-play me-1"></i>Continue
                                    </a>
                                @endif
                            @else
                                <form action="{{ route('dashboard.courses.enroll', $course) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus me-1"></i>Enroll
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @empty
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No courses found</h4>
                    <p class="text-muted mb-4">
                        @if($filter === 'in_progress')
                            You don't have any courses in progress at the moment.
                        @elseif($filter === 'completed')
                            You haven't completed any courses yet.
                        @elseif($filter === 'available')
                            No new courses are available for enrollment right now.
                        @else
                            You haven't enrolled in any courses yet. Browse our course catalog to get started.
                        @endif
                    </p>
                    @if($filter !== 'available')
                        <a href="{{ route('dashboard.courses.index', ['filter' => 'available']) }}" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Browse Available Courses
                        </a>
                    @endif
                </div>
            </div>
        </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($courses->hasPages())
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-center">
                {{ $courses->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add course-specific JavaScript here
    console.log('Courses page loaded');
});
</script>
@endpush
