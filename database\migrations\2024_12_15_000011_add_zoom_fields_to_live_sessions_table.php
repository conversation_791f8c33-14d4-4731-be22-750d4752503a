<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('live_sessions', function (Blueprint $table) {
            // Add slug field if it doesn't exist (without unique constraint first)
            if (!Schema::hasColumn('live_sessions', 'slug')) {
                $table->string('slug')->nullable()->after('title');
            }
        });

        // Populate slug for existing records
        $sessions = DB::table('live_sessions')->whereNull('slug')->orWhere('slug', '')->get();
        foreach ($sessions as $session) {
            $slug = \Illuminate\Support\Str::slug($session->title . '-' . $session->id);
            DB::table('live_sessions')->where('id', $session->id)->update(['slug' => $slug]);
        }

        // Now add the unique constraint and other fields
        Schema::table('live_sessions', function (Blueprint $table) {
            // Make slug unique now that all records have values
            if (Schema::hasColumn('live_sessions', 'slug')) {
                $table->string('slug')->unique()->change();
            }
            
            // Add level field if it doesn't exist
            if (!Schema::hasColumn('live_sessions', 'level')) {
                $table->string('level')->nullable()->after('category');
            }
            
            // Add pricing fields if they don't exist
            if (!Schema::hasColumn('live_sessions', 'is_free')) {
                $table->boolean('is_free')->default(true)->after('cpd_credits');
            }
            
            if (!Schema::hasColumn('live_sessions', 'price')) {
                $table->decimal('price', 8, 2)->nullable()->after('is_free');
            }
            
            // Add registration and meeting settings
            if (!Schema::hasColumn('live_sessions', 'registration_required')) {
                $table->boolean('registration_required')->default(false)->after('price');
            }
            
            if (!Schema::hasColumn('live_sessions', 'auto_record')) {
                $table->boolean('auto_record')->default(false)->after('registration_required');
            }
            
            if (!Schema::hasColumn('live_sessions', 'waiting_room')) {
                $table->boolean('waiting_room')->default(false)->after('auto_record');
            }
            
            if (!Schema::hasColumn('live_sessions', 'join_before_host')) {
                $table->boolean('join_before_host')->default(false)->after('waiting_room');
            }
            
            if (!Schema::hasColumn('live_sessions', 'mute_participants')) {
                $table->boolean('mute_participants')->default(true)->after('join_before_host');
            }
            
            // Add Zoom-specific fields
            if (!Schema::hasColumn('live_sessions', 'zoom_meeting_id')) {
                $table->string('zoom_meeting_id')->nullable()->after('meeting_password');
            }
            
            if (!Schema::hasColumn('live_sessions', 'zoom_join_url')) {
                $table->text('zoom_join_url')->nullable()->after('zoom_meeting_id');
            }
            
            if (!Schema::hasColumn('live_sessions', 'zoom_start_url')) {
                $table->text('zoom_start_url')->nullable()->after('zoom_join_url');
            }
            
            if (!Schema::hasColumn('live_sessions', 'zoom_password')) {
                $table->string('zoom_password')->nullable()->after('zoom_start_url');
            }
            
            if (!Schema::hasColumn('live_sessions', 'zoom_webinar_id')) {
                $table->string('zoom_webinar_id')->nullable()->after('zoom_password');
            }
            
            // Skip adding indexes to avoid conflicts - they can be added manually if needed
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('live_sessions', function (Blueprint $table) {
            // Drop indexes first (with try-catch to handle non-existent indexes)
            try {
                $table->dropIndex(['slug']);
            } catch (\Exception $e) {
                // Index doesn't exist, skip
            }

            try {
                $table->dropIndex(['level']);
            } catch (\Exception $e) {
                // Index doesn't exist, skip
            }

            try {
                $table->dropIndex(['is_free']);
            } catch (\Exception $e) {
                // Index doesn't exist, skip
            }

            try {
                $table->dropIndex(['registration_required']);
            } catch (\Exception $e) {
                // Index doesn't exist, skip
            }

            try {
                $table->dropIndex(['zoom_meeting_id']);
            } catch (\Exception $e) {
                // Index doesn't exist, skip
            }

            // Drop columns
            $columns = [
                'slug',
                'level',
                'is_free',
                'price',
                'registration_required',
                'auto_record',
                'waiting_room',
                'join_before_host',
                'mute_participants',
                'zoom_meeting_id',
                'zoom_join_url',
                'zoom_start_url',
                'zoom_password',
                'zoom_webinar_id'
            ];

            foreach ($columns as $column) {
                if (Schema::hasColumn('live_sessions', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
