

<?php $__env->startSection('page-title', 'Services Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Services Management</h2>
                    <p class="text-muted mb-0">Manage educational services offered on the platform</p>
                </div>
                <div>
                    <a href="<?php echo e(route('admin.content.services.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add New Service
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters & Search -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form action="<?php echo e(route('admin.content.services.index')); ?>" method="GET" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text bg-light border-0"><i class="fas fa-search text-muted"></i></span>
                        <input type="text" class="form-control bg-light border-0" name="search" 
                               placeholder="Search services..." value="<?php echo e(request('search')); ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select bg-light border-0">
                        <option value="">All Status</option>
                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="sort" class="form-select bg-light border-0">
                        <option value="order_asc" <?php echo e(request('sort') == 'order_asc' ? 'selected' : ''); ?>>Order (Low to High)</option>
                        <option value="order_desc" <?php echo e(request('sort') == 'order_desc' ? 'selected' : ''); ?>>Order (High to Low)</option>
                        <option value="title_asc" <?php echo e(request('sort') == 'title_asc' ? 'selected' : ''); ?>>Title (A-Z)</option>
                        <option value="title_desc" <?php echo e(request('sort') == 'title_desc' ? 'selected' : ''); ?>>Title (Z-A)</option>
                        <option value="newest" <?php echo e(request('sort') == 'newest' ? 'selected' : ''); ?>>Newest First</option>
                        <option value="oldest" <?php echo e(request('sort') == 'oldest' ? 'selected' : ''); ?>>Oldest First</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Services List -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input select-all" type="checkbox" id="selectAll">
                                    <label class="form-check-label" for="selectAll"></label>
                                </div>
                            </th>
                            <th width="60">Order</th>
                            <th width="80">Icon</th>
                            <th>Title</th>
                            <th>Description</th>
                            <th width="100">Status</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="sortable" data-url="<?php echo e(route('admin.content.reorder', ['type' => 'services'])); ?>">
                        <?php $__empty_1 = true; $__currentLoopData = $services ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr data-id="<?php echo e($service->id); ?>">
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input select-item" type="checkbox" 
                                           id="service<?php echo e($service->id); ?>" name="selected_items[]" value="<?php echo e($service->id); ?>">
                                    <label class="form-check-label" for="service<?php echo e($service->id); ?>"></label>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark"><?php echo e($service->order); ?></span>
                                <i class="fas fa-grip-vertical text-muted ms-2 drag-handle"></i>
                            </td>
                            <td>
                                <?php if($service->icon): ?>
                                    <i class="<?php echo e($service->icon); ?> fa-2x text-primary"></i>
                                <?php else: ?>
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-graduation-cap text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <h6 class="mb-0"><?php echo e($service->title); ?></h6>
                            </td>
                            <td>
                                <p class="text-muted mb-0 small"><?php echo e(Str::limit($service->description, 100)); ?></p>
                            </td>
                            <td>
                                <?php if($service->is_active): ?>
                                    <span class="badge bg-success-soft">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger-soft">Inactive</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="d-flex">
                                    <a href="<?php echo e(route('admin.content.services.edit', $service->id)); ?>" class="btn btn-sm btn-outline-primary me-2">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo e(route('admin.content.services.destroy', $service->id)); ?>" method="POST" class="delete-form">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                                    <h5>No Services Found</h5>
                                    <p class="text-muted">Get started by adding your first service</p>
                                    <a href="<?php echo e(route('admin.content.services.create')); ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus me-2"></i>Add New Service
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <?php if(isset($services) && $services->count() > 0): ?>
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form action="<?php echo e(route('admin.content.bulk-action', ['type' => 'services'])); ?>" method="POST" id="bulkActionForm">
                <?php echo csrf_field(); ?>
                <div class="row g-3 align-items-center">
                    <div class="col-auto">
                        <h5 class="mb-0">Bulk Actions</h5>
                    </div>
                    <div class="col-auto">
                        <select name="action" class="form-select" id="bulkAction">
                            <option value="">Select Action</option>
                            <option value="activate">Activate</option>
                            <option value="deactivate">Deactivate</option>
                            <option value="delete">Delete</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-primary" id="applyBulkAction" disabled>
                            Apply
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-center">
        <?php echo e($services->links()); ?>

    </div>
    <?php endif; ?>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Select all functionality
        const selectAll = document.getElementById('selectAll');
        const selectItems = document.querySelectorAll('.select-item');
        const applyBulkAction = document.getElementById('applyBulkAction');
        const bulkAction = document.getElementById('bulkAction');
        
        if (selectAll) {
            selectAll.addEventListener('change', function() {
                selectItems.forEach(item => {
                    item.checked = this.checked;
                });
                updateBulkActionButton();
            });
        }
        
        selectItems.forEach(item => {
            item.addEventListener('change', function() {
                updateBulkActionButton();
                updateSelectAllCheckbox();
            });
        });
        
        bulkAction.addEventListener('change', updateBulkActionButton);
        
        function updateBulkActionButton() {
            const hasCheckedItems = Array.from(selectItems).some(item => item.checked);
            const hasSelectedAction = bulkAction.value !== '';
            applyBulkAction.disabled = !(hasCheckedItems && hasSelectedAction);
        }
        
        function updateSelectAllCheckbox() {
            const allChecked = Array.from(selectItems).every(item => item.checked);
            const someChecked = Array.from(selectItems).some(item => item.checked);
            
            selectAll.checked = allChecked;
            selectAll.indeterminate = someChecked && !allChecked;
        }
        
        // Confirm delete
        document.querySelectorAll('.delete-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                if (confirm('Are you sure you want to delete this service? This action cannot be undone.')) {
                    this.submit();
                }
            });
        });
        
        // Confirm bulk delete
        document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
            const action = document.getElementById('bulkAction').value;
            if (action === 'delete') {
                e.preventDefault();
                if (confirm('Are you sure you want to delete the selected services? This action cannot be undone.')) {
                    this.submit();
                }
            }
        });
        
        // Sortable functionality for drag and drop reordering
        if (typeof Sortable !== 'undefined') {
            const sortableList = document.querySelector('.sortable');
            if (sortableList) {
                new Sortable(sortableList, {
                    handle: '.drag-handle',
                    animation: 150,
                    onEnd: function(evt) {
                        const itemIds = Array.from(sortableList.querySelectorAll('tr[data-id]'))
                            .map((row, index) => {
                                return {
                                    id: row.dataset.id,
                                    order: index
                                };
                            });
                            
                        fetch(sortableList.dataset.url, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify({ items: itemIds })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Update the order numbers displayed in the UI
                                itemIds.forEach(item => {
                                    const row = sortableList.querySelector(`tr[data-id="${item.id}"]`);
                                    const orderBadge = row.querySelector('.badge');
                                    if (orderBadge) {
                                        orderBadge.textContent = item.order;
                                    }
                                });
                            }
                        })
                        .catch(error => console.error('Error updating order:', error));
                    }
                });
            }
        }
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\VCH\resources\views/admin/content/services/index.blade.php ENDPATH**/ ?>