<?php

namespace App\Providers;

use App\Models\User;
use App\Models\CarouselSlide;
use App\Models\Service;
use App\Models\TeamMember;
use App\Models\Testimonial;
use App\Models\Course;
use App\Models\LiveSession;
use App\Policies\UserPolicy;
use App\Policies\ContentPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        User::class => UserPolicy::class,
        CarouselSlide::class => ContentPolicy::class,
        Service::class => ContentPolicy::class,
        TeamMember::class => ContentPolicy::class,
        Testimonial::class => ContentPolicy::class,
        Course::class => ContentPolicy::class,
        LiveSession::class => ContentPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define additional gates
        Gate::define('manage-content', function (User $user) {
            return $user->canManageContent();
        });

        Gate::define('manage-users', function (User $user) {
            return $user->canManageUsers();
        });

        Gate::define('manage-courses', function (User $user) {
            return $user->canManageCourses();
        });

        Gate::define('access-admin', function (User $user) {
            return $user->isAdmin();
        });

        Gate::define('access-instructor-panel', function (User $user) {
            return $user->isInstructor() || $user->isAdmin();
        });

        Gate::define('moderate-content', function (User $user) {
            return $user->isModerator() || $user->isAdmin();
        });
    }
}
