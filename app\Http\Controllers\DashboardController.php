<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\LiveSession;
use App\Models\Certificate;
use App\Models\Enrollment;
use App\Models\SessionRegistration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        // Get user statistics
        $stats = [
            'sessions_attended' => SessionRegistration::where('user_id', $user->id)
                ->where('status', 'attended')
                ->count(),
            'courses_completed' => Enrollment::where('user_id', $user->id)
                ->where('status', 'completed')
                ->count(),
            'cpd_credits' => Certificate::where('user_id', $user->id)
                ->where('status', 'active')
                ->sum('cpd_credits'),
            'learning_hours' => $this->calculateLearningHours($user->id),
        ];

        // Get current learning path (enrolled courses)
        $currentCourses = Course::whereHas('enrollments', function ($query) use ($user) {
            $query->where('user_id', $user->id)
                  ->whereIn('status', ['enrolled', 'in_progress']);
        })->with(['enrollments' => function ($query) use ($user) {
            $query->where('user_id', $user->id);
        }])->take(3)->get();

        // Get upcoming sessions
        $upcomingSessions = LiveSession::upcoming()
            ->with(['registrations' => function ($query) use ($user) {
                $query->where('user_id', $user->id);
            }])
            ->orderBy('session_date')
            ->take(5)
            ->get();

        // Get recent activities (simplified)
        $recentActivities = $this->getRecentActivities($user->id);

        // Calculate progress percentages
        $progressData = [
            'overall_progress' => $this->calculateOverallProgress($user->id),
            'cpd_progress' => min(100, ($stats['cpd_credits'] / 60) * 100), // Assuming 60 credits target
            'learning_hours_progress' => min(100, ($stats['learning_hours'] / 40) * 100), // Assuming 40 hours target
        ];

        return view('dashboard', compact(
            'stats',
            'currentCourses',
            'upcomingSessions',
            'recentActivities',
            'progressData'
        ));
    }

    private function calculateLearningHours($userId)
    {
        $courseHours = Enrollment::where('user_id', $userId)
            ->where('status', 'completed')
            ->with('course')
            ->get()
            ->sum(function ($enrollment) {
                return $enrollment->course->duration_hours;
            });

        $sessionHours = SessionRegistration::where('user_id', $userId)
            ->where('status', 'attended')
            ->with('liveSession')
            ->get()
            ->sum(function ($registration) {
                return $registration->liveSession->duration_minutes / 60;
            });

        return round($courseHours + $sessionHours, 1);
    }

    private function calculateOverallProgress($userId)
    {
        $enrollments = Enrollment::where('user_id', $userId)->get();

        if ($enrollments->isEmpty()) {
            return 0;
        }

        $totalProgress = $enrollments->sum('progress_percentage');
        return round($totalProgress / $enrollments->count());
    }

    private function getRecentActivities($userId)
    {
        $activities = collect();

        // Recent course completions
        $recentCompletions = Enrollment::where('user_id', $userId)
            ->where('status', 'completed')
            ->with('course')
            ->orderBy('completed_at', 'desc')
            ->take(2)
            ->get();

        foreach ($recentCompletions as $completion) {
            $activities->push([
                'type' => 'course_completed',
                'title' => 'Course completed',
                'description' => $completion->course->title . ' - ' . $completion->course->cpd_credits . ' CPD credits earned',
                'time' => $completion->completed_at,
                'icon' => 'fas fa-check',
                'color' => 'success'
            ]);
        }

        // Recent session attendance
        $recentSessions = SessionRegistration::where('user_id', $userId)
            ->where('status', 'attended')
            ->with('liveSession')
            ->orderBy('attended_at', 'desc')
            ->take(2)
            ->get();

        foreach ($recentSessions as $session) {
            $activities->push([
                'type' => 'session_attended',
                'title' => 'Live session attended',
                'description' => $session->liveSession->title,
                'time' => $session->attended_at,
                'icon' => 'fas fa-video',
                'color' => 'primary'
            ]);
        }

        // Recent certificates
        $recentCertificates = Certificate::where('user_id', $userId)
            ->orderBy('issued_at', 'desc')
            ->take(2)
            ->get();

        foreach ($recentCertificates as $certificate) {
            $activities->push([
                'type' => 'certificate_earned',
                'title' => 'Certificate earned',
                'description' => $certificate->title,
                'time' => $certificate->issued_at,
                'icon' => 'fas fa-certificate',
                'color' => 'warning'
            ]);
        }

        return $activities->sortByDesc('time')->take(4)->values();
    }

    public function billing()
    {
        $user = Auth::user();

        // Mock billing data - in a real app, this would come from a payment provider
        $stats = [
            'current_plan' => 'Professional',
            'monthly_cost' => 29.99,
            'next_billing_date' => now()->addMonth(),
            'total_spent' => 359.88,
        ];

        $invoices = [
            [
                'id' => 'INV-2024-001',
                'date' => now()->subMonth(),
                'amount' => 29.99,
                'status' => 'paid',
                'description' => 'Professional Plan - Monthly Subscription',
            ],
            [
                'id' => 'INV-2024-002',
                'date' => now()->subMonths(2),
                'amount' => 29.99,
                'status' => 'paid',
                'description' => 'Professional Plan - Monthly Subscription',
            ],
            [
                'id' => 'INV-2024-003',
                'date' => now()->subMonths(3),
                'amount' => 29.99,
                'status' => 'paid',
                'description' => 'Professional Plan - Monthly Subscription',
            ],
        ];

        $paymentMethods = [
            [
                'id' => 1,
                'type' => 'card',
                'last_four' => '4242',
                'brand' => 'Visa',
                'expires' => '12/26',
                'is_default' => true,
            ]
        ];

        return view('dashboard.billing', compact('stats', 'invoices', 'paymentMethods'));
    }
}
