<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPD Credits Report - {{ $year }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            color: #333;
        }
        
        .report-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2E8B57;
            padding-bottom: 20px;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #2E8B57, #4A90E2);
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        .report-title {
            font-size: 28px;
            color: #2E8B57;
            margin: 0 0 10px 0;
            font-weight: bold;
        }
        
        .report-subtitle {
            font-size: 16px;
            color: #666;
            margin: 0;
        }
        
        .participant-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .participant-info h3 {
            margin: 0 0 15px 0;
            color: #2E8B57;
            font-size: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .info-label {
            font-weight: bold;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #2E8B57, #4A90E2);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .certificates-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .certificates-table th,
        .certificates-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .certificates-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2E8B57;
            border-bottom: 2px solid #2E8B57;
        }
        
        .certificates-table tr:hover {
            background: #f8f9fa;
        }
        
        .certificate-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .type-course {
            background: #d4edda;
            color: #155724;
        }
        
        .type-session {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .credits-badge {
            background: #2E8B57;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #eee;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        
        .generated-date {
            margin-bottom: 10px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .report-container {
                box-shadow: none;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="header">
            <div class="logo">VCH</div>
            <h1 class="report-title">CPD Credits Report</h1>
            <p class="report-subtitle">Continuing Professional Development - {{ $year }}</p>
        </div>
        
        <div class="participant-info">
            <h3>Participant Information</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">Name:</span>
                    <span class="info-value">{{ $user->name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Email:</span>
                    <span class="info-value">{{ $user->email }}</span>
                </div>
                @if($user->license_number)
                <div class="info-item">
                    <span class="info-label">License Number:</span>
                    <span class="info-value">{{ $user->license_number }}</span>
                </div>
                @endif
                @if($user->profession)
                <div class="info-item">
                    <span class="info-label">Profession:</span>
                    <span class="info-value">{{ $user->profession }}</span>
                </div>
                @endif
            </div>
        </div>
        
        <div class="summary-stats">
            <div class="stat-card">
                <div class="stat-number">{{ $totalCredits }}</div>
                <div class="stat-label">Total CPD Credits</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $certificates->count() }}</div>
                <div class="stat-label">Certificates Earned</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $certificates->where('certificate_type', 'course')->count() }}</div>
                <div class="stat-label">Course Certificates</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ $certificates->where('certificate_type', 'session')->count() }}</div>
                <div class="stat-label">Session Certificates</div>
            </div>
        </div>
        
        @if($certificates->count() > 0)
        <table class="certificates-table">
            <thead>
                <tr>
                    <th>Certificate</th>
                    <th>Type</th>
                    <th>Date Earned</th>
                    <th>Instructor</th>
                    <th>CPD Credits</th>
                </tr>
            </thead>
            <tbody>
                @foreach($certificates as $certificate)
                <tr>
                    <td>
                        <strong>{{ $certificate->title }}</strong>
                        <br>
                        <small style="color: #666;">{{ $certificate->certificate_number }}</small>
                    </td>
                    <td>
                        <span class="certificate-type type-{{ $certificate->certificate_type }}">
                            {{ ucfirst($certificate->certificate_type) }}
                        </span>
                    </td>
                    <td>{{ $certificate->issued_at->format('M j, Y') }}</td>
                    <td>
                        @if($certificate->course)
                            {{ $certificate->course->instructor }}
                        @elseif($certificate->liveSession)
                            {{ $certificate->liveSession->instructor }}
                        @else
                            -
                        @endif
                    </td>
                    <td>
                        <span class="credits-badge">{{ $certificate->cpd_credits }}</span>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
        @else
        <div style="text-align: center; padding: 40px; color: #666;">
            <h4>No certificates earned in {{ $year }}</h4>
            <p>Continue your professional development by enrolling in courses and attending live sessions.</p>
        </div>
        @endif
        
        <div class="footer">
            <div class="generated-date">
                Report generated on {{ now()->format('F j, Y \a\t g:i A') }}
            </div>
            <div>
                Virtual CME Hub - Continuing Medical Education Platform<br>
                For verification of certificates, visit: {{ url('/') }}
            </div>
        </div>
    </div>
</body>
</html>
