<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Course;
use App\Models\LiveSession;
use App\Models\CarouselSlide;
use App\Models\Service;
use App\Models\TeamMember;
use App\Models\Testimonial;
use App\Models\FAQ;
use App\Models\User;
use App\Models\Enrollment;
use App\Models\SessionRegistration;
use App\Models\Certificate;
use App\Models\Notification;

class VCHSeeder extends Seeder
{
    public function run(): void
    {
        // Create sample courses
        $courses = [
            [
                'title' => 'Advanced Cardiology',
                'description' => 'Comprehensive course covering latest developments in cardiac interventions and treatment protocols.',
                'content' => 'This course covers advanced cardiac procedures, diagnostic techniques, and treatment protocols.',
                'instructor' => 'Dr. <PERSON>',
                'instructor_title' => 'Cardiologist, MD',
                'duration_hours' => 8,
                'cpd_credits' => 8,
                'category' => 'Cardiology',
                'level' => 'advanced',
                'status' => 'active',
                'price' => 299.99,
                'learning_objectives' => [
                    'Understand advanced cardiac diagnostic techniques',
                    'Master interventional cardiology procedures',
                    'Apply evidence-based treatment protocols'
                ],
                'start_date' => now()->addDays(7),
                'end_date' => now()->addDays(37),
            ],
            [
                'title' => 'Neurology Updates',
                'description' => 'Latest developments in neurological disorders and treatment approaches.',
                'content' => 'Explore cutting-edge research in neurology and its clinical applications.',
                'instructor' => 'Dr. Michael Chen',
                'instructor_title' => 'Neurologist, MD, PhD',
                'duration_hours' => 6,
                'cpd_credits' => 6,
                'category' => 'Neurology',
                'level' => 'intermediate',
                'status' => 'active',
                'price' => 249.99,
                'learning_objectives' => [
                    'Identify latest neurological treatment options',
                    'Understand neuroplasticity principles',
                    'Apply new diagnostic criteria'
                ],
                'start_date' => now()->addDays(14),
                'end_date' => now()->addDays(44),
            ],
            [
                'title' => 'Pediatric Emergency Care',
                'description' => 'Critical care protocols and emergency procedures for pediatric patients.',
                'content' => 'Essential skills for managing pediatric emergencies in clinical settings.',
                'instructor' => 'Dr. Emily Rodriguez',
                'instructor_title' => 'Pediatric Emergency Medicine, MD',
                'duration_hours' => 10,
                'cpd_credits' => 10,
                'category' => 'Pediatrics',
                'level' => 'advanced',
                'status' => 'active',
                'price' => 349.99,
                'learning_objectives' => [
                    'Master pediatric resuscitation techniques',
                    'Understand age-specific emergency protocols',
                    'Apply family-centered care principles'
                ],
                'start_date' => now()->addDays(21),
                'end_date' => now()->addDays(51),
            ],
        ];

        foreach ($courses as $courseData) {
            $courseData['slug'] = \Illuminate\Support\Str::slug($courseData['title']);
            Course::updateOrCreate(
                ['slug' => $courseData['slug']],
                $courseData
            );
        }

        // Create sample live sessions
        $sessions = [
            [
                'title' => 'Advanced Cardiology Interactive Session',
                'description' => 'Interactive case studies and Q&A session on cardiac interventions.',
                'instructor' => 'Dr. Sarah Johnson',
                'instructor_title' => 'Cardiologist, MD',
                'session_date' => now()->addDays(3)->setTime(14, 0),
                'duration_minutes' => 90,
                'cpd_credits' => 2,
                'category' => 'Cardiology',
                'meeting_url' => 'https://zoom.us/j/example123',
                'meeting_id' => 'example123',
                'max_participants' => 50,
                'status' => 'scheduled',
                'materials' => [
                    'slides' => 'https://example.com/slides.pdf',
                    'handouts' => 'https://example.com/handouts.pdf'
                ],
            ],
            [
                'title' => 'Neurology Case Review',
                'description' => 'Review of complex neurological cases with expert analysis.',
                'instructor' => 'Dr. Michael Chen',
                'instructor_title' => 'Neurologist, MD, PhD',
                'session_date' => now()->addDays(5)->setTime(15, 30),
                'duration_minutes' => 60,
                'cpd_credits' => 1,
                'category' => 'Neurology',
                'meeting_url' => 'https://zoom.us/j/example456',
                'meeting_id' => 'example456',
                'max_participants' => 30,
                'status' => 'scheduled',
            ],
            [
                'title' => 'Pediatric Emergency Protocols',
                'description' => 'Live demonstration of pediatric emergency procedures.',
                'instructor' => 'Dr. Emily Rodriguez',
                'instructor_title' => 'Pediatric Emergency Medicine, MD',
                'session_date' => now()->addDays(7)->setTime(13, 0),
                'duration_minutes' => 120,
                'cpd_credits' => 3,
                'category' => 'Pediatrics',
                'meeting_url' => 'https://zoom.us/j/example789',
                'meeting_id' => 'example789',
                'max_participants' => 40,
                'status' => 'scheduled',
            ],
        ];

        foreach ($sessions as $sessionData) {
            LiveSession::updateOrCreate(
                ['title' => $sessionData['title']],
                $sessionData
            );
        }

        // Create carousel slides
        $slides = [
            [
                'title' => 'Advanced Continuing Professional Development',
                'subtitle' => 'Virtual Medical Education',
                'description' => 'Join thousands of healthcare professionals in our interactive virtual learning environment. Access cutting-edge medical education from anywhere, anytime.',
                'image' => 'img/carousel-1.jpg',
                'primary_button_text' => 'Learn More',
                'primary_button_link' => '/about',
                'secondary_button_text' => 'Join Live Session',
                'secondary_button_link' => '/booking',
                'order' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'Real-Time Medical Case Studies',
                'subtitle' => 'Interactive Learning Platform',
                'description' => 'Participate in live case discussions, interactive simulations, and collaborative learning sessions with leading medical experts worldwide.',
                'image' => 'img/carousel-2.jpg',
                'primary_button_text' => 'View Courses',
                'primary_button_link' => '/services',
                'secondary_button_text' => 'Get Support',
                'secondary_button_link' => '/contact',
                'order' => 2,
                'is_active' => true,
            ],
        ];

        foreach ($slides as $slideData) {
            CarouselSlide::create($slideData);
        }

        // Create services
        $services = [
            [
                'title' => 'Live Interactive Sessions',
                'description' => 'Join real-time educational sessions with medical experts from around the world.',
                'icon' => 'fas fa-video',
                'order' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'Self-Paced Courses',
                'description' => 'Access comprehensive medical courses that you can complete at your own pace.',
                'icon' => 'fas fa-graduation-cap',
                'order' => 2,
                'is_active' => true,
            ],
            [
                'title' => 'CPD Certification',
                'description' => 'Earn recognized CPD credits and certificates for your professional development.',
                'icon' => 'fas fa-certificate',
                'order' => 3,
                'is_active' => true,
            ],
            [
                'title' => '24/7 Support',
                'description' => 'Get help whenever you need it with our round-the-clock support team.',
                'icon' => 'fas fa-headset',
                'order' => 4,
                'is_active' => true,
            ],
        ];

        foreach ($services as $serviceData) {
            Service::create($serviceData);
        }

        // Create FAQs
        $faqs = [
            [
                'question' => 'How do I register for a live session?',
                'answer' => 'You can register for live sessions through your dashboard. Navigate to Live Sessions, find the session you want to attend, and click the Register button.',
                'category' => 'sessions',
                'order' => 1,
                'is_active' => true,
            ],
            [
                'question' => 'How are CPD credits calculated?',
                'answer' => 'CPD credits are awarded based on the duration and complexity of the educational content. Typically, 1 hour of learning equals 1 CPD credit.',
                'category' => 'certificates',
                'order' => 2,
                'is_active' => true,
            ],
            [
                'question' => 'Can I download my certificates?',
                'answer' => 'Yes, all certificates can be downloaded as PDF files from your Certificates section in the dashboard.',
                'category' => 'certificates',
                'order' => 3,
                'is_active' => true,
            ],
            [
                'question' => 'What technical requirements do I need?',
                'answer' => 'You need a stable internet connection, a modern web browser, and for live sessions, a microphone and camera are recommended.',
                'category' => 'technical',
                'order' => 4,
                'is_active' => true,
            ],
            [
                'question' => 'How do I access course materials?',
                'answer' => 'Course materials are available in your enrolled courses section. You can access videos, documents, and other resources anytime.',
                'category' => 'courses',
                'order' => 5,
                'is_active' => true,
            ],
        ];

        foreach ($faqs as $faqData) {
            FAQ::create($faqData);
        }

        // Create sample testimonials
        $testimonials = [
            [
                'client_name' => 'Dr. James Wilson',
                'client_position' => 'Cardiologist',
                'client_company' => 'City General Hospital',
                'testimonial' => 'The Virtual CME Hub has transformed my continuing education experience. The interactive sessions are incredibly valuable and the flexibility allows me to learn around my busy schedule.',
                'rating' => 5,
                'order' => 1,
                'is_active' => true,
            ],
            [
                'client_name' => 'Dr. Maria Garcia',
                'client_position' => 'Emergency Medicine Physician',
                'client_company' => 'Regional Medical Center',
                'testimonial' => 'Outstanding platform with high-quality content. The CPD credits are recognized by our medical board, making this an essential resource for my professional development.',
                'rating' => 5,
                'order' => 2,
                'is_active' => true,
            ],
            [
                'client_name' => 'Dr. Robert Chen',
                'client_position' => 'Pediatrician',
                'client_company' => 'Children\'s Hospital',
                'testimonial' => 'The pediatric emergency care course was exceptional. The instructors are world-class experts and the interactive format made complex topics easy to understand.',
                'rating' => 5,
                'order' => 3,
                'is_active' => true,
            ],
        ];

        foreach ($testimonials as $testimonialData) {
            Testimonial::create($testimonialData);
        }

        // Create sample team members
        $teamMembers = [
            [
                'name' => 'Dr. Sarah Johnson',
                'position' => 'Lead Cardiologist & Course Director',
                'bio' => 'Dr. Johnson is a renowned cardiologist with over 15 years of experience in interventional cardiology. She leads our cardiology education programs.',
                'image' => 'img/team-1.jpg',
                'email' => '<EMAIL>',
                'social_links' => [
                    'linkedin' => 'https://linkedin.com/in/sarahjohnson',
                    'twitter' => 'https://twitter.com/drsarahjohnson'
                ],
                'order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Dr. Michael Chen',
                'position' => 'Neurologist & Research Director',
                'bio' => 'Dr. Chen specializes in neurological disorders and leads our neurology research initiatives. He has published over 100 peer-reviewed papers.',
                'image' => 'img/team-2.jpg',
                'email' => '<EMAIL>',
                'social_links' => [
                    'linkedin' => 'https://linkedin.com/in/michaelchen',
                ],
                'order' => 2,
                'is_active' => true,
            ],
        ];

        foreach ($teamMembers as $memberData) {
            TeamMember::create($memberData);
        }

        $this->command->info('VCH sample data seeded successfully!');
    }
}
