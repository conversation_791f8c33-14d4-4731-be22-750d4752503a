@extends('layouts.dashboard')

@section('page-title', 'Create Service')

@section('content')
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Create Service</h2>
                    <p class="text-muted mb-0">Add a new educational service</p>
                </div>
                <div>
                    <a href="{{ route('admin.content.services.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Services
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <form action="{{ route('admin.content.services.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-graduation-cap me-2 text-primary"></i>Service Information</h5>
                    </div>
                    <div class="card-body">
                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Service Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Provide a concise description of the service (100-200 characters recommended)</div>
                        </div>

                        <!-- Icon -->
                        <div class="mb-3">
                            <label for="icon" class="form-label">Icon</label>
                            <div class="input-group">
                                <span class="input-group-text"><i id="iconPreview" class="fas fa-graduation-cap"></i></span>
                                <input type="text" class="form-control @error('icon') is-invalid @enderror" 
                                       id="icon" name="icon" value="{{ old('icon', 'fas fa-graduation-cap') }}" 
                                       placeholder="fas fa-icon-name">
                                <button class="btn btn-outline-secondary" type="button" id="iconPickerBtn">
                                    Choose Icon
                                </button>
                            </div>
                            @error('icon')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Enter a Font Awesome icon class (e.g., fas fa-graduation-cap)</div>
                        </div>

                        <!-- Image -->
                        <div class="mb-3">
                            <label for="image" class="form-label">Service Image</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Upload an image representing the service (optional). Recommended size: 600x400px</div>
                        </div>

                        <!-- Image Preview -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <img id="previewImg" src="" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                        </div>
                    </div>
                </div>

                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-white border-0">
                        <h5 class="mb-0"><i class="fas fa-cog me-2 text-primary"></i>Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Order -->
                                <div class="mb-3">
                                    <label for="order" class="form-label">Display Order <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('order') is-invalid @enderror" 
                                           id="order" name="order" value="{{ old('order', 0) }}" min="0" required>
                                    @error('order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Lower numbers appear first</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Status -->
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">Status</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active (service will be visible on the website)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.content.services.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Cancel
                    </a>
                    <div>
                        <button type="submit" name="action" value="save" class="btn btn-outline-primary me-2">
                            <i class="fas fa-save me-1"></i>Save Draft
                        </button>
                        <button type="submit" name="action" value="publish" class="btn btn-primary">
                            <i class="fas fa-check me-1"></i>Save & Publish
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Preview Panel -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm sticky-top" style="top: 2rem;">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-eye me-2 text-primary"></i>Live Preview</h5>
                </div>
                <div class="card-body">
                    <div id="servicePreview" class="text-center">
                        <div class="mb-4">
                            <div class="service-icon bg-primary text-white rounded-circle mx-auto mb-3" 
                                 style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-graduation-cap fa-2x"></i>
                            </div>
                            <h5 class="service-title">Service Title</h5>
                            <p class="text-muted service-description">Service description will appear here</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image preview
        document.getElementById('image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                    updatePreview();
                };
                reader.readAsDataURL(file);
            }
        });

        // Icon preview
        document.getElementById('icon').addEventListener('input', function() {
            updateIconPreview();
            updatePreview();
        });

        function updateIconPreview() {
            const iconClass = document.getElementById('icon').value;
            const iconPreview = document.getElementById('iconPreview');
            
            // Remove all classes and add the new one
            iconPreview.className = '';
            if (iconClass) {
                iconPreview.className = iconClass;
            } else {
                iconPreview.className = 'fas fa-graduation-cap';
            }
        }

        // Live preview update
        function updatePreview() {
            const title = document.getElementById('title').value || 'Service Title';
            const description = document.getElementById('description').value || 'Service description will appear here';
            const iconClass = document.getElementById('icon').value || 'fas fa-graduation-cap';
            
            document.querySelector('.service-title').textContent = title;
            document.querySelector('.service-description').textContent = description;
            
            const iconElement = document.querySelector('.service-icon i');
            iconElement.className = '';
            iconElement.className = iconClass + ' fa-2x';
        }

        // Form field listeners for live preview
        ['title', 'description'].forEach(id => {
            document.getElementById(id).addEventListener('input', updatePreview);
        });

        // Initialize preview
        updateIconPreview();
        updatePreview();

        // Form submission handling
        document.querySelector('form').addEventListener('submit', function(e) {
            const action = e.submitter.value;
            if (action === 'save') {
                document.getElementById('is_active').checked = false;
            } else if (action === 'publish') {
                document.getElementById('is_active').checked = true;
            }
        });

        // Icon picker functionality (simplified version)
        document.getElementById('iconPickerBtn').addEventListener('click', function() {
            // This is a simplified version. In a real implementation, you would show a modal with icon options
            alert('In a complete implementation, this would open an icon picker dialog.');
        });
    });
</script>
@endpush
@endsection