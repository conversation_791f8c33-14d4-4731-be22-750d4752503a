/* Dashboard Specific Styles - Enhanced Version */

/* Global Dashboard Variables */
:root {
    --dashboard-primary: #2E8B57;
    --dashboard-secondary: #FF6B35;
    --dashboard-success: #28a745;
    --dashboard-warning: #ffc107;
    --dashboard-info: #17a2b8;
    --dashboard-danger: #dc3545;
    --dashboard-light: #f8f9fa;
    --dashboard-dark: #2c3e50;
    --dashboard-border-radius: 12px;
    --dashboard-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --dashboard-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.12);
    --dashboard-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --dashboard-spacing: 1.5rem;
    --sidebar-width: 280px;
}

/* Ensure proper content spacing */
.main-content {
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
    min-height: 100vh;
    position: relative;
    z-index: 1;
}

/* Content area improvements */
.content-area {
    padding: 2rem;
    background: #f5f7fa;
    min-height: calc(100vh - 80px);
    position: relative;
    z-index: 1;
}

/* Prevent content from going under sidebar */
.dashboard-content-wrapper {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
}

/* Enhanced Dashboard Stats Cards */
.dashboard-stats {
    margin-bottom: var(--dashboard-spacing);
}

.stat-card {
    background: #fff;
    border-radius: var(--dashboard-border-radius);
    padding: 2rem;
    box-shadow: var(--dashboard-shadow);
    transition: var(--dashboard-transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--dashboard-primary), var(--dashboard-secondary));
    border-radius: 0 2px 2px 0;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--dashboard-shadow-hover);
    border-color: rgba(46, 139, 87, 0.2);
}

.stat-card.secondary::before {
    background: linear-gradient(180deg, var(--dashboard-secondary), var(--dashboard-warning));
}

.stat-card.success::before {
    background: linear-gradient(180deg, var(--dashboard-success), #20c997);
}

.stat-card.warning::before {
    background: linear-gradient(180deg, var(--dashboard-warning), #fd7e14);
}

.stat-card.info::before {
    background: linear-gradient(180deg, var(--dashboard-info), #6f42c1);
}

.stat-card .stat-icon {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, rgba(46, 139, 87, 0.1), rgba(46, 139, 87, 0.05));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    color: var(--dashboard-primary);
    transition: var(--dashboard-transition);
}

.stat-card:hover .stat-icon {
    transform: scale(1.1);
    background: linear-gradient(135deg, rgba(46, 139, 87, 0.15), rgba(46, 139, 87, 0.08));
}

.stat-card.secondary .stat-icon {
    color: var(--dashboard-secondary);
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));
}

.stat-card.success .stat-icon {
    color: var(--dashboard-success);
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
}

.stat-card.warning .stat-icon {
    color: var(--dashboard-warning);
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
}

.stat-card.info .stat-icon {
    color: var(--dashboard-info);
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
}

.stat-number {
    font-size: 2.75rem;
    font-weight: 800;
    color: var(--dashboard-dark);
    margin-bottom: 0.5rem;
    line-height: 1;
    letter-spacing: -0.02em;
}

.stat-label {
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
}

/* Enhanced Dashboard Cards */
.dashboard-card {
    background: #fff;
    border-radius: var(--dashboard-border-radius);
    box-shadow: var(--dashboard-shadow);
    margin-bottom: var(--dashboard-spacing);
    overflow: hidden;
    transition: var(--dashboard-transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--dashboard-shadow-hover);
    border-color: rgba(46, 139, 87, 0.1);
}

.dashboard-card-header {
    background: linear-gradient(135deg, var(--dashboard-primary), var(--dashboard-secondary));
    color: white;
    padding: 1.5rem 2rem;
    border-bottom: none;
    position: relative;
    overflow: hidden;
}

.dashboard-card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="70" r="1" fill="rgba(255,255,255,0.06)"/></svg>');
    opacity: 0.3;
}

.dashboard-card-header h5 {
    margin: 0;
    font-weight: 700;
    font-size: 1.1rem;
    position: relative;
    z-index: 1;
}

.dashboard-card-header i {
    margin-right: 0.75rem;
    font-size: 1.2rem;
}

.dashboard-card-body {
    padding: 2rem;
}

/* Enhanced Service Management Cards */
.service-management-card {
    background: #fff;
    border-radius: var(--dashboard-border-radius);
    padding: 2rem;
    box-shadow: var(--dashboard-shadow);
    transition: var(--dashboard-transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: var(--dashboard-spacing);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.service-management-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--dashboard-shadow-hover);
    border-color: rgba(46, 139, 87, 0.2);
}

.service-icon {
    width: 88px;
    height: 88px;
    background: linear-gradient(135deg, rgba(46, 139, 87, 0.1), rgba(46, 139, 87, 0.05));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.2rem;
    color: var(--dashboard-primary);
    margin-bottom: 1.5rem;
    transition: var(--dashboard-transition);
    position: relative;
    overflow: hidden;
}

.service-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: var(--dashboard-transition);
    opacity: 0;
}

.service-management-card:hover .service-icon {
    transform: scale(1.1);
    background: linear-gradient(135deg, rgba(46, 139, 87, 0.15), rgba(46, 139, 87, 0.08));
}

.service-management-card:hover .service-icon::before {
    opacity: 1;
    animation: shimmer 0.6s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Enhanced Quick Actions */
.quick-actions {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: var(--dashboard-border-radius);
    padding: 2rem;
    margin-bottom: var(--dashboard-spacing);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.quick-action-btn {
    background: #fff;
    border: 2px solid transparent;
    color: var(--dashboard-dark);
    padding: 1.25rem 1.5rem;
    border-radius: var(--dashboard-border-radius);
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    transition: var(--dashboard-transition);
    font-weight: 600;
    font-size: 0.875rem;
    height: 120px;
    box-shadow: var(--dashboard-shadow);
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--dashboard-primary), var(--dashboard-secondary));
    transform: scaleX(0);
    transition: var(--dashboard-transition);
}

.quick-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--dashboard-shadow-hover);
    border-color: rgba(46, 139, 87, 0.2);
    color: var(--dashboard-primary);
}

.quick-action-btn:hover::before {
    transform: scaleX(1);
}

.quick-action-btn i {
    font-size: 2rem;
    margin-bottom: 0.75rem;
    transition: var(--dashboard-transition);
}

.quick-action-btn:hover i {
    transform: scale(1.1);
    color: var(--dashboard-primary);
}

.quick-action-btn span {
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.2;
}

/* Quick Action Color Variants */
.quick-action-btn.btn-outline-primary:hover {
    color: var(--dashboard-primary);
}

.quick-action-btn.btn-outline-success:hover {
    color: var(--dashboard-success);
}

.quick-action-btn.btn-outline-warning:hover {
    color: var(--dashboard-warning);
}

.quick-action-btn.btn-outline-info:hover {
    color: var(--dashboard-info);
}

.quick-action-btn.btn-outline-secondary:hover {
    color: var(--dashboard-secondary);
}

.quick-action-btn.btn-outline-dark:hover {
    color: var(--dashboard-dark);
}

/* Enhanced Recent Activities */
.activity-item {
    padding: 1.25rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: flex-start;
    transition: var(--dashboard-transition);
    position: relative;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background: rgba(46, 139, 87, 0.02);
    border-radius: 8px;
    margin: 0 -1rem;
    padding-left: 1rem;
    padding-right: 1rem;
}

.activity-icon {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, rgba(46, 139, 87, 0.1), rgba(46, 139, 87, 0.05));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: var(--dashboard-primary);
    font-size: 1.1rem;
    flex-shrink: 0;
    transition: var(--dashboard-transition);
}

.activity-item:hover .activity-icon {
    transform: scale(1.05);
    background: linear-gradient(135deg, rgba(46, 139, 87, 0.15), rgba(46, 139, 87, 0.08));
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-weight: 600;
    color: var(--dashboard-dark);
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
    line-height: 1.4;
}

.activity-description {
    color: #6c757d;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.activity-time {
    color: #9ca3af;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Activity Icon Color Variants */
.activity-icon.success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
    color: var(--dashboard-success);
}

.activity-icon.warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
    color: var(--dashboard-warning);
}

.activity-icon.info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
    color: var(--dashboard-info);
}

/* Enhanced Dashboard Tables */
.dashboard-table {
    background: #fff;
    border-radius: var(--dashboard-border-radius);
    overflow: hidden;
    box-shadow: var(--dashboard-shadow);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.dashboard-table .table {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.dashboard-table .table thead th {
    background: linear-gradient(135deg, var(--dashboard-primary), var(--dashboard-secondary));
    color: white;
    border: none;
    padding: 1.25rem 1rem;
    font-weight: 700;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.dashboard-table .table thead th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), transparent);
}

.dashboard-table .table tbody td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
    border-color: rgba(0, 0, 0, 0.08);
    transition: var(--dashboard-transition);
}

.dashboard-table .table tbody tr {
    transition: var(--dashboard-transition);
}

.dashboard-table .table tbody tr:hover {
    background-color: rgba(46, 139, 87, 0.02);
    transform: scale(1.001);
}

.dashboard-table .table tbody tr:hover td {
    border-color: rgba(46, 139, 87, 0.1);
}

/* Enhanced Table Elements */
.table .badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
}

.table .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    border-radius: 6px;
    transition: var(--dashboard-transition);
}

.table .btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid transparent;
    transition: var(--dashboard-transition);
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.status-badge::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
}

.status-badge.pending {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border-color: rgba(133, 100, 4, 0.2);
}

.status-badge.pending::before {
    background: #856404;
}

.status-badge.completed {
    background: linear-gradient(135deg, #d4edda, #a8e6cf);
    color: #155724;
    border-color: rgba(21, 87, 36, 0.2);
}

.status-badge.completed::before {
    background: #155724;
}

.status-badge.in-progress {
    background: linear-gradient(135deg, #cce7ff, #a8d8ff);
    color: #004085;
    border-color: rgba(0, 64, 133, 0.2);
}

.status-badge.in-progress::before {
    background: #004085;
    animation: pulse 2s infinite;
}

.status-badge.cancelled {
    background: linear-gradient(135deg, #f8d7da, #ffb3ba);
    color: #721c24;
    border-color: rgba(114, 28, 36, 0.2);
}

.status-badge.cancelled::before {
    background: #721c24;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .stat-card {
        margin-bottom: 1.25rem;
    }

    .quick-action-btn {
        height: 110px;
        padding: 1rem;
    }

    .quick-action-btn i {
        font-size: 1.8rem;
    }
}

@media (max-width: 992px) {
    .dashboard-card-body {
        padding: 1.75rem;
    }

    .stat-card {
        padding: 1.75rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    :root {
        --dashboard-spacing: 1.25rem;
    }

    .stat-card {
        margin-bottom: 1rem;
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2.25rem;
    }

    .stat-card .stat-icon {
        width: 48px;
        height: 48px;
        font-size: 18px;
        top: 1.25rem;
        right: 1.25rem;
    }

    .quick-action-btn {
        height: 100px;
        margin-bottom: 0.75rem;
        font-size: 0.8rem;
    }

    .quick-action-btn i {
        font-size: 1.6rem;
        margin-bottom: 0.5rem;
    }

    .dashboard-card-body {
        padding: 1.5rem;
    }

    .dashboard-table .table thead th,
    .dashboard-table .table tbody td {
        padding: 1rem 0.75rem;
        font-size: 0.85rem;
    }

    .activity-item {
        padding: 1rem 0;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .content-area {
        padding: 1.5rem;
        min-height: calc(100vh - 70px);
    }
}

@media (max-width: 576px) {
    .stat-card {
        padding: 1.25rem;
        text-align: center;
    }

    .stat-card .stat-icon {
        position: static;
        margin: 0 auto 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .quick-action-btn {
        height: 90px;
        padding: 0.75rem;
    }

    .quick-action-btn i {
        font-size: 1.4rem;
    }

    .dashboard-card-body {
        padding: 1.25rem;
    }

    .content-area {
        padding: 1rem;
        min-height: calc(100vh - 60px);
    }

    .activity-item {
        padding: 0.75rem 0;
    }

    .activity-icon {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }
}

/* Dashboard Welcome Section */
.dashboard-welcome {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    border-radius: 15px;
    padding: 3rem 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.dashboard-welcome::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateX(-100px) translateY(-100px); }
    100% { transform: translateX(100px) translateY(100px); }
}

.dashboard-welcome h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.dashboard-welcome p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Chart Container */
.chart-container {
    background: #fff;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.chart-container h5 {
    color: var(--dark);
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--light);
}
