<?php

namespace App\View\Components;

use Illuminate\View\Component;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

class Navigation extends Component
{
    public $items;
    public $currentRoute;
    
    public function __construct()
    {
        $this->currentRoute = Route::currentRouteName();
        $this->items = $this->getNavigationItems();
    }

    public function render()
    {
        return view('components.navigation');
    }

    private function getNavigationItems()
    {
        $items = [
            'main' => [
                'title' => 'Main',
                'items' => [
                    [
                        'text' => 'Dashboard',
                        'route' => 'dashboard',
                        'icon' => 'fas fa-tachometer-alt',
                        'active' => $this->isActive(['dashboard']),
                    ],
                    [
                        'text' => 'View Website',
                        'route' => 'home',
                        'icon' => 'fas fa-globe',
                        'active' => false,
                        'target' => '_blank',
                    ],
                ]
            ],
            'learning' => [
                'title' => 'Learning',
                'items' => [
                    [
                        'text' => 'Live Sessions',
                        'route' => 'dashboard.live-sessions.index',
                        'icon' => 'fas fa-video',
                        'active' => $this->isActive(['dashboard.live-sessions.*']),
                        'badge' => $this->getUpcomingSessionsCount(),
                    ],
                    [
                        'text' => 'My Courses',
                        'route' => 'dashboard.courses.index',
                        'icon' => 'fas fa-graduation-cap',
                        'active' => $this->isActive(['dashboard.courses.*']),
                        'badge' => $this->getInProgressCoursesCount(),
                    ],
                    [
                        'text' => 'Certifications',
                        'route' => 'dashboard.certificates.index',
                        'icon' => 'fas fa-certificate',
                        'active' => $this->isActive(['dashboard.certificates.index', 'dashboard.certificates.show']),
                    ],
                    [
                        'text' => 'CPD Credits',
                        'route' => 'dashboard.certificates.cpd',
                        'icon' => 'fas fa-clock',
                        'active' => $this->isActive(['dashboard.certificates.cpd']),
                    ],
                ]
            ],
            'account' => [
                'title' => 'Account',
                'items' => [
                    [
                        'text' => 'Profile Settings',
                        'route' => 'profile.edit',
                        'icon' => 'fas fa-user-edit',
                        'active' => $this->isActive(['profile.*']),
                    ],
                    [
                        'text' => 'Notifications',
                        'route' => 'dashboard.notifications.index',
                        'icon' => 'fas fa-bell',
                        'active' => $this->isActive(['dashboard.notifications.*']),
                        'badge' => $this->getUnreadNotificationsCount(),
                    ],
                    [
                        'text' => 'Billing',
                        'route' => 'dashboard.billing.index',
                        'icon' => 'fas fa-credit-card',
                        'active' => $this->isActive(['dashboard.billing.*']),
                    ],
                ]
            ],
            'support' => [
                'title' => 'Support',
                'items' => [
                    [
                        'text' => 'Help Center',
                        'route' => 'dashboard.support.help',
                        'icon' => 'fas fa-question-circle',
                        'active' => $this->isActive(['dashboard.support.help']),
                    ],
                    [
                        'text' => 'Contact Support',
                        'route' => 'dashboard.support.contact',
                        'icon' => 'fas fa-headset',
                        'active' => $this->isActive(['dashboard.support.contact']),
                    ],
                    [
                        'text' => 'My Tickets',
                        'route' => 'dashboard.support.tickets',
                        'icon' => 'fas fa-ticket-alt',
                        'active' => $this->isActive(['dashboard.support.tickets*']),
                        'badge' => $this->getOpenTicketsCount(),
                    ],
                ]
            ],
        ];

        // Add admin section if user is admin
        if (Auth::check() && Auth::user()->isAdmin()) {
            $items['admin'] = [
                'title' => 'Administration',
                'items' => [
                    [
                        'text' => 'Content Management',
                        'route' => 'admin.content.index',
                        'icon' => 'fas fa-edit',
                        'active' => $this->isActive(['admin.content.*']),
                    ],
                    [
                        'text' => 'User Management',
                        'route' => 'admin.users.index',
                        'icon' => 'fas fa-users',
                        'active' => $this->isActive(['admin.users.*']),
                    ],
                    [
                        'text' => 'Course Management',
                        'route' => 'dashboard', // TODO: Create admin.courses.index
                        'icon' => 'fas fa-graduation-cap',
                        'active' => false,
                    ],
                    [
                        'text' => 'Session Management',
                        'route' => 'admin.sessions.index',
                        'icon' => 'fas fa-video',
                        'active' => $this->isActive(['admin.sessions.*']),
                    ],
                    [
                        'text' => 'Certificate Management',
                        'route' => 'dashboard', // TODO: Create admin.certificates.index
                        'icon' => 'fas fa-certificate',
                        'active' => false,
                    ],
                    [
                        'text' => 'Support Management',
                        'route' => 'dashboard', // TODO: Create admin.support.index
                        'icon' => 'fas fa-headset',
                        'active' => false,
                    ],
                    [
                        'text' => 'Analytics & Reports',
                        'route' => 'admin.analytics.index',
                        'icon' => 'fas fa-chart-bar',
                        'active' => $this->isActive(['admin.analytics.*']),
                    ],
                    [
                        'text' => 'System Settings',
                        'route' => 'admin.settings.index',
                        'icon' => 'fas fa-cog',
                        'active' => $this->isActive(['admin.settings.*']),
                    ],
                ]
            ];
        }

        return $items;
    }

    private function isActive($routes)
    {
        foreach ($routes as $route) {
            if (str_contains($route, '*')) {
                $pattern = str_replace('*', '', $route);
                if (str_starts_with($this->currentRoute, $pattern)) {
                    return true;
                }
            } else {
                if ($this->currentRoute === $route) {
                    return true;
                }
            }
        }
        return false;
    }

    private function getUpcomingSessionsCount()
    {
        // In a real implementation, this would query the database
        // for upcoming sessions the user is registered for
        return null; // Return null to hide badge, or a number to show it
    }

    private function getInProgressCoursesCount()
    {
        // In a real implementation, this would query the database
        // for courses the user is currently taking
        return null;
    }

    private function getUnreadNotificationsCount()
    {
        // In a real implementation, this would query the database
        // for unread notifications
        return 3; // Example count
    }

    private function getOpenTicketsCount()
    {
        // In a real implementation, this would query the database
        // for open support tickets
        return null;
    }
}
