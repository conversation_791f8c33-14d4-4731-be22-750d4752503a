<?php $__env->startSection('title', 'Content Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="admin-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Content Management</h2>
                    <p class="text-muted mb-0">Manage website content and media</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-sync me-2"></i>Refresh Stats
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-images text-primary fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-primary mb-1"><?php echo e($stats['carousel_slides']); ?></h3>
                    <p class="text-muted mb-0 small">Carousel Slides</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-cogs text-success fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-success mb-1"><?php echo e($stats['services']); ?></h3>
                    <p class="text-muted mb-0 small">Services</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-users text-info fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-info mb-1"><?php echo e($stats['team_members']); ?></h3>
                    <p class="text-muted mb-0 small">Team Members</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-quote-left text-warning fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-warning mb-1"><?php echo e($stats['testimonials']); ?></h3>
                    <p class="text-muted mb-0 small">Testimonials</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-secondary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-graduation-cap text-secondary fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-secondary mb-1"><?php echo e($stats['courses']); ?></h3>
                    <p class="text-muted mb-0 small">Courses</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-danger bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-video text-danger fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-danger mb-1"><?php echo e($stats['live_sessions']); ?></h3>
                    <p class="text-muted mb-0 small">Live Sessions</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Management Sections -->
    <div class="row">
        <!-- Carousel Management -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-images me-2 text-primary"></i>Carousel Slides
                        </h5>
                        <a href="<?php echo e(route('admin.content.carousel.create')); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Add Slide
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Manage homepage carousel slides and banners.</p>
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.content.carousel.index')); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>Manage Carousel Slides
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Services Management -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2 text-success"></i>Services
                        </h5>
                        <a href="<?php echo e(route('admin.content.services.create')); ?>" class="btn btn-success btn-sm">
                            <i class="fas fa-plus me-1"></i>Add Service
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Manage services offered by your organization.</p>
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.content.services.index')); ?>" class="btn btn-outline-success">
                            <i class="fas fa-list me-2"></i>Manage Services
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Team Management -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2 text-info"></i>Team Members
                        </h5>
                        <a href="<?php echo e(route('admin.content.team.create')); ?>" class="btn btn-info btn-sm">
                            <i class="fas fa-plus me-1"></i>Add Member
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Manage team member profiles and information.</p>
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.content.team.index')); ?>" class="btn btn-outline-info">
                            <i class="fas fa-list me-2"></i>Manage Team Members
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testimonials Management -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-quote-left me-2 text-warning"></i>Testimonials
                        </h5>
                        <a href="<?php echo e(route('admin.content.testimonials.create')); ?>" class="btn btn-warning btn-sm">
                            <i class="fas fa-plus me-1"></i>Add Testimonial
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Manage client testimonials and reviews.</p>
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.content.testimonials.index')); ?>" class="btn btn-outline-warning">
                            <i class="fas fa-list me-2"></i>Manage Testimonials
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2 text-primary"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <button class="btn btn-outline-primary" onclick="bulkActivate()">
                                    <i class="fas fa-eye me-2"></i>Bulk Activate
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <button class="btn btn-outline-warning" onclick="bulkDeactivate()">
                                    <i class="fas fa-eye-slash me-2"></i>Bulk Deactivate
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <button class="btn btn-outline-info" onclick="exportContent()">
                                    <i class="fas fa-download me-2"></i>Export Content
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <button class="btn btn-outline-secondary" onclick="clearCache()">
                                    <i class="fas fa-broom me-2"></i>Clear Cache
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-primary"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Activity tracking coming soon</h6>
                        <p class="text-muted">Recent content changes and updates will be displayed here.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function bulkActivate() {
    alert('Bulk activation feature would be implemented here.');
}

function bulkDeactivate() {
    alert('Bulk deactivation feature would be implemented here.');
}

function exportContent() {
    alert('Content export feature would be implemented here.');
}

function clearCache() {
    if (confirm('Are you sure you want to clear the cache?')) {
        alert('Cache clearing feature would be implemented here.');
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\VCH\resources\views/admin/content/index.blade.php ENDPATH**/ ?>