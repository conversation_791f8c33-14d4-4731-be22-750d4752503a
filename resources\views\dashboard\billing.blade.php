@extends('layouts.dashboard')

@section('page-title', 'Billing & Subscription')

@section('content')
<div class="dashboard-content-wrapper">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">Billing & Subscription</h2>
                    <p class="text-muted mb-0">Manage your subscription and payment information</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('dashboard.billing.history') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-history me-2"></i>View History
                    </a>
                    <a href="{{ route('dashboard.support.contact') }}" class="btn btn-outline-primary">
                        <i class="fas fa-headset me-2"></i>Billing Support
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Plan -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                                    <i class="fas fa-crown text-primary fa-2x"></i>
                                </div>
                                <div>
                                    <h4 class="mb-1">{{ $stats['current_plan'] }} Plan</h4>
                                    <p class="text-muted mb-0">
                                        ${{ number_format($stats['monthly_cost'], 2) }}/month • 
                                        Next billing: {{ $stats['next_billing_date']->format('M j, Y') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group">
                                <button class="btn btn-outline-primary">
                                    <i class="fas fa-edit me-1"></i>Change Plan
                                </button>
                                <button class="btn btn-outline-danger">
                                    <i class="fas fa-times me-1"></i>Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-dollar-sign text-success fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-success mb-1">${{ number_format($stats['total_spent'], 2) }}</h3>
                    <p class="text-muted mb-0">Total Spent</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-calendar-alt text-primary fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-primary mb-1">{{ $stats['next_billing_date']->diffInDays(now()) }}</h3>
                    <p class="text-muted mb-0">Days Until Next Bill</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-receipt text-info fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="text-info mb-1">{{ count($invoices) }}</h3>
                    <p class="text-muted mb-0">Total Invoices</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Payment Methods -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-credit-card me-2 text-primary"></i>Payment Methods</h5>
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Add Method
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    @forelse($paymentMethods as $method)
                    <div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                @if($method['brand'] === 'Visa')
                                    <i class="fab fa-cc-visa fa-2x text-primary"></i>
                                @elseif($method['brand'] === 'Mastercard')
                                    <i class="fab fa-cc-mastercard fa-2x text-warning"></i>
                                @else
                                    <i class="fas fa-credit-card fa-2x text-secondary"></i>
                                @endif
                            </div>
                            <div>
                                <h6 class="mb-1">{{ $method['brand'] }} •••• {{ $method['last_four'] }}</h6>
                                <small class="text-muted">Expires {{ $method['expires'] }}</small>
                                @if($method['is_default'])
                                    <span class="badge bg-success ms-2">Default</span>
                                @endif
                            </div>
                        </div>
                        <div class="btn-group btn-group-sm">
                            @if(!$method['is_default'])
                                <button class="btn btn-outline-primary">Make Default</button>
                            @endif
                            <button class="btn btn-outline-danger">Remove</button>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-4">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No payment methods added</h6>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Add Payment Method
                        </button>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Billing History -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-history me-2 text-primary"></i>Billing History</h5>
                        <a href="#" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-download me-1"></i>Download All
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @forelse($invoices as $invoice)
                    <div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3">
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">{{ $invoice['id'] }}</h6>
                                <span class="badge bg-{{ $invoice['status'] === 'paid' ? 'success' : 'warning' }}">
                                    {{ ucfirst($invoice['status']) }}
                                </span>
                            </div>
                            <p class="text-muted mb-1">{{ $invoice['description'] }}</p>
                            <small class="text-muted">{{ \Carbon\Carbon::parse($invoice['date'])->format('M j, Y') }}</small>
                        </div>
                        <div class="text-end ms-3">
                            <h6 class="mb-2">${{ number_format($invoice['amount'], 2) }}</h6>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-secondary">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-4">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No billing history</h6>
                        <p class="text-muted">Your invoices will appear here once you start your subscription.</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Plan Comparison -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0"><i class="fas fa-layer-group me-2 text-primary"></i>Available Plans</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border h-100">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Basic</h5>
                                    <h2 class="text-primary">$19<small class="text-muted">/month</small></h2>
                                    <ul class="list-unstyled mt-3 mb-4">
                                        <li><i class="fas fa-check text-success me-2"></i>5 Courses per month</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Basic support</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Certificate downloads</li>
                                        <li><i class="fas fa-times text-muted me-2"></i>Live sessions</li>
                                    </ul>
                                    <button class="btn btn-outline-primary">Select Plan</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary h-100">
                                <div class="card-header bg-primary text-white text-center">
                                    <span class="badge bg-warning text-dark">Current Plan</span>
                                </div>
                                <div class="card-body text-center">
                                    <h5 class="card-title">Professional</h5>
                                    <h2 class="text-primary">$29<small class="text-muted">/month</small></h2>
                                    <ul class="list-unstyled mt-3 mb-4">
                                        <li><i class="fas fa-check text-success me-2"></i>Unlimited courses</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Priority support</li>
                                        <li><i class="fas fa-check text-success me-2"></i>All certificates</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Live sessions</li>
                                    </ul>
                                    <button class="btn btn-primary" disabled>Current Plan</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border h-100">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Enterprise</h5>
                                    <h2 class="text-primary">$49<small class="text-muted">/month</small></h2>
                                    <ul class="list-unstyled mt-3 mb-4">
                                        <li><i class="fas fa-check text-success me-2"></i>Everything in Pro</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Custom content</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Team management</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Analytics</li>
                                    </ul>
                                    <button class="btn btn-outline-primary">Upgrade</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add billing-specific JavaScript here
    console.log('Billing page loaded');
});
</script>
@endpush
