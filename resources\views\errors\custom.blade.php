@extends('layouts.app')

@section('title', 'Error ' . $status)

@section('content')
<div class="container">
    <div class="row justify-content-center min-vh-100 align-items-center">
        <div class="col-lg-6 col-md-8">
            <div class="text-center">
                <!-- Error Icon -->
                <div class="mb-4">
                    @if($status == 404)
                        <i class="fas fa-search fa-5x text-muted mb-3"></i>
                    @elseif($status == 403)
                        <i class="fas fa-lock fa-5x text-warning mb-3"></i>
                    @elseif($status == 500)
                        <i class="fas fa-exclamation-triangle fa-5x text-danger mb-3"></i>
                    @else
                        <i class="fas fa-question-circle fa-5x text-muted mb-3"></i>
                    @endif
                </div>

                <!-- Error Code -->
                <h1 class="display-1 fw-bold text-primary mb-3">{{ $status }}</h1>

                <!-- Error Title -->
                <h2 class="h3 mb-3">
                    @switch($status)
                        @case(404)
                            Page Not Found
                            @break
                        @case(403)
                            Access Forbidden
                            @break
                        @case(500)
                            Server Error
                            @break
                        @case(503)
                            Service Unavailable
                            @break
                        @default
                            Something Went Wrong
                    @endswitch
                </h2>

                <!-- Error Message -->
                <p class="text-muted mb-4 lead">
                    @switch($status)
                        @case(404)
                            The page you're looking for doesn't exist or has been moved.
                            @break
                        @case(403)
                            You don't have permission to access this resource.
                            @break
                        @case(500)
                            We're experiencing technical difficulties. Our team has been notified.
                            @break
                        @case(503)
                            The service is temporarily unavailable. Please try again later.
                            @break
                        @default
                            {{ $message ?? 'An unexpected error occurred.' }}
                    @endswitch
                </p>

                <!-- Debug Information (Development Only) -->
                @if(config('app.debug') && isset($exception))
                    <div class="alert alert-warning text-start mb-4">
                        <h6><i class="fas fa-bug me-2"></i>Debug Information</h6>
                        <p class="mb-2"><strong>Exception:</strong> {{ get_class($exception) }}</p>
                        <p class="mb-2"><strong>Message:</strong> {{ $exception->getMessage() }}</p>
                        <p class="mb-2"><strong>File:</strong> {{ $exception->getFile() }}:{{ $exception->getLine() }}</p>
                        
                        <details class="mt-3">
                            <summary class="btn btn-sm btn-outline-secondary">View Stack Trace</summary>
                            <pre class="mt-2 p-2 bg-light rounded small">{{ $exception->getTraceAsString() }}</pre>
                        </details>
                    </div>
                @endif

                <!-- Action Buttons -->
                <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                    <a href="{{ url()->previous() }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>Go Back
                    </a>
                    
                    @auth
                        <a href="{{ route('dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    @else
                        <a href="{{ route('home') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>Home
                        </a>
                    @endauth
                    
                    @if($status == 500 || $status == 503)
                        <button onclick="location.reload()" class="btn btn-outline-secondary">
                            <i class="fas fa-sync me-2"></i>Try Again
                        </button>
                    @endif
                </div>

                <!-- Additional Help -->
                <div class="mt-5 pt-4 border-top">
                    <p class="text-muted small mb-3">Need help? Contact our support team</p>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="{{ route('dashboard.support.contact') }}" class="text-decoration-none">
                            <i class="fas fa-headset me-1"></i>Support
                        </a>
                        <a href="{{ route('dashboard.support.help') }}" class="text-decoration-none">
                            <i class="fas fa-question-circle me-1"></i>Help Center
                        </a>
                        <a href="mailto:<EMAIL>" class="text-decoration-none">
                            <i class="fas fa-envelope me-1"></i>Email
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-retry for 503 errors
    @if($status == 503)
        let retryCount = 0;
        const maxRetries = 3;
        const retryDelay = 5000; // 5 seconds
        
        function autoRetry() {
            if (retryCount < maxRetries) {
                retryCount++;
                setTimeout(() => {
                    console.log(`Auto-retry attempt ${retryCount}/${maxRetries}`);
                    location.reload();
                }, retryDelay * retryCount);
            }
        }
        
        // Start auto-retry after 10 seconds
        setTimeout(autoRetry, 10000);
    @endif
    
    // Track error for analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', 'exception', {
            'description': 'Error {{ $status }}: {{ $message ?? "Unknown error" }}',
            'fatal': {{ $status >= 500 ? 'true' : 'false' }}
        });
    }
    
    // Report error to monitoring service (if configured)
    if (window.Sentry) {
        Sentry.captureMessage('Error {{ $status }}: {{ $message ?? "Unknown error" }}', 'error');
    }
});
</script>
@endpush

@push('styles')
<style>
.min-vh-100 {
    min-height: 100vh;
}

.display-1 {
    font-size: 6rem;
    font-weight: 300;
    line-height: 1.2;
}

@media (max-width: 576px) {
    .display-1 {
        font-size: 4rem;
    }
    
    .fa-5x {
        font-size: 3em !important;
    }
}

.btn {
    min-width: 120px;
}

details summary {
    cursor: pointer;
    user-select: none;
}

details summary::-webkit-details-marker {
    display: none;
}

details summary::before {
    content: '▶';
    margin-right: 0.5rem;
    transition: transform 0.2s;
}

details[open] summary::before {
    transform: rotate(90deg);
}

pre {
    max-height: 300px;
    overflow-y: auto;
    font-size: 0.8rem;
    line-height: 1.4;
}

.border-top {
    border-top: 1px solid #dee2e6 !important;
}
</style>
@endpush
@endsection
